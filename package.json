{"name": "web-pay-unique_common", "version": "0.1.0", "private": true, "scripts": {"serve:release": "NODE_ENV=development vue-cli-service serve --mode release", "serve:master": "NODE_ENV=development vue-cli-service serve --mode master", "build:release": "vue-cli-service build --mode release", "build:master": "vue-cli-service build --mode master", "build:online": "vue-cli-service build --mode online", "lint": "vue-cli-service lint", "build:all": "npm run build:online & npm run build:release"}, "dependencies": {"@adyen/adyen-web": "5.12.0", "@airwallex/components-sdk": "^1.19.0", "@checkout.com/checkout-web-components": "^0.1.0-beta", "@ufe/reporter": "^0.1.20", "axios": "^0.26.0", "core-js": "^3.6.5", "crypto-js": "^4.2.0", "current-device": "^0.10.2", "swiper": "5.4.1", "ua-parser-js": "^1.0.37", "vue": "^2.6.11", "vue-awesome-swiper": "4.1.1", "vue-gtag": "^1.16.1", "vue-i18n": "^8.26.1", "vue-infinite-scroll": "^2.0.2", "vue-lazyload": "1.3.4", "vue-router": "^3.2.0", "vuejs-paginate": "^2.1.0", "vuex": "^3.4.0"}, "devDependencies": {"@vue/cli-plugin-babel": "~4.5.15", "@vue/cli-plugin-eslint": "~4.5.15", "@vue/cli-plugin-router": "~4.5.15", "@vue/cli-plugin-vuex": "~4.5.15", "@vue/cli-service": "~4.5.15", "@vue/eslint-config-standard": "^5.1.2", "babel-eslint": "^10.1.0", "copy-webpack-plugin": "^5.1.0", "eslint": "^6.7.2", "eslint-plugin-import": "^2.20.2", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^4.0.0", "eslint-plugin-vue": "^6.2.2", "node-xlsx": "^0.21.0", "postcss-pxtorem": "5.1.1", "sass": "^1.26.5", "sass-loader": "^8.0.2", "vue-template-compiler": "^2.6.11", "webpack-bundle-analyzer": "^4.10.2"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/essential", "@vue/standard"], "parserOptions": {"parser": "babel-es<PERSON>"}, "rules": {}}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}