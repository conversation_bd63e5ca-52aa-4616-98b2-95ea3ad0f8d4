const CopyWebpackPlugin = require('copy-webpack-plugin')
const path = require('path')
const AllPageConfig = require('./config/metaConfig.js')
const BundleAnalyzerPlugin = require('webpack-bundle-analyzer').BundleAnalyzerPlugin

const GAME = process.env.VUE_APP_GAME
const isDev = process.env.NODE_ENV === 'development'
const timeMark = getDateMark()

const config = {
  publicPath: '/', // 基本路径
  lintOnSave: false,
  configureWebpack: {
    devtool: process.env.ENV === 'development' ? 'source-map' : undefined,
    resolve: {
      alias: {
        '@': path.resolve(__dirname, 'src')
      }
      // extensions: ['.js', 'css', 'scss', '.vue']
    },
    plugins: [
      new CopyWebpackPlugin([
        { from: './robots.txt' },
        { from: './config/pwa', to: 'config' },
        { from: './config/service-worker.js' }
      ]),
      // new BundleAnalyzerPlugin()
    ],
    optimization: {
      splitChunks: {
        cacheGroups: {
          // i18nJson: {
          //   name: 'chunk-i18n-json',
          //   test: /[\\/]langHelper[\\/]/,
          //   priority: 0,
          //   chunks: 'async',
          //   enforce: true
          // },
          tool: {
            name: 'chunk-tool',
            test: /swiper/,
            priority: 1,
            chunks: 'async'
          }
        }
      }
    }
  },
  pages: {},
  chainWebpack: config => {
    // config.plugins.delete('preload-dc')
    // config.plugins.delete('prefetch-dc')

    config.module
      .rule('images')
      .test(/\.(png|jpe?g|gif|svg)(\?.*)?$/)
      .use('url-loader')
      .loader('url-loader')
      .options({
        limit: 1024 * 2, // 设置为你想要的阈值，单位是字节（例如 8KiB）
        name: `static/${timeMark}/img/[name].[hash:8].[ext]`
      })
  }
}

updatePageConfig(AllPageConfig)

try {
  const scriptParams = process.env.npm_lifecycle_event
  const [action, env] = scriptParams.split(':') || []

  if (action === 'build') {
    // config.css.extract = {
    //   filename: 'static/css/[name].[chunkhash:8].css',
    //   chunkFilename: 'static/css/[name].[chunkhash:8].css'
    // }

    config.outputDir = 'dist_' + env
    if (env === 'online') {
      Object.assign(config, {
        publicPath: process.env.onlinePublicGlobalPath,
        outputDir: 'dist_online',
        assetsDir: `static/${timeMark}`,
        indexPath: 'index.html', // html 的输出路径

        productionSourceMap: false, // 是否为生产环境构建生成 source map？
        crossorigin: 'anonymous'
      })
    }
  }
} catch (e) {
  console.log(e)
}

function risePage (curGame, PageInfo) {
  // const PageInfo = AllPageConfig[curGame]
  const isOnline = process.env.VUE_APP_PROD_ENV === 'ONLINE'

  const isBuild = process.env.NODE_ENV === 'production'
  let resBathPath = '/'
  if (isBuild && PageInfo.linkMainDomain) resBathPath = `/${PageInfo.linkPathName}/`
  if (isOnline) {
    resBathPath = curGame.includes('cn') ? process.env.onlinePublicCnPath : process.env.onlinePublicGlobalPath
  }

  if (curGame === 'pc') {
    if (isBuild && !isOnline) resBathPath = '/res/'
    return {
      template: 'public/pc.html',
      entry: '/src/main.js',
      filename: isDev ? 'index.html' : `index_${curGame}.html`,
      title: PageInfo.title,
      themeColor: PageInfo.themeColor || 'white',
      resBathPath,
      nameKey: JSON.stringify(PageInfo.nameKey)
    }
  }

  if (curGame === 'sdk') {
    if (isBuild && !isOnline) resBathPath = '/res/'
    return {
      template: 'public/sdk.html',
      entry: '/src/main.js',
      filename: isDev ? 'index.html' : `index_${curGame}.html`,
      title: PageInfo.title,
      themeColor: PageInfo.themeColor || 'white',
      resBathPath,
      nameKey: JSON.stringify(PageInfo.nameKey)
    }
  }
  if (curGame === 'sdk2') {
    if (isBuild && !isOnline) resBathPath = '/res/'
    return {
      template: 'public/sdk2.html',
      entry: '/src/main.js',
      filename: isDev ? 'index.html' : `index_${curGame}.html`,
      title: PageInfo.title,
      themeColor: PageInfo.themeColor || 'white',
      // resBathPath,
      nameKey: JSON.stringify(PageInfo.nameKey)
    }
  }

  return {
    template: 'public/index.html',
    entry: '/src/main.js',
    filename: isDev ? 'index.html' : `index_${curGame}.html`,
    game: curGame,
    // shareObj: EventInfo.shareObj || {},

    title: PageInfo.title,
    description: PageInfo.description,
    icon: PageInfo.icon,
    themeColor: PageInfo.themeColor || 'white',
    linkMainDomain: PageInfo.linkMainDomain || false,
    linkPathName: PageInfo.linkPathName,
    manifestPath: (isOnline && PageInfo.linkMainDomain ? `https://store.funplus.com/subservice/${curGame}/` : 'config/') + `${curGame}.manifest.webmanifest`,
    resBathPath
  }
}
function updatePageConfig (allConfig) {
  if (isDev) {
    const devGame = GAME
    if (!devGame) {
      console.log('pageConfig[\'devGame\'] 不能为空！')
      process.exit(0)
    }
    config.pages.index = risePage(devGame, allConfig[devGame])
  } else {
    config.publicPath = '/res/'
    for (const [curGame, curConfig] of Object.entries(allConfig)) {
      config.pages[curGame] = risePage(curGame, curConfig)
    }
  }
  console.log(config.pages)
}
function getDateMark () {
  const now = new Date();
  const year = now.getFullYear();
  const month = now.getMonth() + 1; // getMonth() 返回 0-11，因此需要加 1

  // 根据月份确定返回的时间戳
  let targetMonth;
  if (month <= 3) {
    targetMonth = 1; // 1月1日
  } else if (month <= 6) {
    targetMonth = 4; // 4月1日
  } else if (month <= 9) {
    targetMonth = 7; // 7月1日
  } else {
    targetMonth = 10; // 10月1日
  }

  // 构造目标日期
  const targetDate = new Date(year, targetMonth - 1, 1); // 注意：getMonth() 的值是从 0 开始的
  return Math.floor(targetDate.getTime() / 1000); // 返回时间戳（秒级）
}

module.exports = config
