(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pagePay~pageSdkV2"],{"0075":function(t,e,o){"use strict";o("d9e2"),o("14d9"),o("88a7"),o("271a"),o("5494");var s=o("fa7d"),i=o("2b80"),a=o.n(i),n=o("af82"),c=o("5001"),r=o("2f62");e["a"]={methods:{customGetSmDeviceId(t){if("function"!==typeof t)return console.error("customGetSmDeviceId cb 必须为函数！");new Promise((t,e)=>{this.$loading.show();const o=setTimeout(()=>t(""),2500);Object(s["b"])((function(e){o&&clearTimeout(o),t(e)}))}).then(e=>{e||console.error("dealSmDeviceId 失败！"),t(e)}).finally(()=>this.$loading.hide())},purchaseGoodsWithDeviceId(t,e){const{channel_id:o,channel_name:i,sub_channel_id:r}=this.chosenChannel,u=this.chosenDiamond,d={name:this.$vt("tokenName"),product_id:u.product_id,price:+(u.nowPriceWithTaxAndExtra||u.price),channel:i,channel_id:o,sub_channel_id:r,return_url:location.origin,fp_device_id:e};if("/"!==this.$router.options.base&&(d.return_url+="/"+this.$router.options.base.replace(/\//g,""),this.IS_CHECKOUT_SDK)){let t=location.origin+window.__ROUTERPATH;t.endsWith("/")&&(t=t.slice(0,-1)),d.return_url=t}"aof"!==this.$gameName&&"rom"!==this.$gameName||(d.ext_info=JSON.stringify({project_code:"koa_aof_web"}));const{type:_,chosenNum:l}=this.chosenDiamond;2===_&&(d.custom_multiple=l),t&&(d.backup=1);let h=this.$store.state.urlParams.utm_campaign;if(h){if(h.includes("?")){const t=h.indexOf("?");h=h.slice(0,t)}d.tracking_id=h}"standalone"===s["g"]&&(d.tracking_id="pwa_"+s["h"]),d.browser_id=localStorage.getItem("browserMark")||"";const p=new a.a;d.browser_info={terminalType:window.innerWidth>1200?"pc":"mobile",osType:p.getOS().name,...Object(s["f"])()};const m=this.$store.getters["formdata/FinalPriceState"],f=this.chosenCoupon;switch(m.feType){case"first_pay":case"direct_first_pay":d.act_type="first_pay",d.discount=f.discount,d.discount_price=f.discount_price;break;case"first_pay_rebate":case"direct_first_pay_rebate":d.act_type="first_pay_rebate",d.discount=f.discount,d.discount_price=f.discount_price,f.act_type&&(d.act_type=f.act_type);break;case"discount_coupon":d.act_type="coupon",d.discount=f.discount,d.discount_price=f.discount_price,d.coupon_id=f.coupon_id;break;case"cash_coupon":d.act_type="deduct",d.discount=f.deduct_price,d.discount_price=f.price,d.coupon_id=f.coupon_id;break;case"rebate_coupon":d.act_type="rebate",d.discount=f.discount,d.discount_price=f.discount_price,d.coupon_id=f.coupon_id;break;case"fixed_discount_coupon":case"direct_fixed_discount":{const t=this.$store.state.formdata.defaultDiscountInfo;d.act_type="fixed_discount",d.discount=t.discount,d.discount_price=t.discount_price;break}case"fixed_rebate":case"direct_fixed_rebate":d.act_type="fixed_rebate";break;case"fixed_dynamic_rebate":d.act_type="product_fixed_rebate";break;default:{const t=this.$store.state.functionSwitch.fixedDiscountType;t&&!d.act_type&&(d.act_type=t);const e=this.$store.state.functionSwitch.smallDiamondDoubleDiscount&&u.product_id===this.$gcbk("ids.minimumDiamondId"),o=e||this.$store.getters["formdata/TWMyCard"];o&&delete d.act_type}}const g=this.chosenChannel,C=`${g.channel_id}_${g.channel_name}_${g.sub_channel_id}_dropin`;if(this.$store.state.vb.builtInCashier&&"payermax_A34_A34_dropin"===C&&(d.order_type="drop_in"),sessionStorage.removeItem("goodsName"),this.IS_CHECKOUT_SDK){const t=this.$store.state.urlParams,e=JSON.parse(t.tc||"{}");m.sdkType&&(d.act_type=m.sdkType),d.package_id=e.package_id,d.name=d.package_name=e.product_name,d.game_order_id=t.oid,sessionStorage.setItem("goodsName",e.product_name)}if(this.IS_CHECKOUT_SDK_V2){const t=window._calState();t.type&&(d.act_type=t.type),d.name||(d.name=window.defaultPackageName,sessionStorage.setItem("goodsName",window.defaultPackageName))}const v=this.$store.state,y={method:`${i}|${o}|${r}`,amount:"",country:v.country,currency:v.currency,product_info:d.product_id,event:"pay_completed",revenue:d.price};this.$loading.show(),this.requestLoading=!0,new Promise(t=>{if(this.$store.getters["formdata/TWMyCard"])return t(Object(n["r"])(d));t(Object(n["s"])(d))}).then(t=>{const{data:e,code:o,message:s}=t;switch(o){case 0:new Promise((t,o)=>{"coin_debt"in e?(this.$root.$emit("showPop","ArrearsReminder",{debt:e.coin_debt||0}),this.$root.$once("arrearsReminderResult",e=>{e?t(1):o(Error("cancel pop!"))})):t(2)}).then(t=>{if(Object(c["logForPayResult"])("success",e.order_id,"-",y),"string"===typeof e.pay_url){const o=new URL(e.pay_url),s=new URLSearchParams(o.search);if(sessionStorage.setItem("3zRtY8vXwN",JSON.stringify(e)),sessionStorage.removeItem("7x9FkL2pQm"),e.pay_url.includes("pingpong")&&"jump_url"!==s.get("window_type")){const o={ppToken:s.get("token"),coinNums:e.coin_recv,currency:e.currency,currency_symbol:e.currency_symbol,amount:e.price};1===t&&(o.inDebt=!0),sessionStorage.setItem("ppParams",JSON.stringify(o)),this.$router.push("/pp")}else{if("jump_url"===s.get("window_type"))return location.href=e.pay_url.replace("&window_type=jump_url","").replace("?window_type=jump_url",""),null;if(e.open_with_new_window)return window.open(e.pay_url,"_blank");location.href=e.pay_url}}else e.pay_url.coinNums=e.coin_recv,e.pay_url.currency_symbol=e.currency_symbol,e.pay_url.host=e.payment_host,e.pay_url.order_id=e.payment_order_id,e.pay_url.out_trade_no=e.order_id,sessionStorage.setItem("id_sign",e.payment_order_id_sign),sessionStorage.setItem("url",e.payment_host),1===t&&(e.pay_url.inDebt=!0),"payermax"===e.pay_url.channel&&(e.pay_url.payment_order_id=e.payment_order_id,e.pay_url.name=e.name),sessionStorage.setItem("params",JSON.stringify(e.pay_url)),"payermax"===e.pay_url.channel?this.$router.push("/pm"):e.pay_url.client_secret?this.$router.push("/aw"):e.pay_url.store_card_url?this.$router.push("/cko"):e.pay_url.stripe_client_secret?this.$router.push("/sp"):this.$router.push("/ad")}).catch(t=>{console.log(t.message)});break;case 1003:{const t={1:this.$t("illegalGift"),2:this.$t("expiredPackage"),3:this.$t("InventoryShortage")};this.$toast.err(t[e.check_status]),Object(c["logForPayResult"])("failed","-",t[e.check_status],y);break}default:throw this.$toast.err(this.$t("shop_fail")),Error(s)}}).catch(t=>{this.requestLoading=!1,Object(c["logForPayResult"])("failed","-",t,y),console.error("coinPlaceOrder下单失败："+t.message)}).finally(()=>{this.requestLoading=!1,this.$loading.hide()})},async purchaseGoods(t){if(this.requestLoading||this.$store.getters["riskPolicy/forbiddenAccess"])return null;if(!this.isLogin)return this.$root.$emit("ClickPayButNotLogin");if(!this.$store.state.agreePrivacyPolicy)return this.$root.$emit("showPop","PrivacyPolicy");if(window.__needDEPop){const t=await new Promise((t,e)=>{const o={ok:()=>t(1),no:()=>t(0)};this.$root.$emit("showPop","privateConfirmPop",o)});if(!t)return null}if("{}"===JSON.stringify(this.chosenChannel))return this.$toast.err(this.$t("ModalTIpsShopBeforeChooseChannel"));const e=this;this.customGetSmDeviceId((function(o){e.purchaseGoodsWithDeviceId(t,o)}))},judgeRisk(){const{channel_id:t}=this.chosenChannel,e=this.$store.getters["riskPolicy/showTipsWhenSomeChannel"].indexOf(t);if(-1!==e){const t="use_"+this.$store.getters["riskPolicy/showTipsWhenSomeChannel"][e];return this.$root.$emit("showPop","RiskControlPolicy",{key:t,cb:this.purchaseGoods})}this.purchaseGoods()}},computed:{...Object(r["c"])("formdata",["chosenChannel","chosenDiamond","chosenCoupon","vip"]),...Object(r["c"])(["isPc","isMobile","IS_CHECKOUT_SDK","IS_CHECKOUT_SDK_V2"]),...Object(r["c"])("userinfo",["isLogin"]),...Object(r["c"])("gameinfo",["gameCode","isKOA","isSS"]),...Object(r["c"])("functionSwitch",["showMobilePolicy","boon"])},data(){return{requestLoading:!1}},created(){this.$root.$on("adyenInitError",()=>this.purchaseGoods(1)),location.search&&history.pushState({},"",location.pathname)}}},"1f1f":function(t,e,o){"use strict";var s=function(){var t=this,e=t._self._c;return e("common-part",{class:[t.$store.state.gameinfo.gameCode,"channel-part-wrapper",t.$gameName,{sdk:t.$store.state.IS_CHECKOUT_SDK}],attrs:{"label-font":t.$t("channelChosen"),id:"channel-part-wrapper"}},[e("div",{staticClass:"channel-list"},[t.calChannelList.length?t._l(t.calChannelList,(function(o,s){return e("div",{key:o.FE_CHANNEL_ID,staticClass:"channel-btn",class:[{"channel-chosen__active":o.FE_CHANNEL_ID===t.chosenChannel.FE_CHANNEL_ID}],on:{click:function(e){return t.toggleStatus(s)}}},[e("div",{directives:[{name:"lazy",rawName:"v-lazy:backgroundImage",value:o.icon_url,expression:"channel.icon_url",arg:"backgroundImage"}],staticClass:"image common-fade-in"}),o.subscript||t.whetherShowVipBonus(o)?e("div",{class:["recommendation","recommendation-REC"]},[e("span",{staticClass:"blank"}),t.whetherShowVipBonus(o)?e("span",{staticClass:"bonus-description"},[t._v(" VIP +"+t._s(t.vip.channelBonus*(2===t.chosenDiamond.type?t.chosenDiamond.totalDiamond:t.chosenDiamond.coin))+" "),e("i")]):e("span",{staticClass:"txt"},[t._v(t._s(t.$t("recommend-txt")))])]):t._e()])})):e("div",{staticClass:"empty"},[t._v(t._s(t.$t("nothingHere")))])],2)])},i=[],a=(o("e9f5"),o("910d"),o("ab43"),o("3772")),n=o("af82"),c=o("2f62"),r=o("fa7d"),u={name:"ChannelChoose",components:{CommonPart:a["a"]},props:{activity:{type:Object,default:()=>{}}},data(){return{channelList:[],unwatch:void 0,isUserChosen:!1}},computed:{...Object(c["c"])(["urlParams","isArZone","currencyUnit"]),...Object(c["c"])("formdata",["chosenChannel","chosenDiamond","chosenCoupon","vip","isInit","isFirstPayUsed"]),whetherShowVipBonus(){return t=>this.isInit&&this.vip.discountSubChannelId.includes(t.sub_channel_id)&&this.vip.channelBonus&&this.isFirstPayUsed&&!this.chosenCoupon.FE_INDEX},calChannelList(){const t=this.$store.getters["riskPolicy/hideSomeChannel"]||[];return t.length?(t.includes(this.chosenChannel.channel_id)&&this.$store.commit("formdata/resetChannel"),this.channelList.filter(e=>!t.includes(e.channel_id))):this.channelList}},methods:{loadChannelList(){const t={currency:this.urlParams.cr,price:+this.chosenDiamond.price,product_id:this.chosenDiamond.product_id},e=this.$store.state.formdata.chosenCoupon;if(e.FE_INDEX&&("discount_coupon"===e.feType&&(t.price=e.discount_price),"cash_coupon"===e.feType&&(t.price=e.price),"first_pay"===e.feType&&(t.price=e.discount_price)),this.$store.getters["formdata/takeEffectDefaultDiscount"]){const e=["BDT","CLP","COP","CRC","DZD","HUF","IDR","INR","IQD","JPY","KES","KRW","KZT","LBP","LKR","MMK","NGN","PHP","PKR","PYG","RSD","RUB","THB","TWD","TZS","VND"];let o=.95*t.price;o=e.includes(this.chosenDiamond.currency)?Math.ceil(o):o.toFixed(2),t.price=+o}const{type:o,nowPrice:s}=this.chosenDiamond;2===o&&(t.price=+s),this.$loading.show(),this.$store.commit("formdata/resetChannel"),Object(n["m"])(t).then(({data:t,code:e,message:o})=>{0===e?(this.channelList=this.adapterChannel(t),setTimeout(()=>{const{lastChannel:t}=this.$store.state.userinfo;if(t){const e=this.calChannelList.filter(e=>e.channel_id===t.channel_id&&t.sub_channel_id===e.sub_channel_id);e&&e.length&&this.$store.commit("formdata/setChosenChannel",e[0]);const o=this.calChannelList.filter(e=>e.channel_name===t.channel_name);if(o&&o.length)return this.$store.commit("formdata/setChosenChannel",o[0])}},0),this.$gcbk("switch.enableAnimation",!1)&&!window.channelFlag&&this.$store.state.isPc&&(window.channelFlag=1,this.$nextTick(()=>{gsap&&gsap.from(".channel-list",{height:0,duration:.4,clearProps:"height"})}))):this.$toast.err(this.$t("fetchChannelError"))}).finally(()=>this.$loading.hide())},adapterChannel(t){if(this.$store.state.gameinfo.isCn){let e;e=r["k"]?["WxpayJSAPI","Alipaywap"]:window.isMobile?["WxpayMWEB","Alipaywap"]:["WxpayPcNATIVE","Alipaypc"],t=t.filter(t=>"wxpay"!==t.channel_id&&"alipay"!==t.channel_id||e.indexOf(t.channel_name+t.sub_channel_id)>-1)}return t.map(t=>(t.FE_CHANNEL_ID=`${t.channel_id}__${t.channel_name}`,t))},toggleStatus(t){this.isUserChosen=!0;const e=this.calChannelList[t],o=this.chosenChannel;if(e.FE_CHANNEL_ID===o.FE_CHANNEL_ID)return null;this.$store.commit("formdata/setChosenChannel",this.calChannelList[t])},initLastChannel(){Object(n["k"])().then(t=>{const{code:e,data:o}=t;if(0===e){if(this.$store.commit("userinfo/saveLastChannelInfo",o),this.isUserChosen)return null;const{lastChannel:t}=this.$store.state.userinfo;if(t){const e=this.calChannelList.filter(e=>e.channel_id===t.channel_id&&t.sub_channel_id===e.sub_channel_id);if(e&&e.length)return this.$store.commit("formdata/setChosenChannel",e[0])}}})}},created(){this.$root.$on("couponChoose",()=>this.loadChannelList()),this.$root.$on("activityInitEnd",()=>this.loadChannelList()),this.$root.$on("loginEnd",t=>{1===t&&this.initLastChannel()})},beforeDestroy(){this.unwatch&&this.unwatch()}},d=u,_=(o("4979"),o("2877")),l=Object(_["a"])(d,s,i,!1,null,"97029186",null);e["a"]=l.exports},"271a":function(t,e,o){"use strict";var s=o("cb2d"),i=o("e330"),a=o("577e"),n=o("d6d6"),c=URLSearchParams,r=c.prototype,u=i(r.getAll),d=i(r.has),_=new c("a=1");!_.has("a",2)&&_.has("a",void 0)||s(r,"has",(function(t){var e=arguments.length,o=e<2?void 0:arguments[1];if(e&&void 0===o)return d(this,t);var s=u(this,t);n(e,1);var i=a(o),c=0;while(c<s.length)if(s[c++]===i)return!0;return!1}),{enumerable:!0,unsafe:!0})},"2fdd":function(t,e,o){},3772:function(t,e,o){"use strict";var s=function(){var t=this,e=t._self._c;return e("div",{staticClass:"common-part-wrapper",class:[t.$gameName,{sdk:t.$store.state.IS_CHECKOUT_SDK}]},[e("div",{staticClass:"label"},[t._t("label",(function(){return[t._v(t._s(t.labelFont))]}))],2),e("div",{staticClass:"body"},[t._t("default")],2)])},i=[],a={name:"CommonPart",props:{labelFont:{type:String,default:""}}},n=a,c=(o("c3e4"),o("2877")),r=Object(c["a"])(n,s,i,!1,null,"c17bc992",null);e["a"]=r.exports},"402f":function(t,e,o){},"43ae":function(t,e,o){"use strict";o("480e")},"480e":function(t,e,o){},4979:function(t,e,o){"use strict";o("fefc")},5494:function(t,e,o){"use strict";var s=o("83ab"),i=o("e330"),a=o("edd0"),n=URLSearchParams.prototype,c=i(n.forEach);s&&!("size"in n)&&a(n,"size",{get:function(){var t=0;return c(this,(function(){t++})),t},configurable:!0,enumerable:!0})},"5f48":function(t,e,o){"use strict";o("fef2")},6201:function(t,e,o){},"72a3":function(t,e,o){"use strict";var s=function(){var t=this,e=t._self._c;return e("common-part",{class:["wrapper",t.$gameName],attrs:{id:"login-part-wrapper","label-font":t.userinfo.isLogin?"":t.$vt("loginPlaceHolder")}},[t.userinfo.isLogin?e("div",{staticClass:"login-status__login"},[e("div",{directives:[{name:"lazy",rawName:"v-lazy:backgroundImage",value:t.userinfo.icon,expression:"userinfo.icon",arg:"backgroundImage"}],staticClass:"avatar lazy"}),e("div",{staticClass:"user-info"},[e("div",{staticClass:"row-1"},[e("div",{staticClass:"name"},[t._v(t._s(t.userinfo.name))]),t.$store.state.isPCSDK?t._e():e("div",{staticClass:"toggle click-btn",on:{click:t.logout}},["ssv"!==t.$gameName?[t._v(t._s(t.$t("switch_account")))]:t._e()],2)]),t.isKOA&&t.vip.isInit?e("div",{staticClass:"row-koa"},[e("div",{staticClass:"grade"},[t._v("VIP "+t._s(t.vip.level))]),e("div",{staticClass:"btn-jump"},[e("a",{attrs:{href:t.vipIntroducePageUrl,target:"_blank"}},[t._v(t._s(t.$t("title_s_vip")))])])]):t._e(),e("div",{staticClass:"row-2"},[e("span",{staticClass:"leave"},[t._v(t._s(t.$t("user_level",{0:t.userinfo.level})))]),e("span",{staticClass:"server"},[t._v(t._s(t.$t("user_server",{0:t.userinfo.server})))])])])]):[e("div",{staticClass:"login-status__not-login",class:[{emphasize:t.focusEmphasize}]},[e("div",{staticClass:"login-input-wrapper"},[e("input",{directives:[{name:"model",rawName:"v-model",value:t.uid,expression:"uid"}],attrs:{type:"number",id:"uidInput",placeholder:t.focusEmphasize?"":t.$vt("loginPlaceHolder")},domProps:{value:t.uid},on:{input:function(e){e.target.composing||(t.uid=e.target.value)}}}),e("i",{on:{click:function(e){e.stopPropagation(),t.isShowTipsImg=!0},mouseenter:function(e){e.stopPropagation(),t.isShowTipsImg=!0}}}),e("img",{staticClass:"tips-img",class:{active:t.isShowTipsImg},attrs:{src:t.$imageLoader("uidTips"),alt:""},on:{click:function(t){t.stopPropagation()}}})]),e("div",{class:["btn-login","click-btn","btn",{btn_disable:!t.uid},{disable:!t.uid}],on:{click:function(e){return t.query()}}},[t._v(t._s(t.$t("login")))]),t.isKOA?e("div",{staticClass:"vip-jump-wrapper"},[e("a",{attrs:{href:t.vipIntroducePageUrl,target:"_blank"}},[t._v(t._s(t.$t("title_s_vip")))])]):t._e()]),t.focusEmphasize?e("div",{staticClass:"tip tip-please-input"},[t._v(t._s(t.$vt("loginPlaceHolder")))]):t._e(),t.focusEmphasize?t._e():e("div",{staticClass:"tip tip-uid-get"},[t._v(t._s(t.$t("where-uid-is")))])]],2)},i=[],a=o("3772"),n=o("af82"),c=o("2f62"),r=o("5001"),u=o("9213"),d=o("fa7d"),_=o("3452"),l=o.n(_);const h=l.a.enc.Utf8.parse("84uyzdgah9m#m9x4qzu&ye53"),p=h.clone();function m(t){return encodeURIComponent(l.a.AES.encrypt(l.a.enc.Utf8.parse(t),h,{iv:p,mode:l.a.mode.CBC,padding:l.a.pad.Pkcs7}).toString())}p.sigBytes=16,p.words.splice(4);var f={name:"LoginModule",components:{CommonPart:a["a"]},data(){return{uid:"",isShowTipsImg:!1,focusEmphasize:!1}},computed:{...Object(c["c"])(["userinfo"]),...Object(c["c"])("gameinfo",["whiteChannel","blackChannel","greyChannel","gameCode","isKOA","isROMCP"]),...Object(c["c"])("functionSwitch",["loginValidation"]),...Object(c["c"])(["isPc"]),...Object(c["c"])("formdata",["vip"]),vipIntroducePageUrl(){return Object({NODE_ENV:"production",VUE_APP_PROD_ENV:"ONLINE",VUE_APP_PREFIX_AME:"https://ame.funplus.com",VUE_APP_PREFIX_ACCOUNT:"https://store-account.funplus.com/api/account",VUE_APP_LOGIN_PAGE_URL:"https://store-funplusid.funplus.com/login",VUE_APP_PREFIX_API_KOA:"https://koa-store-api.funplus.com/api",VUE_APP_PREFIX_TOKEN_KOA:"https://koa-store-coin.kingsgroupgames.com/api",VUE_APP_PREFIX_ACCOUNT_KOA:"https://koa-store-account.funplus.com/api/account",VUE_APP_PREFIX_AME_KOA:"https://ame-koa.kingsgroupgames.com",VUE_APP_PREFIX_TOKEN_KOACN:"https://koa-store-coin-tx.kingsgroup.cn/api",VUE_APP_PREFIX_ACCOUNT_KOACN:"https://koa-store-account-tx.kingsgroup.cn/api/account",VUE_APP_PREFIX_AME_KOACN:"https://ame-koa-tx.kingsgroup.cn",VUE_APP_PREFIX_STORE_KOARP:"https://paykoa.com",VUE_APP_VipIntroducePageUrl_aof:"/vip",VUE_APP_VipIntroducePageUrl_koa:"https://vip.funplus.com/koa",VUE_APP_VipIntroducePageUrl_rom:"https://vip.funplus.com/rom",VUE_APP_VipIntroducePageUrl_koaCn:"https://vip.funplus.com/koa-cn",VUE_APP_OlD_STORE_URL_KOA:"https://koa-store.funplus.com",VUE_APP_CN_ADDRESS_KOA:"https://paykoa.cn",VUE_APP_ROM_ADDRESS_KOA:"https://payromgame.com",VUE_APP_PREFIX_TOKEN_DC:"https://store.funplus.com/dcdarklegion/token",VUE_APP_PREFIX_AME_DC:"https://ame-dcdl.kingsgroupgames.com",VUE_APP_PREFIX_ACCOUNT_DC:"https://store.funplus.com/dcdarklegion/account",VUE_APP_PREFIX_STORE_DCRP:"https://store-master.funplus.com/dcdarklegion",VUE_APP_PREFIX_TOKEN_SSCP:"https://store.funplus.com/stateofsurvival/token",VUE_APP_PREFIX_ACCOUNT_SSCP:"https://store.funplus.com/global/account",VUE_APP_PREFIX_AME_SSCP:"https://ame-ss.funplus.com",VUE_APP_PREFIX_TOKEN_STCP:"https://store.funplus.com/stormshot/token",VUE_APP_PREFIX_ACCOUNT_STCP:"https://store.funplus.com/global/account",VUE_APP_PREFIX_AME_STCP:"https://ame-st.funplus.com",VUE_APP_PREFIX_TOKEN_MCCP:"https://store.funplus.com/mistycontinent/token",VUE_APP_PREFIX_ACCOUNT_MCCP:"https://store.funplus.com/global/account",VUE_APP_PREFIX_AME_MCCP:"https://ame-mc.funplus.com",VUE_APP_PREFIX_TOKEN_GOGCP:"https://store.funplus.com/gunsofglory/token",VUE_APP_PREFIX_ACCOUNT_GOGCP:"https://store.funplus.com/global/account",VUE_APP_PREFIX_AME_GOGCP:"https://ame-gog.funplus.com",VUE_APP_PREFIX_TOKEN_ROMCP:"https://store.funplus.com/realmofmystery/token",VUE_APP_PREFIX_ACCOUNT_ROMCP:"https://store.funplus.com/global/account",VUE_APP_PREFIX_AME_ROMCP:"https://ame-koa.kingsgroupgames.com",VUE_APP_PREFIX_TOKEN_SSV2:"https://ssv2-store-api.funplus.com/api",VUE_APP_PREFIX_AME_SSV2:"https://ame-ssv2.funplus.com",VUE_APP_PREFIX_TOKEN_FOUNDATION:"https://store.funplus.com/foundation/token",VUE_APP_PREFIX_ACCOUNT_FOUNDATION:"https://store.funplus.com/ali/account",VUE_APP_PREFIX_AME_FOUNDATION:"https://ame-foundation.funplus.com",VUE_APP_PREFIX_STORE_SSRP:"https://ss-pay.funplus.com",VUE_APP_PREFIX_TOKEN_SSD:"https://store.funplus.com/tilessurvive/token",VUE_APP_PREFIX_ACCOUNT_SSD:"https://store.funplus.com/global/account",VUE_APP_PREFIX_STORE_SSDRP:"https://store.funplus.com/tilessurvive/",VUE_APP_PREFIX_AME_SSD:"https://ame-ts.funplus.com",VUE_APP_PREFIX_TOKEN_MO:"https://store.funplus.com/seaofconquest/token",VUE_APP_PREFIX_ACCOUNT_MO:"https://store.funplus.com/ali/account",VUE_APP_PREFIX_AME_MO:"https://ame-mo.kingsgroupgames.com",VUE_APP_PREFIX_STORE_MORP:"https://soc-store.funplus.com",VUE_APP_PREFIX_STORE_STRP:"https://st-store.funplus.com",BASE_URL:"https://kg-web-cdn.akamaized.net/online/user-platform-web/web-pay-hub/dist_online/"})["VUE_APP_VipIntroducePageUrl_"+this.$gameName]+"?l="+this.$i18n.locale}},methods:{async query(t){if(!this.uid&&!t)return;const e=this.$store.state,o={hideErrToast:!0,game_project:e.gameProject};t&&"string"===typeof t&&(o.openid=t),this.uid&&(localStorage.removeItem("openid"),o.uid=+this.uid,"KOA"===this.$store.state.gameinfo.gameCode&&(o.ticket=m(`${this.uid}|${Math.floor(Date.now()/1e3)}`),o.fopenid=m(`${Math.ceil(Date.now()/1e3)}|${this.uid}`))),this.uid&&Object(r["logForClickLogin"])(this.uid);try{this.$loading.show();let{data:t={},code:e}=await Object(n["p"])(o);switch(this.$loading.hide(),e){case 0:{try{const e=this.$gcbk("ids.secretKey");"string"===typeof t&&(t=JSON.parse(Object(d["c"])(t,e)))}catch(s){console.error("解密失败！"+this.uid)}const e=this.whiteChannel||[];if(e.length&&!e.includes(t.pkg_channel))return this.uid="",this.$toast.err(this.$t("pkg_not_allow"));const o=this.greyChannel||[],{pkg_channel:i,openid:a}=t;if(o.length)for(const t of o){const{channel:e,to:o}=t;if(e.includes(i))return localStorage.removeItem("openid"),void(window.location.href=`${o}?openid=${encodeURIComponent(a)}`)}const n=this.blackChannel||[];if(n.length&&n.includes(i))return this.$toast.err(this.$t("pkg_not_allow"));const c=this.$store.state.IS_CHECKOUT_SDK||this.$store.state.isPCSDK;if(this.loginValidation&&!c){const e=await this.goValidation(t);if(!e)return null}this.$store.commit("userinfo/setUserInfo",t),(this.isKOA||this.isROMCP)&&await this.loadVipStatus(),this.judgePop(),this.initRiskPolicy(),Object(r["logForLoginSuccess"])(),this.$root.$emit("loginSuccess"),this.$root.$emit("loginEnd",1),this.uid="",this.$gcbk("switch.enableAnimation",!1)&&setTimeout(()=>gsap&&gsap.from(".login-status__login",{opacity:0,xPercent:-5,duration:.5}),0);break}case 2:this.$tips.show(this.$t("maintenance_tips"));break;case 1e3:case 1002:case 4001:case 1007:{const t={1e3:"toast_invalid_param",1002:"text_no_role",4001:"toast_black_user",1007:"toast_black_country"};this.$toast.err(this.$t(t[e])),1002!==e||o.uid||this.logout();break}case 2001:this.$toast.err(this.$t("account_login_fail")),setTimeout(()=>this.$store.commit("userinfo/logout"),1e3);break;default:this.$toast.err(this.$t("account_login_fail"))}}catch(i){i.message.includes("timeout of")&&this.$loading.hide()}},logout(){this.$store.commit("userinfo/logout")},judgePop(){if(window.location.pathname.includes("/common/"))return;const t=window.localStorage.getItem("isWhatDiamondPop");t||(window.localStorage.setItem("isWhatDiamondPop","true"),this.$root.$emit("showWhatIsDiamondPop"))},initRiskPolicy(){const t=this.$root.$emit.bind(this.$root);this.$loading.show(),Object(n["i"])().then(e=>{const{code:o,data:s}=e;0===o&&(this.$store.commit("riskPolicy/init",{list:s.user_risk_list,emit:t}),this.$store.commit("vb/savePrefixChannel",s.reserve_card_channel_gray_ratio),this.$store.commit("vb/resetBuiltInCashierStatus",s.switch_global_config),this.$store.commit("formdata/switchToggle",s.change_coupon_enable),this.$store.commit("functionSwitch/updateFunctionInfo",s),this.$root.$emit("updateSpecialDiamond",s.point_card_product))}).finally(()=>{this.$loading.hide()})},async goValidation(t){let e="";return Object(d["b"])(t=>{e=t}),new Promise((o,s)=>{this.$loading.show(),Object(n["t"])({fp_device_id:e,openid:t.openid}).then(e=>{let{code:s,data:i}=e;switch(i||(i={}),i.username=t.name,i.openid=t.openid,s){case 0:case 5004:i.successCb=()=>o(!0),i.failCb=()=>o(!1),this.$root.$emit("saveValidation",i);break;case 5011:case 5001:case 5002:o(!0);break;default:o(!1),this.$toast.err(this.$t("login-validation-error-text"))}}).finally(()=>this.$loading.hide())})},judgeAvatarPop(){if(this.$store.state.IS_CHECKOUT_SDK)return;const t=u["a"].getLocalStorage("isAvatarBonusPop"),e=`${(new Date).getMonth()+1}/${(new Date).getDate()}`;e!==t&&(this.$root.$emit("showPop","AvatarBonusPop"),u["a"].setLocalStorage("isAvatarBonusPop",e))},async loadVipStatus(){if(this.$store.state.IS_CHECKOUT_SDK_V2)return;const t={p0:"web",p1:11,p2:1075};this.$loading.show();try{const{code:e,data:o}=await Object(n["h"])(t);0===e&&this.$store.commit("formdata/initVipInfo",o)}catch(o){}this.$loading.hide(),this.vip.isNewUser&&this.judgeAvatarPop();let e=!1;this.$root.$on("availableTicketChosen",()=>{!e&&this.$toast.success(this.$t("bonus_coupon_mutually_exclusive")),e=!0}),this.$root.$on("TicketPopClose",()=>e=!1)}},created(){const t=localStorage.getItem("openid");t?this.query(t):(this.$root.$emit("loginEnd",0),this.isKOA&&this.judgeAvatarPop(),this.judgePop()),this.$root.$on("BodyClick",()=>{this.isShowTipsImg=!1}),this.$root.$on("ClickPayButNotLogin",()=>{this.focusEmphasize=!0;const t=document.querySelector("#uidInput");setTimeout(()=>{t.focus()},300),t.addEventListener("input",()=>{this.focusEmphasize=!1})})}},g=f,C=(o("7965"),o("2877")),v=Object(C["a"])(g,s,i,!1,null,"12983a50",null);e["a"]=v.exports},"733f":function(t,e,o){"use strict";o("77e3")},7673:function(t,e,o){"use strict";o("6201")},"774a":function(t,e,o){t.exports=o.p+"static/1751299200/img/coupon-item-chosen.fe63410a.png"},"77e3":function(t,e,o){},7965:function(t,e,o){"use strict";o("402f")},"878c":function(t,e,o){"use strict";var s=function(){var t=this,e=t._self._c;return e("common-part",{class:["coupon-bar-wrapper",t.$gameName],attrs:{"label-font":t.$t("coupon"),id:"coupon-bar-wrapper"}},[t.$store.state.userinfo.isLogin?e("div",{staticStyle:{display:"inline-block"},on:{click:function(e){t.activity.showPop=!0}}},[t.activity.isFirstPayUsed?[0===t.availableCouponNum?e("div",{staticClass:"coupons-wrapper coupons-wrapper__no_coupon"},[t._v(" "+t._s(t.$t("coupon_desc_unavailable"))+" ")]):t._e(),t.availableCouponNum>0&&!t.chosenCoupon.FE_INDEX?e("div",{staticClass:"coupons-wrapper coupons-wrapper__available"},[e("span",[t._v(t._s(t.$t("coupon_nums",{0:t.availableCouponNum})))]),t._v(" "),e("i")]):t._e(),t.availableCouponNum>0&&t.chosenCoupon.FE_INDEX?e("div",{staticClass:"coupons-wrapper coupons-wrapper__chosen"},[e("div",{staticClass:"left"},[e("span",[t._v(t._s(t.$t("coupon_desc_chosen_erver")))])]),e("div",{staticClass:"right"},[e("over-size-scale",{key:t.chosenCoupon.FE_INDEX},[e("span",["discount_coupon"===t.chosenCoupon.feType?[t._v(t._s(t.$t("coupon_discount",{0:t.chosenCoupon.rateWidthOutPercent})))]:t._e(),"cash_coupon"===t.chosenCoupon.feType?[e("span",{class:{"is-ar-zone":t.isArZone}},[t._v(t._s(t.chosenCoupon.deduct_price)+" "+t._s(t.currencyUnit))]),t._v(" OFF")]:t._e(),"rebate_coupon"===t.chosenCoupon.feType?[e("span",[t._v(t._s(t.$t("bonus_tips"))+" ")]),t._v(t._s(t.chosenCoupon.rate)),e("i",{staticClass:"diamond-icon"})]:t._e()],2)]),e("i")],1)]):t._e()]:e("div",{staticClass:"coupons-wrapper coupons-wrapper__unavailable"},[e("div",{staticClass:"left"},[0===t.availableCouponNum?e("span",[t._v(t._s(t.$t("coupon_desc_unavailable")))]):e("span",[t._v(t._s(t.$t("coupon_nums",{0:t.availableCouponNum})))])])])],2):e("div",{staticClass:"coupons-wrapper coupons-wrapper__not-login",on:{click:function(e){return t.$root.$emit("ClickPayButNotLogin")}}},[t._v(t._s(t.$t("view_coupons_after_login")))]),t.activity.showPop?e("coupon-choose-pop",{attrs:{"had-obtained-list":t.activity.hadObtainedList,"not-obtained-list":t.activity.notObtainedList,"last-index":t.activity.chosenIndex,"is-first-charge-used":t.activity.isFirstPayUsed},on:{close:t.closeCouponPop}}):t._e()],1)},i=[],a=(o("d9e2"),o("14d9"),o("e9f5"),o("910d"),o("7d54"),o("ab43"),o("3772")),n=o("af82"),c=o("2f62"),r=function(){var t=this,e=t._self._c;return e("div",{class:["ticket-chosen-wrapper",t.$gameName],attrs:{id:"ticket-chosen-wrapper"}},[e("div",{staticClass:"pop-main"},[e("div",{staticClass:"pop-close",on:{click:function(e){return t.$emit("close")}}}),e("div",{staticClass:"pop-title"},[e("h3",[t._v(t._s(t.$t("coupon")))]),e("span",[t._v(t._s(t.$t("discount_offer_tips")))])]),e("div",{staticClass:"divider"}),e("div",{staticClass:"nav-btn-wrapper"},[e("div",{class:["nav",{"nav-active":0===t.navIndex}],on:{click:function(e){t.navIndex=0}}},[t._v(t._s(t.$t("nav_my_coupon")))]),e("div",{class:["nav",{"nav-active":1===t.navIndex}],on:{click:function(e){t.navIndex=1}}},[t._v(t._s(t.$t("nav_other_coupon")))])]),e("div",{staticClass:"main-container"},[0===t.navIndex?[e("coupon-choose-list",{key:0,attrs:{"coupon-list":t.hadObtainedList,"is-first-charge-used":t.isFirstChargeUsed,reach:!0,"temp-chosen-coupon":t.tempChosenCoupon},on:{"update:tempChosenCoupon":function(e){t.tempChosenCoupon=e},"update:temp-chosen-coupon":function(e){t.tempChosenCoupon=e}}}),e("div",{class:["btn-confirm","click-btn",{"btn-confirm__unavailable":!t.isFirstChargeUsed}],on:{click:t.chooseCoupon}},[t._v(t._s(t.$t("modalBtnOk")))])]:e("coupon-choose-list",{key:1,attrs:{"coupon-list":t.notObtainedList,"is-first-charge-used":t.isFirstChargeUsed,reach:!1}})],2),e("p",{staticClass:"coupon-repeat-tips"},[t._v(" *"+t._s(t.$t("construction_faq_q5a1"))+" "),"RU"===t.$store.state.country?[t._v("Купоны не поддерживают Huawei Pay.")]:t._e()],2)])])},u=[],d=function(){var t=this,e=t._self._c;return e("div",{class:["ticket-wrapper",t.$gameName],attrs:{id:"ticket-wrapper"}},[e("div",{staticClass:"ticket-list"},[t._l(t.couponList,(function(o,s){return["first_pay"!==o.feType&&"first_pay_rebate"!==o.feType&&(!("leaveCount"in o)||o.leaveCount>0)?e("div",{key:o.coupon_id+o.type+s,class:["item",{item__active:t.reach&&o.FE_INDEX===t.tempChosenCoupon.FE_INDEX},{item__unavailable:!t.isFirstChargeUsed||!t.reach||0===o.is_invalid}],on:{click:function(e){return t.choose(s,o)}}},[e("div",{staticClass:"left"},[t.$store.state.formdata.switchToggleState&&o.change_enable?e("coupon-toggle",{attrs:{"temp-chosen":t.reach&&o.FE_INDEX===t.tempChosenCoupon.FE_INDEX,"coupon-item":o}}):t._e(),e("div",{staticClass:"title"},["discount_coupon"===o.feType?[t._v(t._s(o.rate)+" "),e("span",[t._v("OFF")])]:"cash_coupon"===o.feType?[e("span",{class:{"is-ar-zone":t.isArZone}},[t._v(t._s(o.deduct_price)+" "+t._s(t.currencyUnit))]),t._v(" OFF ")]:"rebate_coupon"===o.feType?[e("span",[t._v(t._s(t.$t("bonus_tips"))+" ")]),t._v(t._s(o.rate)),e("i",{staticClass:"diamond-icon"})]:t._e()],2)],1),e("div",{staticClass:"right"},[e("div",{staticClass:"desc"},[t._v(t._s(t.$t(o.langKey,{0:o.num}))+" "+t._s(o.langValue))]),o.showLeaveDate?e("div",{staticClass:"time"},[t._v(t._s(o.showLeaveDate))]):t._e()])]):t._e()]}))],2),t.showEmpty?e("span",{staticClass:"no-data"},[t._v(t._s(t.$t("nothingHere")))]):t._e()])},_=[],l=function(){var t=this,e=t._self._c;return e("div",{staticClass:"coupon-type-toggle-wrapper",on:{click:function(e){return e.stopPropagation(),t.beginToggleCoupon.apply(null,arguments)},mouseover:function(e){t.tips="discount_coupon"===t.couponItem.feType?t.$t("toggle-to-rebate"):t.$t("toggle-to-common")},mouseleave:function(e){t.tips=""}}},[t.tips?e("div",{staticClass:"tips"},[t._v(t._s(t.tips))]):t._e()])},h=[],p={name:"couponToggle",props:["couponItem","tempChosen"],data(){return{tips:""}},methods:{beginToggleCoupon(){this.tips="";const t=this.couponItem,e={change_type:"discount_coupon"===t.feType?"rebate":"coupon",coupon_id:this.couponItem.coupon_id};this.$loading.show(),Object(n["u"])(e).then(t=>{const{code:e}=t;0===e?(sessionStorage.setItem("reopenCoupon","1"),this.tempChosen&&sessionStorage.setItem("reChooseCoupon",this.couponItem.coupon_id),this.$root.$emit("couponToggleSuccess")):this.$toast.err(this.$t("toggle-fail-tips"))}).catch(t=>{console.error(t.message)}).finally(()=>this.$loading.hide())}},mounted(){this.couponItem.FE_INDEX===sessionStorage.getItem("popIndex")&&(sessionStorage.removeItem("popIndex"),this.tips="discount_coupon"===this.couponItem.feType?this.$t("toggle-to-rebate"):this.$t("toggle-to-common"),this.$el.scrollIntoView({behavior:"smooth"}))}},m=p,f=(o("929d"),o("2877")),g=Object(f["a"])(m,l,h,!1,null,"153b91ac",null),C=g.exports,v=o("fa7d"),y={name:"CouponChooseList",components:{CouponToggle:C},props:["couponList","isFirstChargeUsed","reach","tempChosenCoupon"],methods:{choose(t,e){if(!this.isFirstChargeUsed||!this.reach||0===e.is_invalid)return null;this.tempChosenCoupon.FE_INDEX===e.FE_INDEX?this.$emit("update:tempChosenCoupon",{}):(this.$emit("update:tempChosenCoupon",e),this.$root.$emit("availableTicketChosen"))}},computed:{...Object(c["c"])(["isArZone","currencyUnit"]),showEmpty(){return!this.couponList.filter(t=>t.feType.includes("_coupon")).length}},created(){if(this.$store.state.formdata.switchToggleState&&this.reach&&Object(v["o"])("toggleCoupon")){const t=this.couponList.findIndex(t=>t.change_enable);this.couponList[t]&&sessionStorage.setItem("popIndex",this.couponList[t].FE_INDEX)}}},$=y,P=(o("43ae"),Object(f["a"])($,d,_,!1,null,"3d2a8ef0",null)),b=P.exports,E={name:"CouponChoosePop",components:{CouponChooseList:b},props:["hadObtainedList","notObtainedList","isFirstChargeUsed","lastIndex"],data(){return{navIndex:0,tempChosenCoupon:this.$store.state.formdata.chosenCoupon}},computed:{...Object(c["c"])("formdata",["switchToggleState"])},methods:{chooseCoupon(){if(!this.isFirstChargeUsed)return null;this.tempChosenCoupon&&this.$store.commit("formdata/setChosenCoupon",this.tempChosenCoupon),this.$emit("close"),this.$root.$emit("couponChoose"),this.tempChosenCoupon.FE_INDEX&&this.$root.$emit("couponChosen")}},mounted(){this.$gcbk("switch.enableAnimation",!1)&&(gsap&&gsap.from(".ticket-chosen-wrapper .pop-main",{top:"45%",duration:.4,ease:"back",clearProps:!0}),gsap&&gsap.from(".ticket-chosen-wrapper",{backgroundColor:"rgba(0, 0, 0, 0)",duration:.4,clearProps:!0}))}},I=E,w=(o("5f48"),Object(f["a"])(I,r,u,!1,null,"26605e4f",null)),k=w.exports,O=o("da93"),S=o("72c2");const A=t=>(100*(1-t)).toFixed(0),D=t=>100*(t-1)&&(100*(t-1)).toFixed(0),T=t=>t.coin-t.level_coin;var x={name:"CouponChoose",components:{OverSizeScale:O["a"],CommonPart:a["a"],CouponChoosePop:k},data(){return{activity:{notObtainedList:[],hadObtainedList:[],chosenIndex:-1,showPop:!1,isFirstPayUsed:!0,timeInterval:void 0,isLoadingCoupon:!1}}},computed:{...Object(c["c"])(["urlParams","isArZone","currencyUnit"]),...Object(c["c"])("formdata",["chosenChannel","chosenCoupon","chosenDiamond","isFixedRebateWork"]),...Object(c["b"])("formdata",["FinalPriceState"]),availableCouponNum(){const t=this.activity.hadObtainedList;return t&&t.length?t.filter(t=>t.feType.includes("_coupon")&&("leaveCount"in t&&t.leaveCount>0||!("leaveCount"in t))).length:0}},methods:{initActInfo(t){const{timeInterval:e}=this.activity;e&&clearInterval(e),this.activity={notObtainedList:[],hadObtainedList:[],chosenIndex:-1,showPop:!1,isFirstPayUsed:!0,timeInterval:void 0},this.$store.commit("formdata/resetCouponInfo");const o=this.chosenDiamond.price;this.$root.$emit("setDefaultDiscountInfo",{price:o,discount_price:(.95*o).toFixed(2),feType:"fixed_discount_coupon",FE_INDEX:"fixed_discount_coupon_1",rateWidthOutPercent:5,type:"fixed_discount"}),(sessionStorage.getItem("reopenCoupon")||t)&&(sessionStorage.removeItem("reopenCoupon"),this.$nextTick(()=>{this.activity.showPop=!0}))},couponSort(t){const e=t;return e.sort((t,e)=>t.coupon_expire_time>e.coupon_expire_time?1:t.coupon_expire_time===e.coupon_expire_time?parseFloat(t.rateWidthOutPercent)<parseFloat(e.rateWidthOutPercent)?1:parseFloat(t.rateWidthOutPercent)===parseFloat(e.rateWidthOutPercent)?0:-1:-1),e},loadActivity(t=!1){const e={};e.price=this.chosenDiamond.price,e.product_id=this.chosenDiamond.product_id;const{type:o,chosenNum:s}=this.chosenDiamond;if(2===o&&(e.custom_multiple=s),!e.product_id)return null;this.chosenChannel&&(e.channel_id=this.chosenChannel.channel_id,e.sub_channel_id=this.chosenChannel.sub_channel_id),this.$store.state.IS_CHECKOUT_SDK&&(e.package_type=this.chosenDiamond.package_type),this.$store.state.IS_CHECKOUT_SDK&&!this.$store.state.country&&(e.country="US",e.currency="USD"),this.$loading.show(),this.couponLoading=!0,Object(n["g"])(e).then(o=>{this.initActInfo();const{code:s,data:i,message:a}=o;if(t&&(this.isFixedRebateWork?i.fixed_discount=[]:i.fixed_rebate=[],this.$store.commit("formdata/toggleCoupon")),0!==s)throw Error(a);{this.$store.getters["formdata/TWMyCard"]&&(i.first_pay=i.coupon=i.deduct=i.fixed_discount=[]),this.$store.commit("formdata/setIsInit",!0),this.$store.state.gameinfo.isKOA&&this.$store.commit("formdata/setFirstPayProducts",i.range_first_pay||[]),"foundation"===this.$gameName&&this.fixFoundationCoupon(e,i),this.adapterCouponType(i);let t=i.first_pay||[];t=t.map((t,o)=>({...t,feType:"first_pay",rateWidthOutPercent:A(t.discount),rate:A(t.discount)+"%",FE_INDEX:"first_pay_"+o,productId:e.product_id})),i.first_pay&&i.first_pay.length&&!i.first_pay[0].discount&&(t=[]),(i.first_pay_rebate||[]).length&&(t=(i.first_pay_rebate||[]).map((t,o)=>({...t,feType:"first_pay_rebate",rate:""+T(t),FE_INDEX:"first_pay_rebate_"+o,productId:e.product_id})));let o=i.coupon||[];o=o.map((t,o)=>({...t,feType:"discount_coupon",rateWidthOutPercent:A(t.discount),rate:A(t.discount)+"%",FE_INDEX:"discount_coupon_"+o,productId:e.product_id}));const s=o.filter(t=>t.is_received&&t.is_invalid),a=o.filter(t=>t.is_received&&!t.is_invalid);let n=i.deduct||[];n=n.map((t,o)=>({...t,feType:"cash_coupon",FE_INDEX:"cash_coupon_"+o,productId:e.product_id}));const c=n.filter(t=>t.is_received&&t.is_invalid),r=n.filter(t=>t.is_received&&!t.is_invalid);let u=i.rebate||[];u=u.map((t,o)=>({...t,feType:"rebate_coupon",FE_INDEX:"rebate_coupon_"+o,rate:D(t.discount)+"%",rateWidthOutPercent:D(t.discount),productId:e.product_id})),u=this.couponSort(u);const d=u.filter(t=>t.is_received&&t.is_invalid),_=u.filter(t=>t.is_received&&!t.is_invalid);let l=i.fixed_discount||[];l=l.map((t,e)=>({...t,feType:"fixed_discount_coupon",FE_INDEX:"fixed_discount_coupon_"+e,rateWidthOutPercent:A(t.discount)})),l.length&&this.$store.commit("formdata/setFixedCoupon",l[0]);let h=i.fixed_rebate||[];h=h.map((t,o)=>({...t,feType:"fixed_rebate",rateWidthOutPercent:D(t.discount),rate:D(t.discount)+"%",FE_INDEX:"fixed_rebate_"+o,productId:e.product_id})),this.$store.commit("formdata/setFixedRebate",h.length?h[0]:{});let p=i.product_fixed_rebate||[];p=p.map((t,o)=>({...t,feType:"fixed_dynamic_rebate",rateWidthOutPercent:D(t.discount),rate:D(t.discount)+"%",FE_INDEX:"fixed_dynamic_rebate_"+o,productId:e.product_id})),this.$store.commit("formdata/setFixedDynamicRebate",{chosen:p[0]||{},all:i.range_product_fixed_rebate||[]}),this.calcLeaveTime([...o,...n,...u].filter(t=>t.is_received));const m=[...t,...d,...c,...s,..._,...r,...a];this.activity.isFirstPayUsed=0===t.length,this.$store.commit("formdata/setFirstPayStatus",this.activity.isFirstPayUsed),this.activity.hadObtainedList=m;const f=[...d,...c,...s];if(t.length)this.$store.commit("formdata/setChosenCoupon",m[0]);else{const t=this.$store.state.IS_CHECKOUT_SDK_V2&&[...l,...h].length>0;if(f.length&&!t){let t=0;const e=sessionStorage.getItem("reChooseCoupon");e&&(t=m.findIndex(t=>t.coupon_id===+e),t=Math.max(0,t)),this.$store.commit("formdata/setChosenCoupon",m[t])}}this.parsingSdk2Coupon(f),this.activity.hadObtainedList=this.activity.hadObtainedList.map(t=>{if(t.discount_range){const e=this.$store.state.IS_CHECKOUT_SDK,o=e&&t.feType.includes("_coupon")?t.discount_price_range.split("-"):t.discount_range.split("-"),s=this.$store.state.currency;switch(this.$store.state.gameinfo.gameCode){case"KOA":case"MO":"cash_coupon"===t.feType&&(t.langValue=this.$t("min_cash_available_num",{0:o[0],1:this.$vt("tokenName")})),"discount_coupon"===t.feType&&(t.langValue=this.$t("max_cash_available_num",{0:o[1],1:this.$vt("tokenName")})),"rebate_coupon"===t.feType&&(t.langValue=this.$t("max_cash_available_num",{0:o[1],1:this.$vt("tokenName")}));break;default:o.length>1?"0"===o[0]?t.langValue=this.$t("max_cash_available_num",{0:o[1],1:this.$vt("tokenName")}):"0"===o[1]?t.langValue=this.$t("min_cash_available_num",{0:o[0],1:this.$vt("tokenName")}):t.langValue=this.$t("btw_cash_available_num2",{1:o[0],2:o[1],0:this.$vt("tokenName")}):t.langValue=this.$t("cash-num-eq-to",{0:o[0],1:this.$vt("tokenName")})}this.$store.state.IS_CHECKOUT_SDK&&(o.length>1?"0"===o[0]?t.langValue=this.$t("max_cash_available_num",{0:s,1:o[1]}):"0"===o[1]?t.langValue=this.$t("min_cash_available_num",{0:s,1:o[0]}):t.langValue=this.$t("btw_cash_available_num",{0:s,1:o[0],2:o[1]}):t.langValue=this.$t("cash-num-eq-to",{0:s,1:o[0]}))}return t});const g=[...u,...n,...o].filter(t=>!t.is_received),C={"login_0.9":[],"comm_third_0.8":[],"comm_third_0.9":[]},v={"login_0.9":"login_gain_coupon","comm_third_0.9":"invite_gain_coupon","comm_third_0.8":"invite_gain_coupon"};g.forEach(t=>{t.type.includes("login_")&&.9===t.discount&&C["login_0.9"].push(t),t.type.includes("comm_third")&&.8===t.discount&&C["comm_third_0.8"].push(t),t.type.includes("comm_third")&&.9===t.discount&&C["comm_third_0.9"].push(t)});for(const[e,i]of Object.entries(C))i.length&&this.activity.notObtainedList.push({...i[0],num:i.length,langKey:v[e]});this.activity.notObtainedList=this.activity.notObtainedList.sort((t,e)=>t.discount-e.discount),this.$root.$emit("activityInitEnd")}}).catch(t=>{this.initActInfo(),this.$toast.err(this.$t("network_err")),console.error("优惠券初始化失败："+t.message)}).finally(()=>{this.couponLoading=!1,this.$loading.hide(),sessionStorage.removeItem("reChooseCoupon")})},fixActivityInfo(){if(this.couponLoading)return null;const t=this.chosenCoupon.FE_INDEX,e={};e.price=this.chosenDiamond.price,e.product_id=this.chosenDiamond.product_id;const{type:o,chosenNum:s}=this.chosenDiamond;if(2===o&&(e.custom_multiple=s),!e.product_id)return null;this.chosenChannel&&(e.channel_id=this.chosenChannel.channel_id,e.sub_channel_id=this.chosenChannel.sub_channel_id),this.$store.state.IS_CHECKOUT_SDK&&(e.package_type=this.chosenDiamond.package_type),this.$store.state.IS_CHECKOUT_SDK&&!this.$store.state.country&&(e.country="US",e.currency="USD"),Object(n["g"])(e).then(o=>{this.initActInfo(this.activity.showPop);const{code:s,data:i,message:a}=o;if(0!==s)throw Error(a);{this.$store.getters["formdata/TWMyCard"]&&(i.first_pay=i.coupon=i.deduct=i.fixed_discount=[]),this.$store.commit("formdata/setIsInit",!0),"foundation"===this.$gameName&&this.fixFoundationCoupon(e,i),this.adapterCouponType(i);let o=i.first_pay||[];o=o.map((t,o)=>({...t,feType:"first_pay",rateWidthOutPercent:A(t.discount),rate:A(t.discount)+"%",FE_INDEX:"first_pay_"+o,productId:e.product_id})),i.first_pay&&i.first_pay.length&&!i.first_pay[0].discount&&(o=[]),(i.first_pay_rebate||[]).length&&(o=(i.first_pay_rebate||[]).map((t,o)=>({...t,feType:"first_pay_rebate",rate:""+T(t),FE_INDEX:"first_pay_rebate_"+o,productId:e.product_id})));let s=i.coupon||[];s=s.map((t,o)=>({...t,feType:"discount_coupon",rateWidthOutPercent:A(t.discount),rate:A(t.discount)+"%",FE_INDEX:"discount_coupon_"+o,productId:e.product_id}));const a=s.filter(t=>t.is_received&&t.is_invalid),n=s.filter(t=>t.is_received&&!t.is_invalid);let c=i.deduct||[];c=c.map((t,o)=>({...t,feType:"cash_coupon",FE_INDEX:"cash_coupon_"+o,productId:e.product_id}));const r=c.filter(t=>t.is_received&&t.is_invalid),u=c.filter(t=>t.is_received&&!t.is_invalid);let d=i.rebate||[];d=d.map((t,o)=>({...t,feType:"rebate_coupon",FE_INDEX:"rebate_coupon_"+o,rate:D(t.discount)+"%",rateWidthOutPercent:D(t.discount),productId:e.product_id})),d=this.couponSort(d);const _=d.filter(t=>t.is_received&&t.is_invalid),l=d.filter(t=>t.is_received&&!t.is_invalid);let h=i.fixed_discount||[];h=h.map((t,e)=>({...t,feType:"fixed_discount_coupon",FE_INDEX:"fixed_discount_coupon_"+e,rateWidthOutPercent:A(t.discount)})),h.length&&this.$store.commit("formdata/setFixedCoupon",h[0]);let p=i.fixed_rebate||[];p=p.map((t,o)=>({...t,feType:"fixed_rebate",rateWidthOutPercent:D(t.discount),rate:D(t.discount)+"%",FE_INDEX:"fixed_rebate_"+o,productId:e.product_id})),this.$store.commit("formdata/setFixedRebate",p.length?p[0]:{});let m=i.product_fixed_rebate||[];m=m.map((t,o)=>({...t,feType:"fixed_dynamic_rebate",rateWidthOutPercent:D(t.discount),rate:D(t.discount)+"%",FE_INDEX:"fixed_dynamic_rebate_"+o,productId:e.product_id})),this.$store.commit("formdata/setFixedDynamicRebate",{chosen:m[0]||{},all:i.range_product_fixed_rebate||[]}),this.calcLeaveTime([...s,...c,...d].filter(t=>t.is_received));const f=[...o,..._,...r,...a,...l,...u,...n];this.activity.isFirstPayUsed=0===o.length,this.$store.commit("formdata/setFirstPayStatus",this.activity.isFirstPayUsed),this.activity.hadObtainedList=f;const g=[..._,...r,...a];if(t)if(o.length)t===f[0].FE_INDEX&&this.$store.commit("formdata/setChosenCoupon",f[0]);else if(g.length){const e=f.findIndex(e=>e.FE_INDEX===t)||0;this.$store.commit("formdata/setChosenCoupon",f[e])}this.parsingSdk2Coupon(g),this.activity.hadObtainedList=this.activity.hadObtainedList.map(t=>{if(t.discount_range){const e=this.$store.state.IS_CHECKOUT_SDK,o=e&&t.feType.includes("_coupon")?t.discount_price_range.split("-"):t.discount_range.split("-"),s=this.$store.state.currency;switch(this.$store.state.gameinfo.gameCode){case"KOA":case"MO":"cash_coupon"===t.feType&&(t.langValue=this.$t("min_cash_available_num",{0:o[0],1:this.$vt("tokenName")})),"discount_coupon"===t.feType&&(t.langValue=this.$t("max_cash_available_num",{0:o[1],1:this.$vt("tokenName")})),"rebate_coupon"===t.feType&&(t.langValue=this.$t("max_cash_available_num",{0:o[1],1:this.$vt("tokenName")}));break;default:o.length>1?"0"===o[0]?t.langValue=this.$t("max_cash_available_num",{0:o[1],1:this.$vt("tokenName")}):"0"===o[1]?t.langValue=this.$t("min_cash_available_num",{0:o[0],1:this.$vt("tokenName")}):t.langValue=this.$t("btw_cash_available_num2",{1:o[0],2:o[1],0:this.$vt("tokenName")}):t.langValue=this.$t("cash-num-eq-to",{0:o[0],1:this.$vt("tokenName")})}this.$store.state.IS_CHECKOUT_SDK&&(o.length>1?"0"===o[0]?t.langValue=this.$t("max_cash_available_num",{0:s,1:o[1]}):"0"===o[1]?t.langValue=this.$t("min_cash_available_num",{0:s,1:o[0]}):t.langValue=this.$t("btw_cash_available_num",{0:s,1:o[0],2:o[1]}):t.langValue=this.$t("cash-num-eq-to",{0:s,1:o[0]}))}return t});const C=[...d,...c,...s].filter(t=>!t.is_received),v={"login_0.9":[],"comm_third_0.8":[],"comm_third_0.9":[]},y={"login_0.9":"login_gain_coupon","comm_third_0.9":"invite_gain_coupon","comm_third_0.8":"invite_gain_coupon"};C.forEach(t=>{t.type.includes("login_")&&.9===t.discount&&v["login_0.9"].push(t),t.type.includes("comm_third")&&.8===t.discount&&v["comm_third_0.8"].push(t),t.type.includes("comm_third")&&.9===t.discount&&v["comm_third_0.9"].push(t)});for(const[t,e]of Object.entries(v))e.length&&this.activity.notObtainedList.push({...e[0],num:e.length,langKey:y[t]});this.activity.notObtainedList=this.activity.notObtainedList.sort((t,e)=>t.discount-e.discount)}}).catch(t=>{this.initActInfo(),this.$toast.err(this.$t("network_err")),console.error("优惠券初始化失败："+t.message)})},calcLeaveTime(t){const e=t=>t<10?"0"+Math.floor(t):Math.floor(t),o=t=>`${Math.floor(t/3600/24)}d ${e(t/3600%24)} : ${e(t/60%60)} : ${e(t%60)}`,s=t.filter(t=>t.coupon_expire_time&&t.coupon_expire_time>0);for(const i of Object.values(s)){const t=i.leaveCount=i.coupon_expire_time;i.showLeaveDate=o(t)}this.activity.timeInterval=setInterval(()=>{for(const t of Object.values(s)){const e=t.leaveCount-1;e>=0&&(t.leaveCount--,t.showLeaveDate=o(e)),0===e&&this.chosenCoupon.FE_INDEX===t.FE_INDEX&&(this.$store.commit("formdata/setChosenCoupon",{}),this.$root.$emit("couponChoose"))}},1e3)},closeCouponPop(t){this.activity.showPop=!1,this.$root.$emit("TicketPopClose")},parsingSdk2Coupon(t){this.$store.state.IS_CHECKOUT_SDK_V2&&this.$root.$emit("updateSdk2CouponList",t)},fixFoundationCoupon(t,e){const o=e.coin_level_first_pay;if(!o||!o.length)return null;const s=o.map(t=>(t.rate=""+T(t),t.feType="first_pay_rebate",t));s.length&&this.$store.commit("formdata/setFirstPayProducts",[{product_discount_range:s}]);const i=s.filter(e=>e.product_id===t.product_id)||[];i.length&&(i[0].act_type="coin_level_first_pay",e.first_pay_rebate=i)},adapterCouponType(t){this.$store.state.IS_CHECKOUT_SDK_V2&&(t.first_pay=t.direct_first_pay,t.first_pay_rebate=t.direct_first_pay_rebate,t.fixed_discount=t.direct_fixed_discount,t.fixed_rebate=t.direct_fixed_rebate,Reflect.deleteProperty(t,"direct_first_pay"),Reflect.deleteProperty(t,"direct_first_pay_rebate"),Reflect.deleteProperty(t,"direct_fixed_discount"),Reflect.deleteProperty(t,"direct_fixed_rebate"))}},created(){const t="$store.state.formdata.chosenDiamond.product_id";this.unwatch=this.$watch(t,t=>t&&this.loadActivity(),{immediate:!0});const e="$store.state.formdata.chosenDiamond.totalDiamond";this.unwatch=this.$watch(e,(t,e)=>t&&e&&this.loadActivity()),this.$root.$once("loginSuccess",()=>this.loadActivity());const o="$store.state.formdata.chosenChannel.FE_CHANNEL_ID";this.unwatch=this.$watch(o,t=>t&&this.fixActivityInfo()),this.$root.$on("couponToggleSuccess",()=>this.loadActivity()),this.$root.$on("reloadActivity",()=>this.loadActivity()),this.$root.$on("toggleFixedCoupon",t=>this.loadActivity(t))},mounted(){S["a"].ios()&&this.$watch("activity.showPop",t=>this.$root.$emit("showCouponPop",t))},beforeDestroy(){this.unwatch&&this.unwatch()}},F=x,N=(o("ec3d"),Object(f["a"])(F,s,i,!1,null,"066665a8",null));e["a"]=N.exports},"88a7":function(t,e,o){"use strict";var s=o("cb2d"),i=o("e330"),a=o("577e"),n=o("d6d6"),c=URLSearchParams,r=c.prototype,u=i(r.append),d=i(r["delete"]),_=i(r.forEach),l=i([].push),h=new c("a=1&a=2&b=3");h["delete"]("a",1),h["delete"]("b",void 0),h+""!=="a=2"&&s(r,"delete",(function(t){var e=arguments.length,o=e<2?void 0:arguments[1];if(e&&void 0===o)return d(this,t);var s=[];_(this,(function(t,e){l(s,{key:e,value:t})})),n(e,1);var i,c=a(t),r=a(o),h=0,p=0,m=!1,f=s.length;while(h<f)i=s[h++],m||i.key===c?(m=!0,d(this,i.key)):p++;while(p<f)i=s[p++],i.key===c&&i.value===r||u(this,i.key,i.value)}),{enumerable:!0,unsafe:!0})},"8d29":function(t,e,o){"use strict";var s=function(){var t=this,e=t._self._c;return e("common-part",{scopedSlots:t._u([{key:"label",fn:function(){return[e("div",{staticClass:"package-label"},[e("div",[t._v(t._s(t.$t("adyen-order-info")))]),t.$gcbk("switch.switchRebate",!1)&&t.$store.state.formdata.isFixedEventOpen?e("div",[e("fixed-coupon-switch")],1):t._e()])]},proxy:!0}])},[e("div",{staticClass:"goods-name",class:[t.$gameName],attrs:{id:"direct-package-name"}},[t._v(" "+t._s(t.goodsName)+" ")])])},i=[],a=(o("bbd2"),o("3772")),n=function(){var t=this,e=t._self._c;return e("div",{staticClass:"coupon-switch-toggle",on:{click:function(e){return t.$root.$emit("toggleFixedCoupon",!0)}}},[e("i",{staticClass:"toggle-icon",class:{active:t.isFixedRebateWork}}),e("span",{staticClass:"toggle-lang"},[t._v(t._s(t.$t("gog-fixed-coupon-switch").replace("5 %","5%")))])])},c=[],r=o("2f62"),u={name:"fixed-coupon-switch",computed:{...Object(r["c"])("formdata",["isFixedRebateWork"])}},d=u,_=(o("b599"),o("2877")),l=Object(_["a"])(d,n,c,!1,null,"2b400ddb",null),h=l.exports,p=o("af82"),m={components:{CommonPart:a["a"],FixedCouponSwitch:h},data(){return{goodsName:"-"}},methods:{loadDiamondList(){const t={};if(this.$store.state.urlParams.tc){const e=JSON.parse(this.$store.state.urlParams.tc);t.product_id=e.product_id,this.goodsName=e.product_name}this.$loading.show(),Object(p["l"])(t).then(t=>{const{data:e,code:o}=t;if(0===o){if(e.length&&1!==e.length)return this.$toast.err(this.$t("cb_page_title_err"));this.$store.commit("formdata/setChosenDiamond",e[0])}}).finally(()=>this.$loading.hide())},loadOtherDiamondList(){const t={game_order_id:this.$store.state.urlParams.oid||""};if(this.$store.state.urlParams.tc){const e=JSON.parse(this.$store.state.urlParams.tc);t.product_id=e.product_id,e.product_name&&(this.goodsName=e.product_name,this.$root.$emit("updateSdk2PackageName",e.product_name))}if(!t.game_order_id)return this.$toast.err(this.$t("sdk2_error_order"));this.$loading.show(),Object(p["l"])(t).then(t=>{const{data:e,code:o}=t;switch(o){case 0:this.$store.commit("formdata/setChosenDiamond",e),e.sku_name&&"-"===this.goodsName&&(this.goodsName=e.sku_name,this.$store.commit("updateUrlParams",{product_name:e.sku_name}),this.$root.$emit("updateSdk2PackageName",this.goodsName)),"-"===this.goodsName&&this.setDefaultName(e);break;case 106041:this.$toast.err(this.$t("cb_view_err_tips"));break;default:this.$toast.err(this.$t("RU_refused"))}}).finally(()=>this.$loading.hide())},resetSdkStatus(){localStorage.setItem("isWhatDiamondPop","true")},loginFail(){this.$nextTick(()=>{document.querySelector(".checkout-footer-wrapper")&&(document.querySelector(".checkout-footer-wrapper").style.display="none"),document.querySelector(".checkout-counter-sdk-b")&&(document.querySelector(".checkout-counter-sdk-b").style.display="none")})},setDefaultName(t){const e=this.$t("sdk2_default_package_name",{1:t.no_tax_price,0:t.currency_symbol});this.goodsName=e,this.$store.commit("updateUrlParams",{product_name:e}),this.$root.$emit("updateSdk2PackageName",e)}},created(){this.resetSdkStatus(),this.$root.$on("loginEnd",t=>{1===t&&("dc"===this.$gameName?this.loadDiamondList():this.loadOtherDiamondList()),0===t&&this.loginFail()}),console.log(this.$gcbk("switch.switchRebate",!1))}},f=m,g=(o("733f"),Object(_["a"])(f,s,i,!1,null,"cf7c3d58",null));e["a"]=g.exports},"929d":function(t,e,o){"use strict";o("9f15")},"9f15":function(t,e,o){},b534:function(t,e,o){"use strict";o("bf28")},b599:function(t,e,o){"use strict";o("f7c8")},bbd2:function(t,e,o){const s=[o("fb5d"),o("774a")],i=t=>t?new Promise((e,o)=>{const s=document.createElement("link");s.as="image",s.rel="preload",s.href=t,document.head.appendChild(s),s.onload=e,s.onerror=o,setTimeout(o,5e3)}):Promise.resolve(),a=async()=>{while(s.length)try{await i(s.shift())}catch(t){console.error(t)}};Promise.all(Array.from({length:1},a))},bf28:function(t,e,o){},c3e4:function(t,e,o){"use strict";o("e8db")},d6d6:function(t,e,o){"use strict";var s=TypeError;t.exports=function(t,e){if(t<e)throw new s("Not enough arguments");return t}},da93:function(t,e,o){"use strict";var s=function(){var t=this,e=t._self._c;return e("span",{ref:"outer",staticClass:"outer"},[e("span",{ref:"inner",staticClass:"inner",style:`transform: scale(${t.scale})`},[t._t("default")],2)])},i=[],a={name:"OverSizeScale",data(){return{scale:1}},methods:{calc(){const t=this.$refs.outer.offsetWidth,e=this.$refs.inner.offsetWidth;e>t&&(this.scale=t/e)}},mounted(){this.calc()}},n=a,c=(o("b534"),o("2877")),r=Object(c["a"])(n,s,i,!1,null,"62189d20",null);e["a"]=r.exports},e28d:function(t,e,o){"use strict";var s=function(){var t=this,e=t._self._c;return e("div",{class:["checkout-footer-wrapper",t.$gameName,{sdk:t.IS_CHECKOUT_SDK}],attrs:{id:"checkout-footer-wrapper"},on:{click:function(t){t.stopPropagation()}}},[e("transition",{attrs:{name:"fade"}},[t.expandMode?e("div",{staticClass:"expand-part"},[e("div",{staticClass:"pop-title"},[t._v(t._s(t.$t("tax-details")))]),e("div",{staticClass:"divider"}),e("div",{staticClass:"value-wrapper"},[e("div",{staticClass:"origin-price"},[e("span",[t._v(t._s(t.$t("tax-price"))),t.IS_CHECKOUT_SDK?[t._v("：")]:t._e()],2),e("span",[t._v(t._s(t.chosenDiamond.nowPrice||t.chosenDiamond.level_currency_price)+t._s(t.currencyUnit))])]),t.FinalPriceState.feType&&t.hideDiscountRow?e("div",{staticClass:"discount"},[e("span",[t._v(t._s(t.$t("tax-discount"))+" "),t.IS_CHECKOUT_SDK?[t._v("：")]:t._e()],2),e("span",[[["first_pay","discount_coupon"].includes(t.chosenCoupon.feType)?[t._v(" - "+t._s(t.chosenCoupon.discount_amount)+" "+t._s(t.currencyUnit)+" ("+t._s(t.chosenCoupon.rateWidthOutPercent)+"% OFF) ")]:t._e(),"cash_coupon"===t.chosenCoupon.feType?[t._v(" - "+t._s(t.chosenCoupon.discount_amount)+" "+t._s(t.currencyUnit)+" ")]:t._e(),"fixed_discount_coupon"===t.FinalPriceState.feType?[t._v(" - "+t._s(t.FinalPriceState.offCountAmount)+" "+t._s(t.currencyUnit)+" ")]:t._e()]],2)]):t._e(),t.taxCost?e("div",{staticClass:"tax"},[e("span",[t._v(t._s(t.$t("tax-txt"))),t.IS_CHECKOUT_SDK?[t._v("：")]:t._e()],2),e("span",[t._v(t._s(t.taxCost)+t._s(t.currencyUnit))])]):t._e(),t.extraCost?e("div",{staticClass:"tax"},[e("span",[t._v(t._s(t.$t("extra-txt"))),t.IS_CHECKOUT_SDK?[t._v("：")]:t._e()],2),e("span",[t._v(t._s(t.extraCost)+t._s(t.currencyUnit))])]):t._e()]),e("div",{staticClass:"divider"})]):t._e()]),e("div",{staticClass:"common-part"},[e("div",{staticClass:"total-price"},[e("div",{staticClass:"row-1"},[e("span",{staticClass:"now-price",class:{"is-ar-zone":t.isArZone}},[t._v(t._s(t.FinalPriceState.finalNowPrice))]),t.showTaxBtn?e("span",{staticClass:"rate",class:{active:t.expandMode},on:{click:function(e){t.expandMode=!t.expandMode}}},[t._v("+ "+t._s(t.$t("tax-txt"))),e("i")]):t._e()]),e("div",{staticClass:"row-2"},[t.FinalPriceState.finalOriginPrice?e("span",{class:["origin-price",{"is-ar-zone":t.isArZone}]},[t._v(t._s(t.FinalPriceState.finalOriginPrice))]):t._e(),t.FinalPriceState.offCountTips?e("span",{staticClass:"off-count-tips",class:{"off-count-left":!t.hideDiscountRow},domProps:{innerHTML:t._s(t.FinalPriceState.offCountTips)}}):t._e()])]),e("div",{staticClass:"btn click-btn",class:[{disable:t.requestLoading||t.$store.getters["riskPolicy/forbiddenAccess"]},t.$i18n.locale],on:{click:function(e){return t.$emit("purchaseGoods")}}},[e("span",[t._v(t._s(t.$t("shop_now")))]),t.vip.isNewUser?e("i"):t._e()])])],1)},i=[],a=o("2f62"),n={name:"CheckoutFooterTax",props:["requestLoading"],data(){return{expandMode:!1}},computed:{...Object(a["c"])(["urlParams","isArZone","currencyUnit","IS_CHECKOUT_SDK"]),...Object(a["c"])("formdata",["chosenChannel","chosenDiamond","chosenCoupon","vip"]),...Object(a["c"])("gameinfo",["defaultDiscount","gameCode"]),...Object(a["c"])("userinfo",["isLogin"]),...Object(a["b"])("formdata",["FinalPriceState","getRebateCoin","getSDKRebateCoin"]),taxCost(){return this.chosenCoupon.taxation||this.FinalPriceState.taxation||this.chosenDiamond.taxation},extraCost(){return this.chosenCoupon.extra_fee_amount||this.FinalPriceState.extra_fee_amount||this.chosenDiamond.extra_fee_amount},showTaxBtn(){return this.taxCost||this.extraCost},hideDiscountRow(){return!!this.FinalPriceState.feType&&!this.FinalPriceState.feType.includes("rebate")}},watch:{showTaxBtn(t){t||(this.expandMode=!1)}},mounted(){this.$root.$on("BodyClick",()=>{this.expandMode=!1})}},c=n,r=(o("7673"),o("2877")),u=Object(r["a"])(c,s,i,!1,null,"4ceefa9e",null);e["a"]=u.exports},e8db:function(t,e,o){},ec3d:function(t,e,o){"use strict";o("2fdd")},f7c8:function(t,e,o){},fb5d:function(t,e){t.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAABDwAAACGBAMAAADDWofQAAAAD1BMVEUAAAD/66//67L/6bH/6rHH7gOuAAAABHRSTlMAQL+ATezaDQAAAq1JREFUeNrs3FFu01AURVEXGICDGICFOoBUMIAmefMfEx8tGJA4QpXqd2+01gTys+34WTlZ4D+cjvTHB1PPX218HYdafjMo6HZefvk8Drbun/0wKOlpefVhvKucx8dBTefX6/dxHG2TRwMvF/GXcbjnPY9Pg6Ius778b3se3wdVrfuTx7EcXDrYZl2+2/5YTFnXWZfvzXdLB+usk8P2840LhW3TTg5np9r6nufd3b+dTu4dxV18+ZOeTR8H/MPVewfkwdvIA3kgD+RBIA8CeRDIg0AeBPIgkAeBPAjkQSAPAnkQ3FEet/XBL9Wqm5bHdV0WP1Wrbloel5d5L6VNy+NsI9fAtDxW//3SwLQ8DLA7mHf3kEcD8/IwomxgWh6bPBqYd7D19w0NLGOWs5tHfRNfqj+dvDStztkBeSAP5EEgDwJ5EMiDQB4E8iCQB4E8CORBIA8CeRDIg0AeBPIgkAfBHeVhod+AhT4V87DQ78BCn4p5WOh3YKFPxTws9Duw0KdiHhb6HVjoUzEPC/0OLPQpmQcNyAN5IA/kQSAPAnkQyINAHgTyIJAHgTwI5EEgDwJ5EMiDQB4E8iC4ozws9Buw0KdiHhb6HVjoUzEPC/0OLPSpmIeFfgcW+lTMw0K/Awt9KuZhod+BhT4l86ABeSAP5IE8CORBIA8CeRDI40c7d3CDMAwEUXQkKCB0AKnAEg2EdfqvCWQf4MIecsjO4b8WPLIsa3eQIB5IEA8kiAcSxAMJ4oEE8cCZWFTDf0E8QDxwSC9bc42bdCGb3l667iVi7rkwqm5tq5oWbxKLLvaatFfo+mDTxV3VATWJ68Ner+rY0ERBkLVWdEBdAwVB1kJFFT2bBgqCrN2rnoeNePjrZc/DRQP9UcZi0VDweyni4S6avp7rqfRjhaHHvDveAy+ZKkukVxYAAAAASUVORK5CYII="},fef2:function(t,e,o){},fefc:function(t,e,o){}}]);