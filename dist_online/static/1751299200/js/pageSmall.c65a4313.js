(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pageSmall"],{"0219":function(e,t,n){"use strict";n("f68e")},"07f4":function(e,t,n){"use strict";n("f81c")},"164b":function(e,t,n){"use strict";n.r(t);var i=function(){var e=this,t=e._self._c;return t("div",{staticClass:"order-page-wrapper",on:{click:e.closeAllSlide}},[t("header",[t("div",{staticClass:"logo"}),t("div",{staticClass:"right"},[t("div",{staticClass:"toggle"},[t("div",{staticClass:"now-lang",on:{click:function(t){return t.stopPropagation(),e.menuToggle("showToggleLang")}}},[e._v(" "+e._s(e.langObj[e.$i18n.locale])+" "),t("i",{class:{"caret-reverse":e.showToggleLang}})]),e.showToggleLang?t("div",{staticClass:"options"},e._l(Object.entries(e.langObj),(function([n,i]){return t("span",{key:n,on:{click:function(t){return e.toggleLang(n)}}},[e._v(" "+e._s(i)+" ")])})),0):e._e()]),t("div",{staticClass:"divider"}),t("div",{staticClass:"user-info"},[t("div",{staticClass:"info-container",on:{click:function(t){return t.stopPropagation(),e.menuToggle("showToggleLogin")}}},[t("div",{directives:[{name:"lazy",rawName:"v-lazy:backgroundImage",value:e.userinfo.icon,expression:"userinfo.icon",arg:"backgroundImage"}],staticClass:"avatar"}),t("div",{class:[{"no-name":!e.userinfo.name},"name"]},[e._v(e._s(e.userinfo.name))]),t("i",{class:{"caret-reverse":e.showToggleLogin}})]),e.showToggleLogin?t("div",{staticClass:"options"},[e.userinfo.isLogin?[t("span",{on:{click:e.logOut}},[e._v(e._s(e.$t("logout")))]),e.loginToken&&!e.onlyOneRole?t("span",{class:[e.$i18n.locale],on:{click:function(t){return e.openUidListPop(!0)}}},[e._v(e._s(e.$t("switch_character")))]):e._e()]:t("span",{on:{click:function(t){return e.navToLogin(e.$i18n.locale,2031)}}},[e._v(e._s(e.$t("login")))])],2):e._e()])])]),t("div",{staticClass:"content-wrap"},[t("div",{staticClass:"content-title"},[e._v(e._s(e.$t("txt_clear_card_title")))]),t("div",{staticClass:"content-body clear-cache-wrapper"},[t("div",{staticClass:"icon"}),t("div",{staticClass:"txt"},[e._v(e._s(e.$t("txt_clear_card_desc")))]),t("div",{staticClass:"btn",on:{click:e.clearCardCache}},[e._v(e._s(e.$t("txt_clear_card_btn")))])])]),t("div",{staticClass:"content-wrap list-wrap"},[t("div",{staticClass:"content-title"},[e._v(e._s(e.$t("order-page-title")))]),t("div",{directives:[{name:"infinite-scroll",rawName:"v-infinite-scroll",value:()=>!e.isPc&&e.togglePage(),expression:"() => !isPc && togglePage()"}],staticClass:"content-body pc-scroll",attrs:{"infinite-scroll-distance":"100"}},[t("section",{staticClass:"order-list-wrapper"},[e._l(e.orderList,(function(n,i){return[t("div",{key:i,class:["order-item",{"order-item__open":e.activeIndex===i||e.isPc}]},[t("div",{staticClass:"row-1"},[t("div",{staticClass:"order-id"},[e._v(e._s(e.$t("order-page-pay-order"))+"："+e._s(n.order_id))]),t("div",{staticClass:"order-status"},[e._v(e._s(e.$t(e.orderResultMapKey[n.order_status])))])]),t("div",{staticClass:"field"},[e._v(e._s(e.$t("order-page-pay-amount"))+"："+e._s(n.price)+" "+e._s(n.currency))]),t("div",{staticClass:"field"},[e._v(e._s(e.$t("order-page-pay-date"))+"："+e._s(n.created_at))]),t("div",{staticClass:"field"},[e._v(e._s(e.$t("order-page-pay-platform"))+"："+e._s(n.source))]),t("div",{staticClass:"field"},[e._v(e._s(e.$t("order-page-pay-method"))+"："+e._s(n.channel_name))]),t("div",{staticClass:"field"},[e._v(" "+e._s(e.$t("order-page-pay-discount"))+"： "),""===n.act_type?void 0:e._e(),"deduct"===n.act_type?[e._v(e._s(n.discount)+" "+e._s(n.currency))]:e._e(),["fixed_discount","first_pay","coupon"].includes(n.act_type)?[e._v(" "+e._s(e._f("rate")(n.discount))+" OFF")]:e._e()],2),e.isPc?e._e():t("div",{staticClass:"toggle-btn",on:{click:function(t){e.activeIndex=e.activeIndex===i?-1:i}}})])]})),e.orderList.length?e._e():t("div",{staticClass:"no-order-wrapper"},[t("div",{staticClass:"no-order-image"}),t("div",{staticClass:"no-order-txt"},[e._v(e._s(e.$t("nothingHere")))])])],2)])]),e.isPc&&e.totalPages>1?t("footer",[t("paginate",{attrs:{value:e.pageIndex,"page-count":e.totalPages,"click-handler":e.togglePage,"prev-text":"<","next-text":">","container-class":"paginate-wrapper"}})],1):e._e(),e.showToggleUidPop?t("toggle-info",{attrs:{uidList:e.uidList},on:{close:function(t){e.showToggleUidPop=!1},choose:t=>e.loadUserInfo(t)}}):e._e()],1)},a=[],r=(n("d9e2"),n("14d9"),n("e9f5"),n("f665"),n("2b0e")),s=n("8832"),o=n.n(s),c=n("79fa"),l=n("2f62"),d=n("fa7d"),u=n("af82"),p=function(){var e=this,t=e._self._c;return t("div",{staticClass:"cover-bg",on:{click:function(t){return e.$emit("close")}}},[t("section",{staticClass:"toggle-user-info",style:{transform:`translate(-50%, -50%) scale(${1.5*e.$store.state.scaleSize})`},on:{click:function(e){e.stopPropagation()}}},[t("div",{staticClass:"now-info"},[t("div",{directives:[{name:"lazy",rawName:"v-lazy:background-image",value:e.userinfo.icon,expression:"userinfo.icon",arg:"background-image"}],staticClass:"avatar"}),e.userinfo.name?t("div",{staticClass:"id"},[t("i"),t("span",[e._v(e._s(e.userinfo.name))])]):e._e()]),t("div",{staticClass:"info-panel"},[t("div",{staticClass:"info-list"},e._l(e.uidList,(function(n){return t("div",{key:n.uid,class:["info-item",{"info-item__active":e.chosenObj.uid===n.uid}],on:{click:function(t){return e.choose(n)}}},[t("div",{directives:[{name:"lazy",rawName:"v-lazy:background-image",value:n.avatar,expression:"roleItem.avatar",arg:"background-image"}],staticClass:"avatar"}),t("div",{staticClass:"other-info"},[t("div",{staticClass:"row row-1"},[t("div",{staticClass:"id"},[e._v(e._s(n.name))]),t("div",{staticClass:"last-time"},[e._v(e._s(n.last_login))])]),t("div",{staticClass:"row row-2"},[e._v(e._s(e.$t("userinfo_level",{0:n.level}))),t("span"),e._v(e._s(e.$t("userinfo_server",{0:n.server})))])]),e.chosenObj.uid===n.uid||e.userinfo.uid===n.uid?t("div",{staticClass:"active-mark"}):e._e()])})),0),t("div",{class:["toggle-confirm",{"toggle-confirm__disable":!e.chosenObj.uid}],on:{click:e.confirmToggle}},[e._v(e._s(e.$t("switch_character")))])])])])},m=[],h=n("dcd0"),f={name:"toggleInfo",props:["uidList"],data(){return{chosenObj:{}}},computed:{...Object(l["c"])("orderPage",["userinfo"])},methods:{choose(e){return e.uid===this.userinfo.uid?null:e.uid===this.chosenObj.uid?(this.chosenObj={},null):void(this.chosenObj=e)},confirmToggle(){this.chosenObj.uid&&(this.$emit("choose",this.chosenObj.uid),this.$emit("close"),localStorage.removeItem(h["b"]))}}},g=f,v=(n("289c"),n("2877")),y=Object(v["a"])(g,p,m,!1,null,"402e8569",null),_=y.exports,b=n("487a"),w=n.n(b);r["default"].component("paginate",o.a);const C={1:"order-page-status-ok","-1":"order-page-status-pending"};var k={name:"OrderPage",components:{ToggleInfo:_},data(){return{activeIndex:-1,isPc:!0,orderList:[],pageSize:10,pageIndex:1,totalPages:1,showToggleLang:!1,showToggleLogin:!1,showToggleUidPop:!1,langObj:c["b"],uidList:[],onlyOneRole:!0,loginToken:"",orderResultMapKey:C,busy:!1}},filters:{rate(e){return(100*(1-e)).toFixed(0)+"%"}},directives:{infiniteScroll:w.a},methods:{menuToggle(e){if(this[e])return this[e]=!1,null;this.closeAllSlide(),this[e]=!0},toggleLang(e){this.$i18n.locale=e,localStorage.setItem(h["a"],e)},closeAllSlide(){this.showToggleLogin=this.showToggleLang=!1},navToLogin:d["m"],logOut(){localStorage.removeItem(h["b"]),sessionStorage.removeItem(h["c"]),localStorage.removeItem(h["c"]),window.location.href=location.origin+location.pathname},openUidListPop(e){const t=()=>{this.$loading.show();const e={token:this.loginToken};Object(u["f"])(e).then(e=>{const{code:t,data:n}=e;if(sessionStorage.removeItem(h["c"]),!(0===t&&n.uid_list&&n.uid_list.length>=1))throw new Error("get error data.uid_list: "+n.uid_list);this.uidList=n.uid_list,1===n.uid_list.length?this.loadUserInfo(n.uid_list[0].uid):(this.onlyOneRole=!1,this.showToggleUidPop=!0)}).catch(e=>console.error(e)).finally(()=>this.$loading.hide())};return e?t():location.href.includes("token")?void t():(this.loadUserInfo(),null)},loadUserInfo(e){this.closeAllSlide();const t=this.$t("login_fail_2"),n={};e&&(n.uid=e,n.token=this.loginToken);const i=localStorage.getItem(h["b"]);if(!e&&i&&(n.openid=i),"{}"===JSON.stringify(n))return null;this.$loading.show(),Object(u["p"])(n).then(n=>{let{data:a,code:r}=n;if(0===r){try{const e=this.$gcbk("ids.secretKey");"string"===typeof a&&(a=JSON.parse(Object(d["c"])(a,e)))}catch(s){console.error("解密失败！"+(i||this.uid))}this.$store.commit("orderPage/setUserInfo",a),e&&this.resetPage(),this.initList()}else this.$toast.err(t)}).catch(e=>{this.$toast.err(t),console.error(e),setTimeout(()=>this.logOut(),1500)}).finally(()=>this.$loading.hide())},resetPage(){this.orderList=[],this.orderList.length=0,this.totalPages=1,this.pageIndex=1,this.busy=!0},togglePage(e){if(this.isPc)this.pageIndex=e;else{const e=this.pageIndex+1;if(e>this.totalPages)return null;this.pageIndex=e}this.initList()},initList(){this.busy=!0;const e={p0:"web",p1:7,p2:1122,p3:"api",game:"koa",page_size:this.pageSize,page:this.pageIndex,token:localStorage.getItem(h["c"])};Object(u["a"])(e).then(t=>{const{code:n,data:i}=t;if(0===n){const{total:t,result:n=[]}=i;this.isPc?this.orderList=n:this.orderList.push(...n),this.totalPages=Math.ceil(+t/e.page_size)}}).finally(()=>{this.busy=!1})},initPage(){const{openid:e,l:t,token:n}=this.$route.query;if(t&&localStorage.setItem(h["a"],t),n&&sessionStorage.setItem(h["c"],n),n&&localStorage.setItem(h["c"],n),n||e||t)return this.$router.replace("/order");const i=localStorage.getItem(h["b"]),a=localStorage.getItem(h["a"]),r=this.loginToken=sessionStorage.getItem(h["c"]),s=a||this.$i18n.locale,o=Object.keys(c["b"]).find(e=>e===s)||"en";if(this.toggleLang(o),r)return this.openUidListPop(!0);i&&this.loadUserInfo()},clearCardCache(){if(!this.userinfo.isLogin)return void Object(d["m"])(this.$i18n.locale,2031);if(this.busy)return;this.busy=!0;const e={p0:"web",p1:7,p2:1145,p3:"api",game:"koa"};Object(u["b"])(e).then(e=>{0===e.code?this.$toast.err(this.$t("txt_clear_card_tips_suc")):this.$toast.err(this.$t("txt_clear_card_tips_fail"))}).finally(()=>{this.busy=!1})}},computed:{...Object(l["c"])("orderPage",["userinfo"])},created(){this.initPage();const e=()=>{this.isPc=window.innerWidth>940};e(),window.addEventListener("resize",()=>e()),this.$root.$on("bodyClick",()=>this.closeAllSlide())},mounted(){const e=document.querySelector(".pc-scroll");e&&e.addEventListener("scroll",()=>this.closeAllSlide())}},x=k,P=(n("8c3e"),Object(v["a"])(x,i,a,!1,null,"53b6a1ac",null));t["default"]=P.exports},"16ad":function(e,t,n){"use strict";n("14d9"),n("e9f5"),n("ab43");var i=n("afaa");const a={CVC_VERIFICATION_FAILED:"channel-pay-error-cvc",NOT_ENOUGH_MONEY:"channel-pay-error-no-money",UNSAFE_PAYMENT_ENVIRONMENT:"channel-pay-error-environment-unsafe",CARD_MAX_AMOUNT:"channel-pay-error-max-amount",CARD_MAX_PAY_TIMES:"channel-pay-error-max-pay-times",CARD_INVALID_NUMBER:"channel-pay-error-invalid_number",CARD_HAS_EXPIRED:"channel-pay-error-has-expired",NETWORK_ERROR:"channel-pay-error-network-error",TRANSACTION_NOT_ALLOWED:"channel-pay-error-not-allowed",OTHER_ERROR:"channel-pay-error-other"};t["a"]={data(){return{clickPayTimes:Number(sessionStorage.getItem("7x9FkL2pQm")||0)}},methods:{basicShowError(e,t){switch(e){case"ad":{const e={CVC_VERIFICATION_FAILED:{t1:24},NOT_ENOUGH_MONEY:{t1:12,t2:51,t3:[24,25,26,27,28,29,30]},UNSAFE_PAYMENT_ENVIRONMENT:{t1:[20,31,2,14],t2:["05",13,83,59,88]},CARD_MAX_AMOUNT:{t1:28,t2:61},CARD_MAX_PAY_TIMES:{t1:29},CARD_INVALID_NUMBER:{t1:8,t2:[14,15]},CARD_HAS_EXPIRED:{t1:6,t2:54,t3:1},NETWORK_ERROR:{t1:"905_3"},TRANSACTION_NOT_ALLOWED:{t1:[23,22,25],t2:[1,3,12,41,43,46,57,58,62,63,70,82],t3:[3,21]}},n=[];t.refusalReasonCode&&n.push("t1_"+t.refusalReasonCode),t.RawCode&&n.push("t2_"+t.RawCode),t.MacCode&&n.push("t3_"+t.MacCode),this.showMessage(e,n);break}case"cko":{const e={CVC_VERIFICATION_FAILED:{t1:[20087,20100]},NOT_ENOUGH_MONEY:{t1:20051,t3:[24,25,26,27,28,29,30]},UNSAFE_PAYMENT_ENVIRONMENT:{t1:[20001,20002,20003,20005,20012,20046,20059,30004,30020,30034]},CARD_MAX_AMOUNT:{t1:[20061,30021]},CARD_MAX_PAY_TIMES:{t1:[20065,30022]},CARD_INVALID_NUMBER:{t1:[20014,30015]},CARD_HAS_EXPIRED:{t1:30033,t3:1},TRANSACTION_NOT_ALLOWED:{t1:[20057,20091,"2006P",20103,30041,30043,40101],t3:[3,21]}},n=[];t.code&&n.push("t1_"+t.code),t.raw_code&&n.push("t2_"+t.raw_code),t.mac_code&&n.push("t3_"+t.mac_code),this.showMessage(e,n);break}}},showMessage(e,t=[]){if(!t.length)return this.$toast.err(this.$t(a.OTHER_ERROR));for(const n of t){const[t]=n.split("_");for(const[i,r]of Object.entries(e)){const e=r[t],s=Array.isArray(e)?e.map(e=>`${t}_${e}`):[`${t}_${e}`];if(s.includes(n))return this.$toast.err(this.$t(a[i]),4e3)}}this.$toast.err(this.$t(a.OTHER_ERROR),4e3)},async prefetchValidation(e){if(sessionStorage.setItem("7x9FkL2pQm",++this.clickPayTimes),this.clickPayTimes<3)return;const t=JSON.parse(sessionStorage.getItem("3zRtY8vXwN")||"{}");if(!(t.payment_host&&t.order_id&&t.payment_order_id))return;let{payment_host:n,order_id:a,payment_order_id:r}=t;return n+="/api/payment/pay_risk_before",i["d"].post(n,{order_id:r,out_trade_no:a,channel:e}).then(e=>{const{code:t}=e;switch(t){case 0:break;case 120012:return this.$toast.err(this.$t("prefetch-safety-error").replace(/<br\/>/g,"")),sessionStorage.removeItem("ppParams"),setTimeout(()=>this.$router.replace("/"),500),Promise.reject("120012");default:this.$toast.err(this.$t("cb_page_title_err"))}})}},beforeDestroy(){sessionStorage.removeItem("7x9FkL2pQm"),sessionStorage.removeItem("3zRtY8vXwN")}}},"16db":function(e,t){e.exports="data:image/png;base64,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"},"178d":function(e,t,n){"use strict";n("2a81")},"18dc":function(e,t,n){"use strict";n.r(t);var i=function(){var e=this,t=e._self._c;return t("div",{directives:[{name:"show",rawName:"v-show",value:!e.paramIntent,expression:"!paramIntent"}],staticClass:"stripe-page-wrapper"},[e.isPc?t("channel-logo"):e._e(),t("div",{staticClass:"content-wrapper"},[t("channel-order",{attrs:{coin:e.initParams.coinNums,currency:e.initParams.currency_symbol,amount:e.initParams.amount}}),t("channel-wrapper",[t("section",{staticClass:"stripe-wrapper",staticStyle:{"font-size":"15px","text-align":"left"}},[t("div",{staticClass:"inner-wrapper"},[t("form",{attrs:{id:"payment-form"}},[t("div",{attrs:{id:"payment-element"}}),t("button",{staticClass:"stripe-submit",attrs:{id:"submit"},on:{click:function(t){return t.preventDefault(),e.onSubmit.apply(null,arguments)}}},[t("img",{attrs:{src:"https://kg-web-cdn.akamaized.net/prod/web-pay-unique/dist_online/static/img/lock.b2cb557c.svg","aria-hidden":"true"}}),e._v(" Pay "+e._s(e.initParams.currency_symbol)+e._s(e.initParams.amount)+" ")])])])])])],1),e.isMobile?t("channel-logo"):e._e()],1)},a=[],r=(n("e9f5"),n("7d54"),n("88a7"),n("271a"),n("5494"),n("5799")),s=n("82a4"),o=n("1b76"),c=n("2f62"),l=n("afaa");const d="https://js.stripe.com/v3/";var u={name:"stripe",components:{ChannelWrapper:o["a"],ChannelOrder:s["a"],ChannelLogo:r["a"]},computed:{...Object(c["c"])(["isPc","isMobile"])},data(){return{stripe:"",elements:"",initParams:{},paramIntent:""}},methods:{loadScript(){const e=new URLSearchParams(window.location.search);this.paramIntent=e.get("payment_intent_client_secret");const t=document.createElement("script");t.src=d,t.onload=this.paramIntent?this.redirect:this.initForm,document.body.appendChild(t)},async initForm(){const e=window.Stripe(this.initParams.pub_secret_key),t={clientSecret:this.initParams.stripe_client_secret,customerSessionClientSecret:this.initParams.custom_client_secret,appearance:{variables:{colorPrimary:"black",borderRadius:"10px"},rules:{".Input":{border:"1px solid #b9c4c9"},".Input:hover":{border:"1px solid #99a3ad"},".Input:focus":{border:"1px solid #0066ff",boxShadow:"0 0 0 2px #99c2ff"},".CheckboxInput:hover":{border:"1px solid #99a3ad"},".CheckboxInput:focus":{border:"1px solid #0066ff",boxShadow:"0 0 0 2px #99c2ff"}}}},n=e.elements(t),i={layout:{type:"accordion",defaultCollapsed:!1},fields:{billingDetails:{address:"never"}}},a=n.create("payment",i);a.on("loaderror",this.onError),a.mount("#payment-element"),this.stripe=e,this.elements=n},onError(e){this.$toast.err(this.$t("cb_page_pending_desc")),this.$router.go(-1),console.error("stripe init error: "+JSON.stringify(e)),setTimeout(()=>this.$root.$emit("adyenInitError"),200)},async prefetchValidation(e,t){const{code:n}=await l["d"].post(this.initParams.host+"/api/payment/pay_risk_before",{order_id:this.initParams.order_id,out_trade_no:this.initParams.out_trade_no,channel:e,ctoken:t});return n},async onSubmit(){const{error:e}=await this.elements.submit();if(e)return;const{error:t,confirmationToken:n}=await this.stripe.createConfirmationToken({elements:this.elements,params:{payment_method_data:{billing_details:{address:{line1:this.$store.state.country,line2:this.$store.state.country,country:this.$store.state.country,state:this.$store.state.country,city:this.$store.state.country,postal_code:this.$store.state.zipCode}}}}});if(t)return console.error("stripe create token error: "+JSON.stringify(t)),this.$toast.err(this.$t("cb_page_title_err"));const i=await this.prefetchValidation("stripe",n.id);return 120012===i?(this.$toast.err(this.$t("prefetch-safety-error").replace(/<br\/>/g,"")),sessionStorage.removeItem("params"),void setTimeout(()=>this.$router.replace("/"),500)):i?this.$toast.err(this.$t("cb_page_title_err")):void this.stripe.confirmPayment({elements:this.elements,confirmParams:{return_url:location.href,payment_method_data:{billing_details:{address:{line1:this.$store.state.country,line2:this.$store.state.country,country:this.$store.state.country,state:this.$store.state.country,city:this.$store.state.country,postal_code:this.$store.state.zipCode}}}}}).then(e=>{e.error&&(console.error("stripe payment error: "+JSON.stringify(e.error)),this.$toast.err(e.error.message))})},async redirect(){const e=JSON.parse(sessionStorage.getItem("params")||"{}"),t=window.Stripe(e.pub_secret_key),n=new URLSearchParams(window.location.search),i=n.get("payment_intent_client_secret");t.retrievePaymentIntent(i).then(({paymentIntent:e})=>{switch(e.status){case"succeeded":this.$router.replace("/completed?rf=1");break;case"processing":this.$router.replace("/pending?rf=1");break;case"requires_payment_method":this.$router.replace("/fail");break;default:this.$router.replace("/fail");break}}).catch(e=>{console.error("stripe retrieve error: "+JSON.stringify(e));const t=this.initParams.order_id;location.href=`${sessionStorage.getItem("url")}/api/payment/result?channel=stripe&app_id=8519&OrderId=${t}`})}},created(){this.initParams=JSON.parse(sessionStorage.getItem("params")||"{}"),this.loadScript()},beforeDestroy(){const e=d,t=document.getElementsByTagName("script"),n=Array.prototype.slice.call(t);n.forEach((function(t){t.src===e&&t.parentNode.removeChild(t)}))}},p=u,m=(n("a831"),n("2877")),h=Object(m["a"])(p,i,a,!1,null,"1784f58b",null);t["default"]=h.exports},"19cb":function(e,t,n){},"1b76":function(e,t,n){"use strict";var i=function(){var e=this,t=e._self._c;return t("div",{staticClass:"adyen-container"},[e._t("default")],2)},a=[],r={name:"adyen-wrapper"},s=r,o=(n("b39f"),n("2877")),c=Object(o["a"])(s,i,a,!1,null,"494e066e",null);t["a"]=c.exports},"1e0d":function(e,t,n){"use strict";n.r(t);var i=function(){var e=this,t=e._self._c;return t("div",{staticClass:"checkout-page-wrapper"},[e.isPc?t("channel-logo"):e._e(),t("div",{staticClass:"content-wrapper"},[t("channel-order",{attrs:{coin:e.initParams.coinNums,currency:e.initParams.currency_symbol,amount:e.initParams.amount,"in-debt":e.initParams.inDebt}}),t("channel-wrapper",[t("ul",{staticClass:"card-option-list"},[e.historyCard.length?e._l(e.historyCard,(function(n){return t("li",{key:n.key,class:["history-card-item",{active:n.key===e.chosenIndex}],on:{click:function(t){return e.toggle(n.key)}}},[t("div",{staticClass:"card-info"},[t("span",{class:["selected-status",{active:n.key===e.chosenIndex}]}),"amex"===n.cardOrg.toLowerCase()?t("img",{attrs:{src:"https://kg-web-cdn.akamaized.net/prod/web-pay-unique/dist_online/static/img/amex.bf5ac3b0.svg",alt:""}}):e._e(),"jcb"===n.cardOrg.toLowerCase()?t("img",{attrs:{src:"https://kg-web-cdn.akamaized.net/prod/web-pay-unique/dist_online/static/img/jcb.ab1fb383.svg",alt:""}}):e._e(),"mastercard"===n.cardOrg.toLowerCase()?t("img",{attrs:{src:"https://kg-web-cdn.akamaized.net/prod/web-pay-unique/dist_online/static/img/mastercard.1c73bade.svg",alt:""}}):e._e(),"visa"===n.cardOrg.toLowerCase()?t("img",{attrs:{src:"https://kg-web-cdn.akamaized.net/prod/web-pay-unique/dist_online/static/img/visa.ccab0c13.svg",alt:""}}):e._e(),"mada"===n.cardOrg.toLowerCase()?t("img",{attrs:{src:"https://kg-web-cdn.akamaized.net/prod/web-pay-unique_koa/dist_online/static/image/mada.svg",alt:""}}):e._e(),"diners"===n.cardOrg.toLowerCase()?t("img",{attrs:{src:"https://kg-web-cdn.akamaized.net/prod/web-pay-unique_koa/dist_online/static/image/diners.svg",alt:""}}):e._e(),"discover"===n.cardOrg.toLowerCase()?t("img",{attrs:{src:"https://kg-web-cdn.akamaized.net/prod/web-pay-unique_koa/dist_online/static/image/discover.svg",alt:""}}):e._e(),t("span",{staticClass:"card-number"},[e._v("•••• "+e._s(n.cardSummery))])]),e.chosenIndex===n.key?[t("div",{staticClass:"form-row-wrapper"},[t("div",{staticClass:"form-row-item date-wrapper"},[t("span",[e._v("Expiry date")]),t("div",{staticClass:"disabled-input"},[e._v(e._s(n.cardExpiry))])]),t("div",{staticClass:"form-row-item cvc-wrapper"},[t("span",{class:[{"cvc-error":e.showCvcError}]},[e._v("CVC / CVV")]),t("input",{directives:[{name:"model",rawName:"v-model",value:e.cvc,expression:"cvc"}],class:[{"error-cvc_input":e.showCvcError}],attrs:{placeholder:"3 or 4 digits"},domProps:{value:e.cvc},on:{input:[function(t){t.target.composing||(e.cvc=t.target.value)},e.fixCvC],focus:function(t){e.showCvcError=!1}}}),e.showCvcError?e._e():t("div",{staticClass:"cvc-find-position-wrapper",attrs:{dir:"ltr"}},["AMEX"===n.cardOrg?t("img",{attrs:{src:"https://kg-web-cdn.akamaized.net/prod/web-pay-unique/dist_online/static/img/field_cvc_position_tips--front.9b9669cd.svg"}}):t("img",{attrs:{src:"https://kg-web-cdn.akamaized.net/prod/web-pay-unique/dist_online/static/img/field_cvv_position_tips--back.1457d81b.svg"}})]),e.showCvcError?[t("img",{staticClass:"error-cvc__red-no",attrs:{alt:"field_error",src:"https://kg-web-cdn.akamaized.net/prod/web-pay-unique/dist_online/static/img/field_error.5f6b2397.svg"}}),t("span",{staticClass:"error-cvc_span"},[e._v(e._s(e.$t("channel-checkout-cvc-error")))])]:e._e()],2)]),t("button",{on:{click:function(t){return e.payByHistoryCard(n)}}},[t("img",{attrs:{src:"https://kg-web-cdn.akamaized.net/prod/web-pay-unique/dist_online/static/img/lock.b2cb557c.svg",alt:"","aria-hidden":"true"}}),e._v(" Pay "+e._s(e.initParams.currency_symbol)+e._s(e.initParams.amount)+" ")])]:e._e()],2)})):e._e(),t("li",{staticClass:"new-card-item",on:{click:function(t){return e.toggle(e.newCardTxt)}}},[t("div",{staticClass:"card-info"},[t("span",{class:["selected-status",{active:e.newCardTxt===e.chosenIndex}]}),t("img",{attrs:{src:"https://kg-web-cdn.akamaized.net/prod/web-pay-unique/dist_online/static/img/card.f49547d7.svg",alt:""}}),t("span",{staticClass:"card-number"},[e._v("Credit Card")]),e.newCardTxt!==e.chosenIndex?t("div",{staticClass:"support-card-list"},[t("img",{attrs:{src:"https://kg-web-cdn.akamaized.net/prod/web-pay-unique/dist_online/static/img/jcb.ab1fb383.svg",alt:""}}),t("img",{attrs:{src:"https://kg-web-cdn.akamaized.net/prod/web-pay-unique/dist_online/static/img/mastercard.1c73bade.svg",alt:""}}),t("img",{attrs:{src:"https://kg-web-cdn.akamaized.net/prod/web-pay-unique/dist_online/static/img/visa.ccab0c13.svg",alt:""}}),t("span",[e._v("+3")])]):e._e()]),t("div",{directives:[{name:"show",rawName:"v-show",value:e.chosenIndex===e.newCardTxt,expression:"chosenIndex === newCardTxt"}],staticClass:"new-card-wrapper"},[t("section",{staticClass:"checkout-wrapper"},[t("div",{staticClass:"inner-wrapper"},[t("div",{attrs:{id:"payments"}})])]),t("div",{directives:[{name:"show",rawName:"v-show",value:e.checkoutInstance,expression:"checkoutInstance"}],staticClass:"operation"},[t("label",[t("span",[t("input",{directives:[{name:"model",rawName:"v-model",value:e.recordCardNum,expression:"recordCardNum"}],attrs:{type:"checkbox"},domProps:{checked:Array.isArray(e.recordCardNum)?e._i(e.recordCardNum,null)>-1:e.recordCardNum},on:{change:function(t){var n=e.recordCardNum,i=t.target,a=!!i.checked;if(Array.isArray(n)){var r=null,s=e._i(n,r);i.checked?s<0&&(e.recordCardNum=n.concat([r])):s>-1&&(e.recordCardNum=n.slice(0,s).concat(n.slice(s+1)))}else e.recordCardNum=a}}}),e.recordCardNum?t("img",{attrs:{src:"https://kg-web-cdn.akamaized.net/prod/web-pay-unique/dist_online/static/img/duigou.9e50b57c.svg",alt:""}}):e._e()]),e._v(" "+e._s(e.$t("channel-checkout-save-card-number"))+" ")]),t("button",{attrs:{id:"submit"},on:{click:e.payByNewCard}},[t("img",{attrs:{src:"https://kg-web-cdn.akamaized.net/prod/web-pay-unique/dist_online/static/img/lock.b2cb557c.svg",alt:"","aria-hidden":"true"}}),e._v(" Pay "+e._s(e.initParams.currency_symbol)+e._s(e.initParams.amount)+" ")])])])])],2)])],1),e.isMobile?t("channel-logo"):e._e()],1)},a=[];n("e9f5"),n("7d54"),n("ab43");(function(e){e.Production="production",e.Sandbox="sandbox"})({}),function(e){e.Approved="Approved",e.Declined="Declined"}({}),function(e){e.NotEnoughFunds="not_enough_funds",e.InvalidPaymentSessionData="invalid_payment_session_data",e.InvalidCustomerData="invalid_customer_data",e.MerchantMisconfiguration="merchant_misconfiguration",e.TryAgain="try_again"}({}),function(e){e.Request="Request",e.Integration="Integration",e.Submit="Submit"}({}),function(e){e.Title="title",e.Subheading="subheading",e.Body="body",e.Footnote="footnote",e.Button="button",e.Input="input",e.Label="label"}({}),function(e){e.Bancontact="bancontact",e.Card="card",e.Eps="eps",e.Giropay="giropay",e.Applepay="applepay",e.Googlepay="googlepay",e.Ideal="ideal",e.Knet="knet",e.Multibanco="multibanco",e.P24="p24",e.Sofort="sofort",e.Payments="payments"}({});const r="https://checkout-web-components.checkout.com/index.js",s=async e=>{const t=(e=>{const t=document.querySelector(`script[src="${e}"]`);if(t)return t;const n=document.createElement("script");return n.src=e,n.async=!0,document.head.appendChild(n),n})(r);try{const n=await(async e=>new Promise((t,n)=>{e.addEventListener("load",()=>{window.CheckoutWebComponents?t(window.CheckoutWebComponents):n(new Error("CheckoutWebComponents not found on window object."))}),e.addEventListener("error",()=>{n(new Error("CheckoutWebComponents failed to load."))})}))(t);return n(e)}catch(e){throw console.error("CheckoutWebComponents: "+e),e}};var o=n("82a4"),c=n("1b76"),l=n("5799"),d=n("2f62"),u=n("afaa"),p=n("16ad");const m="newCard";var h={name:"checkout",components:{ChannelLogo:l["a"],ChannelWrapper:c["a"],ChannelOrder:o["a"]},mixins:[p["a"]],computed:{...Object(d["c"])(["isPc","isMobile"]),cvcValidated(){const e=this.cvc.length;return e>=3&&e<=4}},watch:{loading(e,t){!0===e&&!1===t&&this.$loading.show(),!1===e&&!0===t&&this.$loading.hide()}},data(){return{checkoutInstance:"",initParams:{},newCardTxt:m,historyCard:[],chosenIndex:-1,cvc:"",showCvcError:!1,recordCardNum:!1,loading:!1,isFirstPayFinished:!0}},methods:{async prepareParams(){let e;try{e=JSON.parse(sessionStorage.getItem("params")||"{}"),this.initParams=e,e.sources&&(this.historyCard=e.sources.map(e=>{const t=Object.keys(e)[0],n=e[t];return{key:t,...n}})),this.historyCard.length?this.chosenIndex=this.historyCard[0].key:this.toggle(m)}catch(t){console.error(t)}},async initCheckout(){const e={zh_tw:"zh-tw",zh_cn:"zh"},t=e[this.$i18n.locale]||this.$i18n.locale,n=this.initParams,i=await s({paymentSession:n.session_data,publicKey:n.client_key,environment:n.env,appearance:{focusOutlineWidth:"0"},locale:t,onReady:()=>this.onReady(),onPaymentCompleted:(e,t)=>this.onPaymentCompleted(e,t),onChange:e=>this.onChange(e),onError:(e,t)=>this.onError(e,t),onSubmit:e=>this.onSubmit(e),componentOptions:{card:{displayCardholderName:"hidden"}}}),a=await i.create("flow",{showPayButton:!1});this.checkoutInstance=a.mount("#payments")},onChange(e){console.log("onChange","isValid: ",e.isValid()," for ",e.type)},onReady(){console.log("onReady")},onSubmit(e){console.log("onSubmit"),this.loading&&(this.loading=!1)},onPaymentCompleted(e,t){this.loading&&(this.loading=!1),this.isFirstPayFinished=!0,this.$router.replace("/completed?ir=cko")},onError(e,t){if(this.loading&&(this.loading=!1),this.isFirstPayFinished=!0,"Integration"===t.type&&t.details&&t.details.includes("PaymentSession Response needs to be provided")&&(console.error("checkout: 组件初始化失败!"),this.$router.go(-1),setTimeout(()=>this.$root.$emit("adyenInitError"),200)),"Submit"===t.type)switch(t.details){case"Payment Method not valid":break}if("Request"===t.type&&200!==t.status&&t.details.paymentId)return this.fetchErrorMessage(t.details.paymentId);console.error("initCheckout: 未知的错误！"+t.message)},fetchErrorMessage(e){if(!this.initParams.ext_detail_url)return null;const t=this.initParams;u["d"].get(t.ext_detail_url,{params:{sid:e}}).then(e=>{const{code:t,data:n}=e;0===t&&this.basicShowError("cko",n)})},fixCvC(e){const t=Number.isNaN(Number(e.data));if(t){const e=String(this.cvc),t=e.length;this.cvc=e.slice(0,t-1)}const n=String(this.cvc);n.length>4&&(this.cvc=n.slice(0,4))},payByHistoryCard(e){if(!this.cvcValidated)return this.showCvcError=!0,null;this.loading=!0;const t=this.initParams;u["d"].post(t.payment_url,{reference:t.reference,source_id:e.key,cvv:this.cvc}).then(e=>{const{code:t,data:n}=e;if(0===t)if(n.response_code)switch(n.response_code){case"10000":this.onPaymentCompleted();break;default:this.basicShowError("cko",n)}else location.href=n.redirect_url}).finally(()=>this.loading=!1)},payByNewCard(){if(this.loading)return null;if(!this.isFirstPayFinished)return null;this.isFirstPayFinished=!1,this.loading=!0;const e=this.initParams;this.recordCardNum&&u["d"].post(e.store_card_url,{reference:e.reference}),this.checkoutInstance.submit()},async toggle(e){this.chosenIndex=e;try{e!==m||this.checkoutInstance||await this.initCheckout()}catch(t){this.onError("",t)}},delJsScript(){const e="https://checkout-web-components.checkout.com/index.js",t=document.getElementsByTagName("script"),n=Array.prototype.slice.call(t);n.forEach((function(t){t.src===e&&t.parentNode.removeChild(t)}))}},created(){this.$store.state.functionSwitch.ckoCheckedByDefault&&(this.recordCardNum=!0),this.prepareParams()},beforeDestroy(){this.delJsScript(),sessionStorage.removeItem("params")}},f=h,g=(n("ebe8"),n("2877")),v=Object(g["a"])(f,i,a,!1,null,"31a3e2e3",null);t["default"]=v.exports},"271a":function(e,t,n){"use strict";var i=n("cb2d"),a=n("e330"),r=n("577e"),s=n("d6d6"),o=URLSearchParams,c=o.prototype,l=a(c.getAll),d=a(c.has),u=new o("a=1");!u.has("a",2)&&u.has("a",void 0)||i(c,"has",(function(e){var t=arguments.length,n=t<2?void 0:arguments[1];if(t&&void 0===n)return d(this,e);var i=l(this,e);s(t,1);var a=r(n),o=0;while(o<i.length)if(i[o++]===a)return!0;return!1}),{enumerable:!0,unsafe:!0})},"289c":function(e,t,n){"use strict";n("967b")},"2a81":function(e,t,n){},"38d3":function(e,t,n){"use strict";n.r(t),n.d(t,"CLSThresholds",(function(){return E})),n.d(t,"FCPThresholds",(function(){return P})),n.d(t,"FIDThresholds",(function(){return M})),n.d(t,"INPThresholds",(function(){return K})),n.d(t,"LCPThresholds",(function(){return Y})),n.d(t,"TTFBThresholds",(function(){return G})),n.d(t,"getCLS",(function(){return T})),n.d(t,"getFCP",(function(){return S})),n.d(t,"getFID",(function(){return $})),n.d(t,"getINP",(function(){return Q})),n.d(t,"getLCP",(function(){return Z})),n.d(t,"getTTFB",(function(){return te})),n.d(t,"onCLS",(function(){return T})),n.d(t,"onFCP",(function(){return S})),n.d(t,"onFID",(function(){return $})),n.d(t,"onINP",(function(){return Q})),n.d(t,"onLCP",(function(){return Z})),n.d(t,"onTTFB",(function(){return te}));var i,a,r,s,o,c=-1,l=function(e){addEventListener("pageshow",(function(t){t.persisted&&(c=t.timeStamp,e(t))}),!0)},d=function(){return window.performance&&performance.getEntriesByType&&performance.getEntriesByType("navigation")[0]},u=function(){var e=d();return e&&e.activationStart||0},p=function(e,t){var n=d(),i="navigate";return c>=0?i="back-forward-cache":n&&(document.prerendering||u()>0?i="prerender":document.wasDiscarded?i="restore":n.type&&(i=n.type.replace(/_/g,"-"))),{name:e,value:void 0===t?-1:t,rating:"good",delta:0,entries:[],id:"v3-".concat(Date.now(),"-").concat(Math.floor(8999999999999*Math.random())+1e12),navigationType:i}},m=function(e,t,n){try{if(PerformanceObserver.supportedEntryTypes.includes(e)){var i=new PerformanceObserver((function(e){Promise.resolve().then((function(){t(e.getEntries())}))}));return i.observe(Object.assign({type:e,buffered:!0},n||{})),i}}catch(e){}},h=function(e,t,n,i){var a,r;return function(s){t.value>=0&&(s||i)&&((r=t.value-(a||0))||void 0===a)&&(a=t.value,t.delta=r,t.rating=function(e,t){return e>t[1]?"poor":e>t[0]?"needs-improvement":"good"}(t.value,n),e(t))}},f=function(e){requestAnimationFrame((function(){return requestAnimationFrame((function(){return e()}))}))},g=function(e){var t=function(t){"pagehide"!==t.type&&"hidden"!==document.visibilityState||e(t)};addEventListener("visibilitychange",t,!0),addEventListener("pagehide",t,!0)},v=function(e){var t=!1;return function(n){t||(e(n),t=!0)}},y=-1,_=function(){return"hidden"!==document.visibilityState||document.prerendering?1/0:0},b=function(e){"hidden"===document.visibilityState&&y>-1&&(y="visibilitychange"===e.type?e.timeStamp:0,C())},w=function(){addEventListener("visibilitychange",b,!0),addEventListener("prerenderingchange",b,!0)},C=function(){removeEventListener("visibilitychange",b,!0),removeEventListener("prerenderingchange",b,!0)},k=function(){return y<0&&(y=_(),w(),l((function(){setTimeout((function(){y=_(),w()}),0)}))),{get firstHiddenTime(){return y}}},x=function(e){document.prerendering?addEventListener("prerenderingchange",(function(){return e()}),!0):e()},P=[1800,3e3],S=function(e,t){t=t||{},x((function(){var n,i=k(),a=p("FCP"),r=m("paint",(function(e){e.forEach((function(e){"first-contentful-paint"===e.name&&(r.disconnect(),e.startTime<i.firstHiddenTime&&(a.value=Math.max(e.startTime-u(),0),a.entries.push(e),n(!0)))}))}));r&&(n=h(e,a,P,t.reportAllChanges),l((function(i){a=p("FCP"),n=h(e,a,P,t.reportAllChanges),f((function(){a.value=performance.now()-i.timeStamp,n(!0)}))})))}))},E=[.1,.25],T=function(e,t){t=t||{},S(v((function(){var n,i=p("CLS",0),a=0,r=[],s=function(e){e.forEach((function(e){if(!e.hadRecentInput){var t=r[0],n=r[r.length-1];a&&e.startTime-n.startTime<1e3&&e.startTime-t.startTime<5e3?(a+=e.value,r.push(e)):(a=e.value,r=[e])}})),a>i.value&&(i.value=a,i.entries=r,n())},o=m("layout-shift",s);o&&(n=h(e,i,E,t.reportAllChanges),g((function(){s(o.takeRecords()),n(!0)})),l((function(){a=0,i=p("CLS",0),n=h(e,i,E,t.reportAllChanges),f((function(){return n()}))})),setTimeout(n,0))})))},I={passive:!0,capture:!0},L=new Date,D=function(e,t){i||(i=t,a=e,r=new Date,O(removeEventListener),N())},N=function(){if(a>=0&&a<r-L){var e={entryType:"first-input",name:i.type,target:i.target,cancelable:i.cancelable,startTime:i.timeStamp,processingStart:i.timeStamp+a};s.forEach((function(t){t(e)})),s=[]}},A=function(e){if(e.cancelable){var t=(e.timeStamp>1e12?new Date:performance.now())-e.timeStamp;"pointerdown"==e.type?function(e,t){var n=function(){D(e,t),a()},i=function(){a()},a=function(){removeEventListener("pointerup",n,I),removeEventListener("pointercancel",i,I)};addEventListener("pointerup",n,I),addEventListener("pointercancel",i,I)}(t,e):D(t,e)}},O=function(e){["mousedown","keydown","touchstart","pointerdown"].forEach((function(t){return e(t,A,I)}))},M=[100,300],$=function(e,t){t=t||{},x((function(){var n,r=k(),o=p("FID"),c=function(e){e.startTime<r.firstHiddenTime&&(o.value=e.processingStart-e.startTime,o.entries.push(e),n(!0))},d=function(e){e.forEach(c)},u=m("first-input",d);n=h(e,o,M,t.reportAllChanges),u&&g(v((function(){d(u.takeRecords()),u.disconnect()}))),u&&l((function(){var r;o=p("FID"),n=h(e,o,M,t.reportAllChanges),s=[],a=-1,i=null,O(addEventListener),r=c,s.push(r),N()}))}))},R=0,j=1/0,F=0,U=function(e){e.forEach((function(e){e.interactionId&&(j=Math.min(j,e.interactionId),F=Math.max(F,e.interactionId),R=F?(F-j)/7+1:0)}))},W=function(){return o?R:performance.interactionCount||0},z=function(){"interactionCount"in performance||o||(o=m("event",U,{type:"event",buffered:!0,durationThreshold:0}))},K=[200,500],V=0,B=function(){return W()-V},H=[],q={},J=function(e){var t=H[H.length-1],n=q[e.interactionId];if(n||H.length<10||e.duration>t.latency){if(n)n.entries.push(e),n.latency=Math.max(n.latency,e.duration);else{var i={id:e.interactionId,latency:e.duration,entries:[e]};q[i.id]=i,H.push(i)}H.sort((function(e,t){return t.latency-e.latency})),H.splice(10).forEach((function(e){delete q[e.id]}))}},Q=function(e,t){t=t||{},x((function(){var n;z();var i,a=p("INP"),r=function(e){e.forEach((function(e){e.interactionId&&J(e),"first-input"===e.entryType&&!H.some((function(t){return t.entries.some((function(t){return e.duration===t.duration&&e.startTime===t.startTime}))}))&&J(e)}));var t,n=(t=Math.min(H.length-1,Math.floor(B()/50)),H[t]);n&&n.latency!==a.value&&(a.value=n.latency,a.entries=n.entries,i())},s=m("event",r,{durationThreshold:null!==(n=t.durationThreshold)&&void 0!==n?n:40});i=h(e,a,K,t.reportAllChanges),s&&("PerformanceEventTiming"in window&&"interactionId"in PerformanceEventTiming.prototype&&s.observe({type:"first-input",buffered:!0}),g((function(){r(s.takeRecords()),a.value<0&&B()>0&&(a.value=0,a.entries=[]),i(!0)})),l((function(){H=[],V=W(),a=p("INP"),i=h(e,a,K,t.reportAllChanges)})))}))},Y=[2500,4e3],X={},Z=function(e,t){t=t||{},x((function(){var n,i=k(),a=p("LCP"),r=function(e){var t=e[e.length-1];t&&t.startTime<i.firstHiddenTime&&(a.value=Math.max(t.startTime-u(),0),a.entries=[t],n())},s=m("largest-contentful-paint",r);if(s){n=h(e,a,Y,t.reportAllChanges);var o=v((function(){X[a.id]||(r(s.takeRecords()),s.disconnect(),X[a.id]=!0,n(!0))}));["keydown","click"].forEach((function(e){addEventListener(e,(function(){return setTimeout(o,0)}),!0)})),g(o),l((function(i){a=p("LCP"),n=h(e,a,Y,t.reportAllChanges),f((function(){a.value=performance.now()-i.timeStamp,X[a.id]=!0,n(!0)}))}))}}))},G=[800,1800],ee=function e(t){document.prerendering?x((function(){return e(t)})):"complete"!==document.readyState?addEventListener("load",(function(){return e(t)}),!0):setTimeout(t,0)},te=function(e,t){t=t||{};var n=p("TTFB"),i=h(e,n,G,t.reportAllChanges);ee((function(){var a=d();if(a){var r=a.responseStart;if(r<=0||r>performance.now())return;n.value=Math.max(r-u(),0),n.entries=[a],i(!0),l((function(){n=p("TTFB",0),(i=h(e,n,G,t.reportAllChanges))(!0)}))}}))}},"3ff4":function(e,t,n){},"487a":function(e,t,n){(function(t,n){e.exports=n()})(0,(function(){"use strict";var e="@@InfiniteScroll",t=function(e,t){var n,i,a,r,s,o=function(){e.apply(r,s),i=n};return function(){if(r=this,s=arguments,n=Date.now(),a&&(clearTimeout(a),a=null),i){var e=t-(n-i);e<0?o():a=setTimeout((function(){o()}),e)}else o()}},n=function(e){return e===window?Math.max(window.pageYOffset||0,document.documentElement.scrollTop):e.scrollTop},i=document.defaultView.getComputedStyle,a=function(e){var t=e;while(t&&"HTML"!==t.tagName&&"BODY"!==t.tagName&&1===t.nodeType){var n=i(t).overflowY;if("scroll"===n||"auto"===n)return t;t=t.parentNode}return window},r=function(e){return e===window?document.documentElement.clientHeight:e.clientHeight},s=function(e){return e===window?n(window):e.getBoundingClientRect().top+n(window)},o=function(e){var t=e.parentNode;while(t){if("HTML"===t.tagName)return!0;if(11===t.nodeType)return!1;t=t.parentNode}return!1},c=function(){if(!this.binded){this.binded=!0;var e=this,n=e.el,i=n.getAttribute("infinite-scroll-throttle-delay"),r=200;i&&(r=Number(e.vm[i]||i),(isNaN(r)||r<0)&&(r=200)),e.throttleDelay=r,e.scrollEventTarget=a(n),e.scrollListener=t(l.bind(e),e.throttleDelay),e.scrollEventTarget.addEventListener("scroll",e.scrollListener),this.vm.$on("hook:beforeDestroy",(function(){e.scrollEventTarget.removeEventListener("scroll",e.scrollListener)}));var s=n.getAttribute("infinite-scroll-disabled"),o=!1;s&&(this.vm.$watch(s,(function(t){e.disabled=t,!t&&e.immediateCheck&&l.call(e)})),o=Boolean(e.vm[s])),e.disabled=o;var c=n.getAttribute("infinite-scroll-distance"),d=0;c&&(d=Number(e.vm[c]||c),isNaN(d)&&(d=0)),e.distance=d;var u=n.getAttribute("infinite-scroll-immediate-check"),p=!0;u&&(p=Boolean(e.vm[u])),e.immediateCheck=p,p&&l.call(e);var m=n.getAttribute("infinite-scroll-listen-for-event");m&&e.vm.$on(m,(function(){l.call(e)}))}},l=function(e){var t=this.scrollEventTarget,i=this.el,a=this.distance;if(!0===e||!this.disabled){var o=n(t),c=o+r(t),l=!1;if(t===i)l=t.scrollHeight-c<=a;else{var d=s(i)-s(t)+i.offsetHeight+o;l=c+a>=d}l&&this.expression&&this.expression()}},d={bind:function(t,n,i){t[e]={el:t,vm:i.context,expression:n.value};var a=arguments;t[e].vm.$on("hook:mounted",(function(){t[e].vm.$nextTick((function(){o(t)&&c.call(t[e],a),t[e].bindTryCount=0;var n=function n(){t[e].bindTryCount>10||(t[e].bindTryCount++,o(t)?c.call(t[e],a):setTimeout(n,50))};n()}))}))},unbind:function(t){t&&t[e]&&t[e].scrollEventTarget&&t[e].scrollEventTarget.removeEventListener("scroll",t[e].scrollListener)}},u=function(e){e.directive("InfiniteScroll",d)};return window.Vue&&(window.infiniteScroll=d,Vue.use(u)),d.install=u,d}))},"495c":function(e,t,n){"use strict";n.r(t);var i=function(){var e=this,t=e._self._c;return t("div",{staticClass:"pingpong-page-wrapper"},[e.isPc?t("channel-logo"):e._e(),t("div",{staticClass:"content-wrapper"},[t("channel-order",{attrs:{coin:e.initParams.coinNums,currency:e.initParams.currency_symbol,amount:e.initParams.amount,"in-debt":e.initParams.inDebt}}),t("channel-wrapper",[t("section",{staticClass:"pingpong-wrapper"},[t("div",{staticClass:"inner-wrapper"},[t("pp-funplus-checkout",{attrs:{savepay:e.savePay,accessToken:e.initParams.ppToken}})],1)])])],1),e.isMobile?t("channel-logo"):e._e()],1)},a=[],r=n("2f62"),s=n("82a4"),o=n("1b76"),c=n("5799"),l=n("16ad"),d={name:"pingpong",components:{ChannelLogo:c["a"],ChannelWrapper:o["a"],ChannelOrder:s["a"]},mixins:[l["a"]],computed:{...Object(r["c"])(["isPc","isMobile"]),savePay(){return this.$store.state.functionSwitch.ckoCheckedByDefault?"Y":"N"}},data(){return{initParams:{}}},methods:{loadPingpongScript(){const e="https://payssr-cdn.pingpongx.com/production-fra/acquirer-checkout-funplus/pp-funplus-checkout.js",t=document.createElement("script");t.src=e,t.type="module",t.onload=this.onScriptLoad,document.body.appendChild(t)},onScriptLoad(){const e=window.PingPong;e.Checkout.initializedHook=e=>{e?this.onReady(e):this.onCmpError()},e.Checkout.beforeCheckoutHook=async()=>this.prefetchValidation("pingpong"),this.initParams=JSON.parse(sessionStorage.getItem("ppParams")||"{}")},onReady(){console.log("cmp ready!")},onCmpError(){this.$router.go(-1),setTimeout(()=>this.$root.$emit("adyenInitError"),200)}},created(){this.loadPingpongScript()}},u=d,p=(n("5584"),n("2877")),m=Object(p["a"])(u,i,a,!1,null,null,null);t["default"]=m.exports},"4f6d":function(e,t,n){"use strict";n.d(t,"a",(function(){return a}));var i=n("72c2");function a(){const e=document.createElement("a");e.href=i["a"].ios()?window.$gcbk("gameinfo.appGameDeepLinkIos"):window.$gcbk("gameinfo.appGameDeepLinkAndroid"),e.style.display="none",document.body.appendChild(e),e.click(),document.body.removeChild(e)}},5208:function(e,t,n){"use strict";n("b9c7")},5494:function(e,t,n){"use strict";var i=n("83ab"),a=n("e330"),r=n("edd0"),s=URLSearchParams.prototype,o=a(s.forEach);i&&!("size"in s)&&r(s,"size",{get:function(){var e=0;return o(this,(function(){e++})),e},configurable:!0,enumerable:!0})},5584:function(e,t,n){"use strict";n("19cb")},5799:function(e,t,n){"use strict";var i=function(){var e=this;e._self._c;return e._m(0)},a=[function(){var e=this,t=e._self._c;return t("div",{staticClass:"image-wrapper"},[t("img",{attrs:{src:n("16db"),alt:""}})])}],r={name:"channelLogo"},s=r,o=(n("ba9a"),n("2877")),c=Object(o["a"])(s,i,a,!1,null,"5ba3ee7d",null);t["a"]=c.exports},"82a4":function(e,t,n){"use strict";var i=function(){var e=this,t=e._self._c;return t("div",{staticClass:"info-wrapper",staticStyle:{"text-align":"left"}},[t("div",{staticClass:"title"},[e._v(e._s(e.$t("adyen-order-details")))]),t("p",[e._v(e._s(e.$t("adyen-order-info"))+" "),e.goodsName?t("span",{staticClass:"sdk-products-name"},[e._v(e._s(e.goodsName))]):t("span",{staticClass:"token-name"},[e._v(e._s(e.coin)+" "+e._s(e.$vt("tokenName")))])]),t("p",[e._v(e._s(e.$t("totalPrice"))+" "),t("span",[e._v(e._s(e.currency)+" "+e._s(e.amount))])]),e.showForm?t("div",{staticClass:"form"},[t("div",{staticClass:"form-title"},[e._v(e._s(e.$t("channel-order-info")))]),t("div",{staticClass:"divider"}),t("div",{staticClass:"form-item"},[t("div",{staticClass:"label"},[e._v(e._s(e.$t("label-zipcode")))]),t("div",{staticClass:"content"},[t("i"),t("input",{directives:[{name:"model",rawName:"v-model",value:e.zipCode,expression:"zipCode"}],attrs:{type:"text",placeholder:e.$t("label-zipcode")},domProps:{value:e.zipCode},on:{change:e.change,input:[function(t){t.target.composing||(e.zipCode=t.target.value)},function(t){return e.fixInput()}]}})])])]):e._e()])},a=[],r=n("afaa"),s={name:"orderInfo",props:["coin","amount","currency"],data(){const e=this.$store.state.zipCode,t=window.__showEmailForm;return{zipCode:e,showForm:t,goodsName:sessionStorage.getItem("goodsName")}},methods:{change(){const e=sessionStorage.getItem("id_sign"),t=sessionStorage.getItem("url")+"/api/payment/save_billingaddress";e&&sessionStorage.getItem("url")&&this.zipCode&&(this.zipCode.length>15&&(this.zipCode=this.zipCode.slice(0,15)),r["d"].post(t,{zipcode:this.zipCode,order_id:e}))},fixInput(){"US"===this.$store.state.country&&(this.zipCode=this.zipCode.replace(/[^0-9]/g,""))}}},o=s,c=(n("5208"),n("2877")),l=Object(c["a"])(o,i,a,!1,null,"3768ed97",null);t["a"]=l.exports},8390:function(e,t,n){"use strict";var i=Object.defineProperty,a=Object.defineProperties,r=Object.getOwnPropertyDescriptor,s=Object.getOwnPropertyDescriptors,o=Object.getOwnPropertyNames,c=Object.getOwnPropertySymbols,l=Object.prototype.hasOwnProperty,d=Object.prototype.propertyIsEnumerable,u=(e,t,n)=>t in e?i(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,p=(e,t)=>{for(var n in t||(t={}))l.call(t,n)&&u(e,n,t[n]);if(c)for(var n of c(t))d.call(t,n)&&u(e,n,t[n]);return e},m=(e,t)=>a(e,s(t)),h=(e,t)=>{for(var n in t)i(e,n,{get:t[n],enumerable:!0})},f=(e,t,n,a)=>{if(t&&"object"===typeof t||"function"===typeof t)for(let s of o(t))l.call(e,s)||s===n||i(e,s,{get:()=>t[s],enumerable:!(a=r(t,s))||a.enumerable});return e},g=e=>f(i({},"__esModule",{value:!0}),e),v=(e,t,n)=>new Promise((i,a)=>{var r=e=>{try{o(n.next(e))}catch(t){a(t)}},s=e=>{try{o(n.throw(e))}catch(t){a(t)}},o=e=>e.done?i(e.value):Promise.resolve(e.value).then(r,s);o((n=n.apply(e,t)).next())}),y={};h(y,{Environment:()=>P,NetworkType:()=>S,PlatformType:()=>x,SeverityType:()=>k,default:()=>_e}),e.exports=g(y);var _,b,w,C,k=(e=>(e["ERROR"]="error",e["WARN"]="warn",e["INFO"]="info",e["AUTO_DETECT_ERROR"]="autoDetectError",e["PERFORMANCE"]="performance",e["SPEED"]="speed",e["AUTO_DETECT_EVENT"]="autoDetectEvent",e))(k||{}),x=(e=>(e["android"]="android",e["ios"]="ios",e["windows"]="windows",e["macos"]="macos",e["linux"]="linux",e["other"]="other",e))(x||{}),P=(e=>(e["production"]="prod",e["demo"]="demo",e["staging"]="staging",e))(P||{}),S=(e=>(e["unknown"]="unknown",e["wifi"]="wifi",e["net2g"]="net2g",e["net3g"]="net3g",e["net4g"]="net4g",e["net5g"]="net5g",e))(S||{}),E="airTracker_unknown",T={appName:E,appVersion:E,env:"staging",isWebappContainer:!1,delay:2e3,errorRepeatTime:3,enableErrorMonitoring:!1,enableWebVitals:!1,assetSpeedMonitoringWhiteList:[],enableDetectPageChange:!1,assetSpeedMonitoringWhiteListByMFE:{}},I={appName:E,env:"staging",sessionId:E,deviceId:E,platform:"other"},L=e=>!!e&&(!/^\s*$/.test(e)&&(e=e.replace(/\\(?:["\\\/bfnrt]|u[0-9a-fA-F]{4})/g,"@"),e=e.replace(/"[^"\\\n\r]*"|true|false|null|-?\d+(?:\.\d*)?(?:[eE][+\-]?\d+)?/g,"]"),e=e.replace(/(?:^|:|,)(?:\s*\[)+/g,""),/^[\],:{}\s]*$/.test(e))),D=()=>{const e=new WeakSet;return(t,n)=>{if(n instanceof Error)return`Error.message: ${n.message} \n  Error.stack: ${n.stack}`;if("object"===typeof n&&null!==n){if(e.has(n))return`[Circular ${t||"root"}]`;e.add(n)}return"function"===typeof n?"function":"symbol"===typeof n?"symbol":"undefined"===typeof n?null:n}},N=e=>{if("string"===typeof e)return e;try{return e instanceof Error?(JSON.stringify(e,D())||"undefined").replace(/"/gim,""):JSON.stringify(e,D())}catch(t){return'{"error":"error happen when airTracker stringify"}'}},A=({method:e="post",url:t,data:n,success:i,fail:a})=>{if(!L(n))return;const r=new XMLHttpRequest;r.addEventListener("readystatechange",()=>{4===r.readyState&&(r.status>=400||0===r.status?null==a||a(r.response):null==i||i(r.response))}),r.open(e,t),r.setRequestHeader("Content-Type","application/json"),r.send(n)},O={onInit:"onInit",onConfigInit:"onConfigInit",onConfigUpdated:"onConfigUpdated",onCommonDataInit:"onCommonDataInit",onCommonUpdated:"onCommonUpdated",onPageChange:"onPageChange",onDestroy:"onDestroy,"},M="AIR_ANALYTICS_DEVICE_ID",$=e=>"prod"==e?"https://api.airwallex.com/papluginlogs/cors-logs":"demo"==e?"https://api-demo.airwallex.com/papluginlogs/cors-logs":"https://api-staging.airwallex.com/papluginlogs/cors-logs",R=e=>"prod"==e?"https://api.airwallex.com/papluginlogs/logs":"demo"==e?"https://api-demo.airwallex.com/papluginlogs/logs":"https://api-staging.airwallex.com/papluginlogs/logs",j=()=>{const e="xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,e=>{const t=16*Math.random()|0,n="x"===e?t:3&t|8;return n.toString(16)});return e},F=()=>{var e,t;return(null==(t=null==(e=null==location?void 0:location.pathname)?void 0:e.split("/"))?void 0:t[2])||"unknown"},U=()=>{try{let e=window.localStorage.getItem(M);return e||(e=j(),window.localStorage.setItem(M,e)),e}catch(e){}},W=()=>{const e={android:/\bAndroid\s*([^;]+)/,ios:/\b(iPad|iPhone|iPod)\b.*? OS ([\d_]+)/,windows:/\b(Windows NT)/,macos:/\b(Mac OS)/,linux:/\b(Linux)/i},t=t=>e[t].test(navigator.userAgent),n=Object.keys(e).find(t);return n?x[n]:"other"},z=()=>/^[a-zA-Z0-9.-]*.airwallex.com$/g.test(window.location.host),K=()=>{var e,t;let n="";const i=navigator.userAgent.match(/NetType\/(\w+)/);return i?[,n]=i:(null==navigator?void 0:navigator.connection)&&(n=(null==(e=null==navigator?void 0:navigator.connection)?void 0:e.effectiveType)||(null==(t=null==navigator?void 0:navigator.connection)?void 0:t.type)),n||(n="unknown"),V(n)},V=e=>(e=String(e).toLowerCase(),e.indexOf("4g")>=0?"net4g":e.indexOf("wifi")>=0?"wifi":e.indexOf("5g")>=0?"net5g":e.indexOf("3g")>=0?"net3g":e.indexOf("2g")>=0?"net2g":"unknown"),B=function(){return"undefined"!==typeof window.performance&&"function"===typeof window.performance.clearResourceTimings&&"function"===typeof window.performance.getEntriesByType&&"function"===typeof window.performance.now},H=function(){return"function"===typeof window.PerformanceObserver},q=e=>{if("string"===typeof e){const t=e.split("?")[0]||"";return t.slice(0,200)}return e},J=e=>"string"===typeof e?e.split("?")[1]||"":e,Q=e=>{const t="string"===typeof e&&e.startsWith("//");return t?"undefined"!==typeof location&&"https:"===location.protocol:/^https/.test(e)},Y=(e,t)=>"number"===typeof e||"string"===typeof e?e:t?"":-1,X=(e,t)=>{var n;let i=!1;if(!t.config.isWebappContainer)return i=Z(e,t.config.assetSpeedMonitoringWhiteList),i;const a=F();return i||Z(e,null==(n=t.config.assetSpeedMonitoringWhiteListByMFE)?void 0:n[a])},Z=(e,t)=>{if(!(null==t?void 0:t.length))return!1;for(let n=0;n<t.length;n++){const i=t[n];if(i instanceof RegExp&&i.test(e))return!0;if("string"==typeof i&&e.includes(i))return!0}return!1},G=e=>t=>{var n;const i=e.config.isWebappContainer?p(p({},e.commonData||{}),(null==(n=e.MFECommonDataMap)?void 0:n[F()])||{}):e.commonData,a={commonData:i,data:Array.isArray(t)?t:[t]},r=N(a),s={method:"post",url:z()?$(e.commonData.env):R(e.commonData.env),data:r};A(s)},ee=(e,t)=>{let n;const i=[],{config:a}=e;return(r,s)=>{if(i.push(r),e.lifeCycle.on(O.onCommonUpdated,()=>{i.length>0&&(null==s||s(i.splice(0,i.length)),e.lifeCycle.remove(O.onCommonUpdated),n&&clearTimeout(n))}),t&&i.length>=t)return null==s||s(i.splice(0,i.length)),void(n&&clearTimeout(n));n&&clearTimeout(n),n=setTimeout(()=>{n=null,i.length>0&&(null==s||s(i.splice(0,i.length)),e.lifeCycle.remove(O.onCommonUpdated))},a.delay)}},te=e=>{const t={};return(n,i)=>{const a="number"===typeof e.config.errorRepeatTime?e.config.errorRepeatTime:5;if(0===a)return null==i?void 0:i(n);null==i||i(n.filter(e=>"autoDetectError"!=e.severity||(t[e.error]=t[e.error]||0,t[e.error]+=1,!(t[e.error]>a))))}},ne=()=>{},ie=e=>{if(!e||!e.reduce||!e.length)throw new TypeError("createPipeline need at least one function param");return 1===e.length?(t,n)=>{e[0](t,n||ne)}:e.reduce((e,t)=>(n,i=ne)=>e(n,e=>null==t?void 0:t(e,i)))},ae=class{constructor(){this.emit=(e,t)=>{if(!this)return;let n,i=this.eventsList[e];if(null==i?void 0:i.length){i=i.slice();for(let r=0;r<i.length;r++){n=i[r];try{const i=n.callback.apply(this,[t]);if(1===n.type&&this.remove(e,n.callback),!1===i)break}catch(a){throw a}}}return this},this.eventsList={}}indexOf(e,t){for(let n=0;n<e.length;n++)if(e[n].callback===t)return n;return-1}on(e,t,n=0){if(!this)return;let i=this.eventsList[e];if(i||(this.eventsList[e]=[],i=this.eventsList[e]),-1===this.indexOf(i,t)){const a={name:e,type:n||0,callback:t};return i.push(a),this}return this}one(e,t){this.on(e,t,1)}remove(e,t){if(!this)return;const n=this.eventsList[e];if(!n)return null;if(!t){try{delete this.eventsList[e]}catch(i){}return null}if(n.length){const e=this.indexOf(n,t);n.splice(e,1)}return this}clear(){this.eventsList={}}},re=class{constructor(e){this.name="",this.isInit=!1,this.name=e.name,this.option=e}patch(e){var t,n;this.isInit||(this.isInit=!0,null==(n=null==(t=this.option)?void 0:t.setUp)||n.call(this.option,e))}uninstall(){var e,t;null==(t=null==(e=this.option)?void 0:e.destroy)||t.apply(this),this.isInit=!1}},se=e=>{const t=`${N(e.message)||""} @ (${N(e.filename)||""}:${e.lineno||0}:${e.colno||0})\n${N(e.error||"")}`;_.normalLogPipeLine({severity:"autoDetectError",eventName:"windowOnError",extraInfo:{error:t}})},oe=e=>{const t=e&&N(e.reason);_.normalLogPipeLine({severity:"autoDetectError",eventName:"promiseError",extraInfo:{error:t}})},ce=e=>{const t=(null==e?void 0:e.target)||(null==e?void 0:e.srcElement);if(!t)return;const n=(null==t?void 0:t.src)||(null==t?void 0:t.href),{tagName:i}=t;let a="unknown";if("string"===typeof n&&i){if(window.location.href.indexOf(n)>-1)return;if(/\.js$/.test(n))a="script";else if(/\.css$/.test(n))a="css";else switch(i.toLowerCase()){case"script":a="script";break;case"link":a="css";break;case"img":a="image";break;case"audio":case"video":a="media";break;default:return}_.normalLogPipeLine({severity:"autoDetectError",eventName:"staticFileLoadError",extraInfo:{staticFileType:a,error:`${i} load fail: ${n}`}})}},le=new re({name:"errorDetectionPlugin",setUp:e=>{_=e,window.addEventListener("error",se),window.addEventListener("unhandledrejection",oe),window.document.addEventListener("error",ce,!0)},destroy:()=>{window.removeEventListener("unhandledrejection",oe),window.document.removeEventListener("error",ce,!0),window.removeEventListener("error",se)}}),de=n("38d3"),ue=new re({name:"webVitalsPlugin",setUp:e=>v(void 0,null,(function*(){if(!B()||!H())return;const t=t=>{const{name:n,navigationType:i,rating:a,value:r}=t;e.normalLogPipeLine({severity:"performance",eventName:n,extraInfo:{log:{navigationType:i,rating:a,value:r}}})};(0,de.onCLS)(t),(0,de.onFID)(t),(0,de.onLCP)(t)}))}),pe=["img","css","script","link","audio","video","iframe"],me="resource",he=e=>{const t=e.name;return{url:q(t),method:"get",duration:Number(e.duration.toFixed(2)),type:"static",isHttps:Q(t),urlQuery:J(t),domainLookup:Y(e.domainLookupEnd-e.domainLookupStart),connectTime:Y(e.connectEnd-e.connectStart)}},fe=(e,t)=>{for(let n=0,i=e.length;n<i;n++){const i=e[n];-1!==pe.indexOf(i.initiatorType)&&X(i.name,t)&&t.normalLogPipeLine({severity:"performance",eventName:"assets_speed",extraInfo:{log:he(i)}})}},ge=new re({name:"assetsSpeedPlugin",setUp:e=>{if(!B())return;let t=0;window.performance.onresourcetimingbufferfull=()=>{t=0,window.performance.clearResourceTimings()},"function"===typeof window.PerformanceObserver?(fe(window.performance.getEntriesByType(me),e),w=new window.PerformanceObserver(t=>{fe(t.getEntries(),e)}),w.observe({entryTypes:[me]})):b=setInterval(()=>{const n=window.performance.getEntriesByType(me),i=n.slice(t);t=n.length,fe(i,e)},3e3)},destroy:()=>{null==w||w.disconnect(),b&&clearInterval(b)}}),ve=new re({name:"onPageChangePlugin",setUp:e=>v(void 0,null,(function*(){let t=null==location?void 0:location.href;const n=document.querySelector("body");C=new MutationObserver(()=>{if(location.href!==t){const n=t;t=location.href,e.normalLogPipeLine({severity:"autoDetectEvent",eventName:"onPageChange",extraInfo:{prevHref:n,href:(null==location?void 0:location.href)||"",hostname:null==location?void 0:location.hostname,pathName:null==location?void 0:location.pathname,protocol:null==location?void 0:location.protocol,search:null==location?void 0:location.search}})}});const i={subtree:!0,childList:!0};C.observe(n||document,i)})),destroy:()=>{null==C||C.disconnect()}}),ye=class{constructor({config:e,plugins:t=[]}){this.config=T,this.lifeCycle=new ae,this.plugins=[],this._commonData=I,this._MFECommonDataMap={},this.timeMap={},this.normalPipelineObj=ie([ee(this,8),te(this),G(this)]),this.normalLogPipeLine=({severity:e,eventName:t,extraInfo:n})=>{const i=p({severity:e,eventName:t,currentHref:(null==location?void 0:location.href)||"unknown href"},n);return this.config.isWebappContainer&&(i.MFEName=F()),this.normalPipelineObj(i)},this.plugins=[...t],this.setConfig(e),this.initCommonData(e),this.lifeCycle.emit(O.onInit),e.enableErrorMonitoring&&this.plugins.push(le),e.enableWebVitals&&this.plugins.push(ue),((null==e?void 0:e.assetSpeedMonitoringWhiteList)&&e.assetSpeedMonitoringWhiteList.length>0||e.isWebappContainer)&&this.plugins.push(ge),(null==e?void 0:e.enableDetectPageChange)&&this.plugins.push(ve),this.installPlugins()}installPlugins(){this.plugins.forEach(e=>{e.patch(this)})}get commonData(){return this._commonData}get MFECommonDataMap(){return this._MFECommonDataMap}initCommonData(e){this._commonData.sessionId=j(),this._commonData.deviceId=U()||E,this._commonData.platform=W(),this._commonData.networkType=K()||"unknown",this._commonData.env=e.env||this._commonData.env,this._commonData.accountId=e.accountId||E,this._commonData.appVersion=e.appVersion,this._commonData.appName=e.appName,this.lifeCycle.emit(O.onCommonDataInit,this.commonData)}updateCommonDataBasedOnConfig(e){this._commonData.env=e.env||this._commonData.env,this._commonData.appName=e.appName||this._commonData.appName,this._commonData.env=e.env||this._commonData.env,this._commonData.accountId=e.accountId||this._commonData.accountId,this._commonData.appVersion=e.appVersion||this._commonData.appVersion,this.lifeCycle.emit(O.onCommonUpdated,this._commonData)}updateCommonData(e){this._commonData=p(p({},this._commonData),e)}addToMFECommonData({MFEName:e,MFECommonData:t}){var n;this.config.isWebappContainer&&e&&t&&(this._MFECommonDataMap=m(p({},this._MFECommonDataMap||{}),{[e]:p(p({},(null==(n=this._MFECommonDataMap)?void 0:n[e])||{}),t)}))}setConfig(e){if(this.config.isWebappContainer)return;const t=(e,t)=>{this.config[e]=t};Object.entries(e).forEach(e=>{const[n,i]=e;void 0!==typeof i&&t(n,i)}),this.lifeCycle.emit(O.onConfigInit,this.config),this.updateCommonDataBasedOnConfig(e)}info(e,t){this.normalLogPipeLine({severity:"info",eventName:e,extraInfo:t})}warn(e,t){this.normalLogPipeLine({severity:"warn",eventName:e,extraInfo:t})}error(e,t){this.normalLogPipeLine({severity:"error",eventName:e,extraInfo:t})}businessLog(e,t){this.normalLogPipeLine({severity:"info",eventName:e,extraInfo:m(p({},t),{isBusinessLog:!0})})}reportDuration(e,t){"string"===typeof e?"number"===typeof t?t<0||t>6e4?console.warn("reportDuration: duration (second param) must between 0 and 60000"):this.normalLogPipeLine({severity:"speed",eventName:e,extraInfo:{duration:t}}):console.warn("reportDuration: duration (second param) must be number"):console.warn("reportDuration: eventName (first param) must be a string")}getTimerKey(e){return this.config.isWebappContainer?`${F()}_${e}`:e}timeStart(e){"string"===typeof e?(this.timeMap[this.getTimerKey(e)]&&console.warn(`Timer ${e} already exists`),this.timeMap[this.getTimerKey(e)]=Date.now()):console.warn("time: first param must be a string")}timeEnd(e,t){if("string"===typeof e)if(this.timeMap[this.getTimerKey(e)]){const n=Date.now()-this.timeMap[this.getTimerKey(e)]+(t||0);this.normalLogPipeLine({severity:"speed",eventName:e,extraInfo:{duration:n}}),delete this.timeMap[this.getTimerKey(e)]}else console.warn(`Timer key :${e} does not exist`);else console.warn("timeEnd: first param must be a string")}addToAssetSpeedWhiteListByMFE({MFEName:e,whiteList:t}){var n,i,a;this.config.isWebappContainer&&e&&(null==t?void 0:t.length)&&(this.config.assetSpeedMonitoringWhiteListByMFE||(this.config.assetSpeedMonitoringWhiteListByMFE={}),(null==(n=this.config.assetSpeedMonitoringWhiteListByMFE)?void 0:n[e])?null==(a=null==(i=this.config.assetSpeedMonitoringWhiteListByMFE)?void 0:i[e])||a.push(...t):this.config.assetSpeedMonitoringWhiteListByMFE=m(p({},this.config.assetSpeedMonitoringWhiteListByMFE),{[e]:t}))}destroy(){this.config.isWebappContainer||this.plugins.forEach(e=>{e.uninstall()})}},_e=ye},8832:function(e,t,n){!function(t,n){e.exports=n()}(0,(function(){return function(e){function t(i){if(n[i])return n[i].exports;var a=n[i]={exports:{},id:i,loaded:!1};return e[i].call(a.exports,a,a.exports,t),a.loaded=!0,a.exports}var n={};return t.m=e,t.c=n,t.p="",t(0)}([function(e,t,n){"use strict";function i(e){return e&&e.__esModule?e:{default:e}}var a=n(1),r=i(a);e.exports=r.default},function(e,t,n){n(2);var i=n(6)(n(7),n(8),"data-v-82963a40",null);e.exports=i.exports},function(e,t,n){var i=n(3);"string"==typeof i&&(i=[[e.id,i,""]]),n(5)(i,{}),i.locals&&(e.exports=i.locals)},function(e,t,n){t=e.exports=n(4)(),t.push([e.id,"a[data-v-82963a40]{cursor:pointer}",""])},function(e,t){e.exports=function(){var e=[];return e.toString=function(){for(var e=[],t=0;t<this.length;t++){var n=this[t];n[2]?e.push("@media "+n[2]+"{"+n[1]+"}"):e.push(n[1])}return e.join("")},e.i=function(t,n){"string"==typeof t&&(t=[[null,t,""]]);for(var i={},a=0;a<this.length;a++){var r=this[a][0];"number"==typeof r&&(i[r]=!0)}for(a=0;a<t.length;a++){var s=t[a];"number"==typeof s[0]&&i[s[0]]||(n&&!s[2]?s[2]=n:n&&(s[2]="("+s[2]+") and ("+n+")"),e.push(s))}},e}},function(e,t,n){function i(e,t){for(var n=0;n<e.length;n++){var i=e[n],a=u[i.id];if(a){a.refs++;for(var r=0;r<a.parts.length;r++)a.parts[r](i.parts[r]);for(;r<i.parts.length;r++)a.parts.push(c(i.parts[r],t))}else{var s=[];for(r=0;r<i.parts.length;r++)s.push(c(i.parts[r],t));u[i.id]={id:i.id,refs:1,parts:s}}}}function a(e){for(var t=[],n={},i=0;i<e.length;i++){var a=e[i],r=a[0],s=a[1],o=a[2],c=a[3],l={css:s,media:o,sourceMap:c};n[r]?n[r].parts.push(l):t.push(n[r]={id:r,parts:[l]})}return t}function r(e,t){var n=h(),i=v[v.length-1];if("top"===e.insertAt)i?i.nextSibling?n.insertBefore(t,i.nextSibling):n.appendChild(t):n.insertBefore(t,n.firstChild),v.push(t);else{if("bottom"!==e.insertAt)throw new Error("Invalid value for parameter 'insertAt'. Must be 'top' or 'bottom'.");n.appendChild(t)}}function s(e){e.parentNode.removeChild(e);var t=v.indexOf(e);t>=0&&v.splice(t,1)}function o(e){var t=document.createElement("style");return t.type="text/css",r(e,t),t}function c(e,t){var n,i,a;if(t.singleton){var r=g++;n=f||(f=o(t)),i=l.bind(null,n,r,!1),a=l.bind(null,n,r,!0)}else n=o(t),i=d.bind(null,n),a=function(){s(n)};return i(e),function(t){if(t){if(t.css===e.css&&t.media===e.media&&t.sourceMap===e.sourceMap)return;i(e=t)}else a()}}function l(e,t,n,i){var a=n?"":i.css;if(e.styleSheet)e.styleSheet.cssText=y(t,a);else{var r=document.createTextNode(a),s=e.childNodes;s[t]&&e.removeChild(s[t]),s.length?e.insertBefore(r,s[t]):e.appendChild(r)}}function d(e,t){var n=t.css,i=t.media,a=t.sourceMap;if(i&&e.setAttribute("media",i),a&&(n+="\n/*# sourceURL="+a.sources[0]+" */",n+="\n/*# sourceMappingURL=data:application/json;base64,"+btoa(unescape(encodeURIComponent(JSON.stringify(a))))+" */"),e.styleSheet)e.styleSheet.cssText=n;else{for(;e.firstChild;)e.removeChild(e.firstChild);e.appendChild(document.createTextNode(n))}}var u={},p=function(e){var t;return function(){return"undefined"==typeof t&&(t=e.apply(this,arguments)),t}},m=p((function(){return/msie [6-9]\b/.test(window.navigator.userAgent.toLowerCase())})),h=p((function(){return document.head||document.getElementsByTagName("head")[0]})),f=null,g=0,v=[];e.exports=function(e,t){t=t||{},"undefined"==typeof t.singleton&&(t.singleton=m()),"undefined"==typeof t.insertAt&&(t.insertAt="bottom");var n=a(e);return i(n,t),function(e){for(var r=[],s=0;s<n.length;s++){var o=n[s],c=u[o.id];c.refs--,r.push(c)}if(e){var l=a(e);i(l,t)}for(s=0;s<r.length;s++){c=r[s];if(0===c.refs){for(var d=0;d<c.parts.length;d++)c.parts[d]();delete u[c.id]}}}};var y=function(){var e=[];return function(t,n){return e[t]=n,e.filter(Boolean).join("\n")}}()},function(e,t){e.exports=function(e,t,n,i){var a,r=e=e||{},s=typeof e.default;"object"!==s&&"function"!==s||(a=e,r=e.default);var o="function"==typeof r?r.options:r;if(t&&(o.render=t.render,o.staticRenderFns=t.staticRenderFns),n&&(o._scopeId=n),i){var c=o.computed||(o.computed={});Object.keys(i).forEach((function(e){var t=i[e];c[e]=function(){return t}}))}return{esModule:a,exports:r,options:o}}},function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default={props:{value:{type:Number},pageCount:{type:Number,required:!0},forcePage:{type:Number},clickHandler:{type:Function,default:function(){}},pageRange:{type:Number,default:3},marginPages:{type:Number,default:1},prevText:{type:String,default:"Prev"},nextText:{type:String,default:"Next"},breakViewText:{type:String,default:"…"},containerClass:{type:String},pageClass:{type:String},pageLinkClass:{type:String},prevClass:{type:String},prevLinkClass:{type:String},nextClass:{type:String},nextLinkClass:{type:String},breakViewClass:{type:String},breakViewLinkClass:{type:String},activeClass:{type:String,default:"active"},disabledClass:{type:String,default:"disabled"},noLiSurround:{type:Boolean,default:!1},firstLastButton:{type:Boolean,default:!1},firstButtonText:{type:String,default:"First"},lastButtonText:{type:String,default:"Last"},hidePrevNext:{type:Boolean,default:!1}},beforeUpdate:function(){void 0!==this.forcePage&&this.forcePage!==this.selected&&(this.selected=this.forcePage)},computed:{selected:{get:function(){return this.value||this.innerValue},set:function(e){this.innerValue=e}},pages:function(){var e=this,t={};if(this.pageCount<=this.pageRange)for(var n=0;n<this.pageCount;n++){var i={index:n,content:n+1,selected:n===this.selected-1};t[n]=i}else{for(var a=Math.floor(this.pageRange/2),r=function(n){var i={index:n,content:n+1,selected:n===e.selected-1};t[n]=i},s=function(e){var n={disabled:!0,breakView:!0};t[e]=n},o=0;o<this.marginPages;o++)r(o);var c=0;this.selected-a>0&&(c=this.selected-1-a);var l=c+this.pageRange-1;l>=this.pageCount&&(l=this.pageCount-1,c=l-this.pageRange+1);for(var d=c;d<=l&&d<=this.pageCount-1;d++)r(d);c>this.marginPages&&s(c-1),l+1<this.pageCount-this.marginPages&&s(l+1);for(var u=this.pageCount-1;u>=this.pageCount-this.marginPages;u--)r(u)}return t}},data:function(){return{innerValue:1}},methods:{handlePageSelected:function(e){this.selected!==e&&(this.innerValue=e,this.$emit("input",e),this.clickHandler(e))},prevPage:function(){this.selected<=1||this.handlePageSelected(this.selected-1)},nextPage:function(){this.selected>=this.pageCount||this.handlePageSelected(this.selected+1)},firstPageSelected:function(){return 1===this.selected},lastPageSelected:function(){return this.selected===this.pageCount||0===this.pageCount},selectFirstPage:function(){this.selected<=1||this.handlePageSelected(1)},selectLastPage:function(){this.selected>=this.pageCount||this.handlePageSelected(this.pageCount)}}}},function(e,t){e.exports={render:function(){var e=this,t=e.$createElement,n=e._self._c||t;return e.noLiSurround?n("div",{class:e.containerClass},[e.firstLastButton?n("a",{class:[e.pageLinkClass,e.firstPageSelected()?e.disabledClass:""],attrs:{tabindex:"0"},domProps:{innerHTML:e._s(e.firstButtonText)},on:{click:function(t){e.selectFirstPage()},keyup:function(t){return"button"in t||!e._k(t.keyCode,"enter",13)?void e.selectFirstPage():null}}}):e._e(),e._v(" "),e.firstPageSelected()&&e.hidePrevNext?e._e():n("a",{class:[e.prevLinkClass,e.firstPageSelected()?e.disabledClass:""],attrs:{tabindex:"0"},domProps:{innerHTML:e._s(e.prevText)},on:{click:function(t){e.prevPage()},keyup:function(t){return"button"in t||!e._k(t.keyCode,"enter",13)?void e.prevPage():null}}}),e._v(" "),e._l(e.pages,(function(t){return[t.breakView?n("a",{class:[e.pageLinkClass,e.breakViewLinkClass,t.disabled?e.disabledClass:""],attrs:{tabindex:"0"}},[e._t("breakViewContent",[e._v(e._s(e.breakViewText))])],2):t.disabled?n("a",{class:[e.pageLinkClass,t.selected?e.activeClass:"",e.disabledClass],attrs:{tabindex:"0"}},[e._v(e._s(t.content))]):n("a",{class:[e.pageLinkClass,t.selected?e.activeClass:""],attrs:{tabindex:"0"},on:{click:function(n){e.handlePageSelected(t.index+1)},keyup:function(n){return"button"in n||!e._k(n.keyCode,"enter",13)?void e.handlePageSelected(t.index+1):null}}},[e._v(e._s(t.content))])]})),e._v(" "),e.lastPageSelected()&&e.hidePrevNext?e._e():n("a",{class:[e.nextLinkClass,e.lastPageSelected()?e.disabledClass:""],attrs:{tabindex:"0"},domProps:{innerHTML:e._s(e.nextText)},on:{click:function(t){e.nextPage()},keyup:function(t){return"button"in t||!e._k(t.keyCode,"enter",13)?void e.nextPage():null}}}),e._v(" "),e.firstLastButton?n("a",{class:[e.pageLinkClass,e.lastPageSelected()?e.disabledClass:""],attrs:{tabindex:"0"},domProps:{innerHTML:e._s(e.lastButtonText)},on:{click:function(t){e.selectLastPage()},keyup:function(t){return"button"in t||!e._k(t.keyCode,"enter",13)?void e.selectLastPage():null}}}):e._e()],2):n("ul",{class:e.containerClass},[e.firstLastButton?n("li",{class:[e.pageClass,e.firstPageSelected()?e.disabledClass:""]},[n("a",{class:e.pageLinkClass,attrs:{tabindex:e.firstPageSelected()?-1:0},domProps:{innerHTML:e._s(e.firstButtonText)},on:{click:function(t){e.selectFirstPage()},keyup:function(t){return"button"in t||!e._k(t.keyCode,"enter",13)?void e.selectFirstPage():null}}})]):e._e(),e._v(" "),e.firstPageSelected()&&e.hidePrevNext?e._e():n("li",{class:[e.prevClass,e.firstPageSelected()?e.disabledClass:""]},[n("a",{class:e.prevLinkClass,attrs:{tabindex:e.firstPageSelected()?-1:0},domProps:{innerHTML:e._s(e.prevText)},on:{click:function(t){e.prevPage()},keyup:function(t){return"button"in t||!e._k(t.keyCode,"enter",13)?void e.prevPage():null}}})]),e._v(" "),e._l(e.pages,(function(t){return n("li",{class:[e.pageClass,t.selected?e.activeClass:"",t.disabled?e.disabledClass:"",t.breakView?e.breakViewClass:""]},[t.breakView?n("a",{class:[e.pageLinkClass,e.breakViewLinkClass],attrs:{tabindex:"0"}},[e._t("breakViewContent",[e._v(e._s(e.breakViewText))])],2):t.disabled?n("a",{class:e.pageLinkClass,attrs:{tabindex:"0"}},[e._v(e._s(t.content))]):n("a",{class:e.pageLinkClass,attrs:{tabindex:"0"},on:{click:function(n){e.handlePageSelected(t.index+1)},keyup:function(n){return"button"in n||!e._k(n.keyCode,"enter",13)?void e.handlePageSelected(t.index+1):null}}},[e._v(e._s(t.content))])])})),e._v(" "),e.lastPageSelected()&&e.hidePrevNext?e._e():n("li",{class:[e.nextClass,e.lastPageSelected()?e.disabledClass:""]},[n("a",{class:e.nextLinkClass,attrs:{tabindex:e.lastPageSelected()?-1:0},domProps:{innerHTML:e._s(e.nextText)},on:{click:function(t){e.nextPage()},keyup:function(t){return"button"in t||!e._k(t.keyCode,"enter",13)?void e.nextPage():null}}})]),e._v(" "),e.firstLastButton?n("li",{class:[e.pageClass,e.lastPageSelected()?e.disabledClass:""]},[n("a",{class:e.pageLinkClass,attrs:{tabindex:e.lastPageSelected()?-1:0},domProps:{innerHTML:e._s(e.lastButtonText)},on:{click:function(t){e.selectLastPage()},keyup:function(t){return"button"in t||!e._k(t.keyCode,"enter",13)?void e.selectLastPage():null}}})]):e._e()],2)},staticRenderFns:[]}}])}))},"88a7":function(e,t,n){"use strict";var i=n("cb2d"),a=n("e330"),r=n("577e"),s=n("d6d6"),o=URLSearchParams,c=o.prototype,l=a(c.append),d=a(c["delete"]),u=a(c.forEach),p=a([].push),m=new o("a=1&a=2&b=3");m["delete"]("a",1),m["delete"]("b",void 0),m+""!=="a=2"&&i(c,"delete",(function(e){var t=arguments.length,n=t<2?void 0:arguments[1];if(t&&void 0===n)return d(this,e);var i=[];u(this,(function(e,t){p(i,{key:t,value:e})})),s(t,1);var a,o=r(e),c=r(n),m=0,h=0,f=!1,g=i.length;while(m<g)a=i[m++],f||a.key===o?(f=!0,d(this,a.key)):h++;while(h<g)a=i[h++],a.key===o&&a.value===c||l(this,a.key,a.value)}),{enumerable:!0,unsafe:!0})},"88bb":function(e,t,n){},"8c3e":function(e,t,n){"use strict";n("b2ba")},"967b":function(e,t,n){},a280:function(e,t,n){"use strict";n.r(t);var i=function(){var e=this,t=e._self._c;return t("div",{staticClass:"payment-callback-page"},["completed"===e.status||"fail"===e.status?[t("div",{staticClass:"info-wrapper"},[t("div",{class:["image","image-status__"+e.status]}),t("section",[t("div",{staticClass:"title-desc"},[e._v(e._s(e.$t(e.title[e.status])))]),t("div",{staticClass:"command"},[e._v(e._s(e.$t(e.tips[e.status])))])])])]:"pending"===e.status?[t("div",{staticClass:"info-wrapper",staticStyle:{"flex-direction":"column"}},[t("div",{class:["image","image-status__"+e.status]}),t("section",[t("div",{staticClass:"title-desc"},[e._v(e._s(e.$t("cb_page_pending_desc")))]),t("div",{staticClass:"command"},[e._v(e._s(e.$t("cb_page_pending_tips")))])])])]:[e._v("Configuration Error!")]],2)},a=[],r=n("af82"),s={name:"PaymentCallback",data(){return{status:this.$route.path.replace("/common/",""),title:{completed:"cb_pay_succeed",fail:"cb_page_title_err"},tips:{completed:"cb_view_tips",fail:"cb_view_err_tips"},interval:"",timeStop:!1}},methods:{clearInterval(){this.interval&&(clearInterval(this.interval),this.interval="")},getPaymentStatus(){const e=this.$route.query,t=e.foreignInvoice||e.orderId||e.OrderId,n={transaction_id:t,hideErrToast:!0};Object(r["o"])(n).then(e=>{0===e.code&&(this.$router.replace("/completed"),this.clearInterval())})},adapterStatus(){const e={fail:2,completed:1,pending:0};return e[this.status]},backGame(){const e=location.href.includes("adyen")||location.href.includes("airwallext");this.$router.replace("/"),e&&setTimeout(()=>window.location.reload(),300)},navTo(e){this.$router.replace(e).then(e=>{e.fullPath.includes("/gallery")&&window.location.reload()})},judgeStatus(){this.interval&&(clearInterval(this.interval),this.interval=""),this.$root.$emit("showPop","CallbackPendingTips")}},created(){window.fetchOrderStatus=this.adapterStatus.bind(this),"pending"===this.status&&(this.getPaymentStatus(),this.interval=setInterval(()=>{this.getPaymentStatus()},2e3),setTimeout(()=>{this.clearInterval(),this.timeStop=!0},6e4))},beforeDestroy(){this.clearInterval()}},o=s,c=(n("a73d"),n("2877")),l=Object(c["a"])(o,i,a,!1,null,"2f31e1d6",null);t["default"]=l.exports},a73d:function(e,t,n){"use strict";n("c5dc")},a831:function(e,t,n){"use strict";n("88bb")},b2ba:function(e,t,n){},b39f:function(e,t,n){"use strict";n("ccab")},b9c7:function(e,t,n){},ba9a:function(e,t,n){"use strict";n("ee51")},c5dc:function(e,t,n){},ccab:function(e,t,n){},d6d6:function(e,t,n){"use strict";var i=TypeError;e.exports=function(e,t){if(e<t)throw new i("Not enough arguments");return e}},e22b:function(e,t,n){"use strict";n.r(t);var i=function(){var e=this,t=e._self._c;return t("div",{staticClass:"airwallex-page-wrapper"},[e.isPc?t("channel-logo"):e._e(),t("div",{staticClass:"content-wrapper"},[t("channel-order",{attrs:{coin:e.initParams.coinNums,currency:e.initParams.currency_symbol,amount:e.initParams.amount,"in-debt":e.initParams.inDebt}}),t("channel-wrapper",[t("section",{staticClass:"airewallex-wrapper"},[t("div",{staticClass:"inner-wrapper"},[t("div",{ref:"card",attrs:{id:"drop-in"}})])])])],1),e.isMobile?t("channel-logo"):e._e()],1)},a=[],r=n("8390"),s=n.n(r),o=function(){return o=Object.assign||function(e){for(var t,n=1,i=arguments.length;n<i;n++)for(var a in t=arguments[n],t)Object.prototype.hasOwnProperty.call(t,a)&&(e[a]=t[a]);return e},o.apply(this,arguments)};function c(e,t,n,i){function a(e){return e instanceof n?e:new n((function(t){t(e)}))}return new(n||(n=Promise))((function(n,r){function s(e){try{c(i.next(e))}catch(t){r(t)}}function o(e){try{c(i["throw"](e))}catch(t){r(t)}}function c(e){e.done?n(e.value):a(e.value).then(s,o)}c((i=i.apply(e,t||[])).next())}))}function l(e,t){var n,i,a,r,s={label:0,sent:function(){if(1&a[0])throw a[1];return a[1]},trys:[],ops:[]};return r={next:o(0),throw:o(1),return:o(2)},"function"===typeof Symbol&&(r[Symbol.iterator]=function(){return this}),r;function o(e){return function(t){return c([e,t])}}function c(o){if(n)throw new TypeError("Generator is already executing.");while(r&&(r=0,o[0]&&(s=0)),s)try{if(n=1,i&&(a=2&o[0]?i["return"]:o[0]?i["throw"]||((a=i["return"])&&a.call(i),0):i.next)&&!(a=a.call(i,o[1])).done)return a;switch(i=0,a&&(o=[2&o[0],a.value]),o[0]){case 0:case 1:a=o;break;case 4:return s.label++,{value:o[1],done:!1};case 5:s.label++,i=o[1],o=[0];continue;case 7:o=s.ops.pop(),s.trys.pop();continue;default:if(a=s.trys,!(a=a.length>0&&a[a.length-1])&&(6===o[0]||2===o[0])){s=0;continue}if(3===o[0]&&(!a||o[1]>a[0]&&o[1]<a[3])){s.label=o[1];break}if(6===o[0]&&s.label<a[1]){s.label=a[1],a=o;break}if(a&&s.label<a[2]){s.label=a[2],s.ops.push(o);break}a[2]&&s.ops.pop(),s.trys.pop();continue}o=t.call(e,s)}catch(c){o=[6,c],i=0}finally{n=a=0}if(5&o[0])throw o[1];return{value:o[0]?o[1]:void 0,done:!0}}}function d(e,t,n){if(n||2===arguments.length)for(var i,a=0,r=t.length;a<r;a++)!i&&a in t||(i||(i=Array.prototype.slice.call(t,0,a)),i[a]=t[a]);return e.concat(i||Array.prototype.slice.call(t))}"function"===typeof SuppressedError&&SuppressedError;var u,p,m,h="@airwallex/components-sdk",f="1.22.0",g="./lib/index.js",v="./lib/index.mjs",y="./lib/index.d.ts",_=["lib/**"],b="MIT",w={"load-types":"node ./load-types.js && yarn prettier -w src/types/*d.ts",prebuild:"yarn clean",build:"yarn rollup --config",postbuild:"yarn bundle-types","bundle-types":"dts-bundle-generator -o ./lib/index.d.ts ./src/index.ts",clean:"rm -rf dist lib",lint:'eslint "src/**/*.ts*"',test:"jest",sonar:"sonar-scanner",release:"semantic-release","lint-stage":"lint-staged",prepare:"husky"},C={semi:!0,singleQuote:!0,printWidth:80,trailingComma:"all"},k={access:"public"},x={"@babel/preset-typescript":"^7.18.6","@rollup/plugin-commonjs":"^25.0.7","@rollup/plugin-json":"^6.1.0","@rollup/plugin-node-resolve":"^15.2.3","@rollup/plugin-terser":"^0.4.4","@rollup/plugin-typescript":"^11.1.6","@semantic-release/changelog":"^5.0.1","@semantic-release/commit-analyzer":"^8.0.1","@semantic-release/git":"^9.0.0","@semantic-release/gitlab":"^6.1.0","@semantic-release/release-notes-generator":"^9.0.0","@swc/core":"^1.3.46","@types/jest":"^29.5.0","@typescript-eslint/eslint-plugin":"^5.57.0","@typescript-eslint/parser":"^5.57.0","dts-bundle-generator":"^9.0.0",eslint:"^7.23.0","eslint-config-prettier":"^8.3.0","eslint-plugin-prettier":"^5.1.3",husky:"^9.0.11",jest:"^29.5.0","jest-environment-jsdom":"^29.5.0","lint-staged":"^15.2.7",prettier:"^3.3.2",rollup:"^4.9.6","semantic-release":"^17.3.8","sonarqube-scanner":"^3.0.1","ts-jest":"^29.0.5","ts-loader":"^9.5.1",tslib:"^2.6.2",typescript:"^4.5.2"},P={"@airwallex/airtracker":"1.2.0"},S={name:h,version:f,main:g,module:v,types:y,files:_,license:b,scripts:w,prettier:C,"lint-staged":{"src/**/*.{ts,tsx}":["yarn lint"],"src/**/*.{js,jsx,ts,tsx,json,css,scss,md}":"prettier --write"},publishConfig:k,devDependencies:x,dependencies:P},E={dev:r["Environment"].staging,staging:r["Environment"].staging,demo:r["Environment"].demo,prod:r["Environment"].production},T=function(e,t){return void 0===e&&(e=r["Environment"].production),window._AirwallexSDKs.airTracker||(window._AirwallexSDKs.airTracker=new s.a({config:{appName:"components-sdk",appVersion:S.version,env:E[e]||r["Environment"].production}}),window._AirwallexSDKs.airTracker.updateCommonData({clientId:t})),window._AirwallexSDKs.airTracker},I=function(e){if("object"!==typeof e)return e;try{var t=JSON.parse(JSON.stringify(e));return Object.keys(t).forEach((function(e){"string"===typeof e&&["auth","code"].some((function(t){return e.includes(t)}))&&(t[e]="***")})),t}catch(u){return e}},L=function(e,t){var n,i;null===(i=null===(n=window._AirwallexSDKs)||void 0===n?void 0:n.airTracker)||void 0===i||i.error(e,o(o({},t),{context:"components-sdk"}))},D=function(e,t){var n;null===(n=window._AirwallexSDKs.airTracker)||void 0===n||n.warn(e,o(o({},t),{context:"components-sdk"}))},N=function(e,t){var n;null===(n=window._AirwallexSDKs.airTracker)||void 0===n||n.info(e,o(o({},t),{context:"components-sdk"}))};(function(e){e["dev"]="dev",e["staging"]="staging",e["demo"]="demo",e["prod"]="prod"})(m||(m={}));var A,O=(u={},u[m.dev]="https://checkout-staging.airwallex.com",u[m.staging]="https://checkout-staging.airwallex.com",u[m.demo]="https://checkout-demo.airwallex.com",u[m.prod]="https://checkout.airwallex.com",u),M=(p={},p[m.dev]="https://static-staging.airwallex.com",p[m.staging]="https://static-staging.airwallex.com",p[m.demo]="https://static-demo.airwallex.com",p[m.prod]="https://static.airwallex.com",p),$="sdkController",R={kyc:"/widgets/kyc/sdk/v1/index.js",rfi:"/widgets/kycRfi/sdk/v1/index.js",paymentsKyb:"/widgets/paymentsKyb/sdk/v1/index.js",sdkController:"/widgets/sdk-controller/sdk/v1/index.js",payouts:"/widgets/payouts/sdk/v1/index.js",payments:"/assets/elements.bundle.min.js",sca:"/hosted-sca/sdk/v1/index.js",taxForm:"/widgets/taxForm/sdk/v1/index.js"},j={kyc:"kyc",paymentsKyb:"paymentsKyb",kycRfi:"rfi",transactionRfi:"rfi",paymentEnablementRfi:"rfi",lendingRfi:"rfi",payoutForm:"payouts",beneficiaryForm:"payouts",hpp:"payments",cvc:"payments",card:"payments",expiry:"payments",dropIn:"payments",cardNumber:"payments",applePayButton:"payments",googlePayButton:"payments",scaSetup:"sca",scaVerify:"sca",scaManagement:"sca",taxForm:"taxForm"},F={payments:["hpp","cvc","card","expiry","dropIn","cardNumber","applePayButton","googlePayButton"],payouts:["payoutForm","beneficiaryForm"],onboarding:["kyc","paymentsKyb"],risk:["scaSetup","scaVerify","scaManagement","transactionRfi","kycRfi","paymentEnablementRfi","lendingRfi"]},U=["payments","sca"],W=Date.now(),z=function(e){return Object.keys(F).includes(e)},K=function(e){return void 0===e&&(e=500),new Promise((function(t){return window.setTimeout(t,e)}))},V=function(e){return j[e]},B=function(e,t){var n="payments"===e?O:M;return n[t]||n.prod},H=function(e){var t=e.reduce((function(e,t){return e.concat(z(t)?F[t]:t)}),[]);return Array.from(new Set(t)).map((function(e){return{elementName:e,sdkName:V(e)}}))},q=function(e){return Array.from(new Set(H(e).map((function(e){var t=e.sdkName;return t})).filter((function(e){return U.includes(e)}))))},J=function(e){return"payments"===e?"payment":e},Q=function(e){var t=e.name,n=e.env,i=void 0===n?m.prod:n,a=B(t,i),r=R[t];if(!r)throw L("[components-sdk] Element static resource not found",{elementName:t}),new Error("Element ".concat(t," static resource URL is invalid."));return"".concat(a).concat(r,"?ts=").concat(W)},Y=function(){return U.reduce((function(e,t){var n;return o(o({},e),(n={},n[t]=window._AirwallexSDKs.__controller__.internalSDKs[t],n))}),{})},X=function(){return Math.floor(performance.now())},Z=function(e){var t=document.createElement("script");t.src=e,t.type="module";var n=document.head||document.body;return n.appendChild(t),t},G=3,ee=function(e){return c(void 0,void 0,void 0,(function(){var t,n;return l(this,(function(i){switch(i.label){case 0:if("undefined"===typeof window)throw new Error("Please load script in browser environment");t=0,n=function(){return c(void 0,void 0,void 0,(function(){var t,n;return l(this,(function(i){return t=X(),n=Z(e),[2,new Promise((function(i,a){n.addEventListener("load",(function(){var n=X();N("[components-sdk] SDK script loaded",{scriptUrl:e,start:t,latency:n-t,end:n}),i(!0)})),n.addEventListener("error",(function(i){n.remove(),console.error(i);var r=X();L("[components-sdk] Failed to load script",{scriptUrl:e,error:i,start:t,latency:r-t,end:r}),a(new Error("Failed to load Airwallex SDK scripts: ".concat(e)))}))}))]}))}))},i.label=1;case 1:if(!(t<G))return[3,7];i.label=2;case 2:return i.trys.push([2,4,,6]),[4,n()];case 3:return[2,i.sent()];case 4:return i.sent(),t++,[4,K()];case 5:return i.sent(),[3,6];case 6:return[3,1];case 7:throw L("[components-sdk] Failed to load script after retry",{scriptUrl:e}),new Error("Failed to load Airwallex SDK scripts: ".concat(e))}}))}))},te=new Map,ne=function(e){var t=e.env,n=e.scriptName,i=Q({name:n,env:t}),a=te.get(n);if(a)return a;var r=ee(i);return te.set(n,r),r},ie=function(e){return c(void 0,void 0,void 0,(function(){return l(this,(function(t){if("undefined"===typeof window)throw new Error("Please call the `init()` function in a browser environment.");return T(e.env,e.clientId),A=new Promise((function(t,n){var i,a=o(o({},e),{env:e.env&&m[e.env]?e.env:m.prod});window.AirwallexComponentsSDK.__env__=a.env;var r=ne({env:a.env,scriptName:$}).then((function(){return window._AirwallexSDKs.__controller__.init(a)})),s=null!==(i=e.enabledElements)&&void 0!==i?i:[],c=q(s),l=c.map((function(e){return ne({env:a.env,scriptName:e}).then((function(){return r})).then((function(){return window._AirwallexSDKs.__controller__.registerFunctions({functionsNamespace:e,instance:window._AirwallexSDKs[J(e)]})}))})),u=H(s),p=u.map((function(e){var t=e.sdkName,n=e.elementName;return ne({scriptName:t,env:a.env}).then((function(){return r})).then((function(){return window._AirwallexSDKs.__controller__.registerElement({sdkName:t,elementName:n,instance:window._AirwallexSDKs[J(t)]})}))}));Promise.all(d(d([r],l,!0),p,!0)).then((function(){N("[components-sdk] SDK initialized",{options:I(e),start:X()}),t(Y())}))["catch"]((function(e){e.code||L("[components-sdk] Unexpected errors when init",{error:e}),n(e)}))})),[2,A]}))}))},ae=function(e,t){return c(void 0,void 0,void 0,(function(){var n,i,a,r,s;return l(this,(function(o){switch(o.label){case 0:if(!e)throw new Error("Element type is missing. Please specify a valid element.");if(!A)throw D("[components-sdk] Did not call init before createElement"),new Error("Please initialize the Element before creating it.");return[4,A];case 1:if(o.sent(),n=V(e),!n)throw new Error("`createElement()` with type `".concat(e,"` is not supported. Please specify a valid Element type."));i=X(),o.label=2;case 2:return o.trys.push([2,6,,7]),[4,ne({scriptName:n,env:window.AirwallexComponentsSDK.__env__||m.prod})];case 3:return o.sent(),[4,window._AirwallexSDKs.__controller__.registerElement({sdkName:n,elementName:e,instance:window._AirwallexSDKs[J(n)]})];case 4:return o.sent(),[4,window._AirwallexSDKs.__controller__.createElement(e,t)];case 5:return a=o.sent(),r=X(),N("[components-sdk] SDK createElement being called",{elementName:e,options:I(t),start:i,latency:r-i,end:r}),[3,7];case 6:if(s=o.sent(),s.code)throw s;return L("[components-sdk] Unexpected errors when createElement",{error:s}),[3,7];case 7:return[2,a]}}))}))},re=[$],se=function(){re.forEach((function(e){var t=Q({name:e,env:m.prod}),n=document.createElement("link");n.href=t,n.rel="modulepreload",n.as="script";var i=document.head||document.body;i.appendChild(n)}))};"loading"===document.readyState?document.addEventListener("DOMContentLoaded",se):se(),"undefined"!==typeof window&&(Object.defineProperties(window,{AirwallexComponentsSDK:{value:{},writable:!1},_AirwallexSDKs:{value:{},writable:!1}}),window.AirwallexComponentsSDK.init=ie,window.AirwallexComponentsSDK.createElement=ae);var oe=n("5799"),ce=n("1b76"),le=n("82a4"),de=n("2f62"),ue={name:"airwallex",components:{ChannelLogo:oe["a"],ChannelWrapper:ce["a"],ChannelOrder:le["a"]},computed:{...Object(de["c"])(["isPc","isMobile"])},data(){return{airwalexInstance:"",initParams:{}}},methods:{async initAirwallext(){let e;try{e=JSON.parse(sessionStorage.getItem("params")||"{}"),this.initParams=e,await ie({enabledElements:["payments"],env:e.env,origin:window.location.origin});const t=await ae("dropIn",{intent_id:e.intent_id,client_secret:e.client_secret,currency:e.currency,mode:e.mode,cvcRequired:!0,theme:{palette:{primary:"#00112c"}},methods:[e.payment_method],customer_id:e.customer_id});t.mount("drop-in"),this.airwalexInstance=t;const n=this.$refs.card;n.addEventListener&&(n.addEventListener("onReady",this.onReady),n.addEventListener("onSuccess",this.onSuccess),n.addEventListener("onError",this.onError))}catch(t){console.error(t.message),console.log(`Airwallext 组件初始化失败，错误信息：${t.message}。`)}},onReady(){console.log("cmp ready!")},onSuccess(e){this.$router.replace("/completed?ir=aw")},onError(e){const{error:t}=e.detail;switch(t.code){default:this.$toast.err(t.message)}}},created(){this.initAirwallext()},beforeDestroy(){sessionStorage.removeItem("params")}},pe=ue,me=(n("178d"),n("2877")),he=Object(me["a"])(pe,i,a,!1,null,"fd385af2",null);t["default"]=he.exports},e827:function(e,t,n){"use strict";n.r(t);var i=function(){var e=this,t=e._self._c;return t("div",{staticClass:"pingpong-page-wrapper"},[e.isPc?t("channel-logo"):e._e(),t("div",{staticClass:"content-wrapper"},[t("channel-order",{attrs:{coin:e.initParams.coinNums,currency:e.initParams.currency_symbol,amount:e.initParams.amount,"in-debt":e.initParams.inDebt}}),t("channel-wrapper",[t("section",{staticClass:"pingpong-wrapper"},[t("div",{staticClass:"inner-wrapper"},[t("div",{staticClass:"cmp-wrapper"},[t("div",{staticClass:"frame-card"}),t("button",{attrs:{id:"submit"},on:{click:e.submitForm}},[t("img",{attrs:{src:"https://kg-web-cdn.akamaized.net/prod/web-pay-unique/dist_online/static/img/lock.b2cb557c.svg",alt:"","aria-hidden":"true"}}),e._v(" Pay "+e._s(e.initParams.currency_symbol)+e._s(e.initParams.amount)+" ")])])])])])],1),e.isMobile?t("channel-logo"):e._e()],1)},a=[],r=n("2f62"),s=n("82a4"),o=n("1b76"),c=n("5799"),l=n("afaa"),d={name:"payermax",components:{ChannelLogo:c["a"],ChannelWrapper:o["a"],ChannelOrder:s["a"]},computed:{...Object(r["c"])(["isPc","isMobile"])},data(){return{initParams:{},cardInstance:{},isFormValid:!1,cardChosen:!1}},methods:{loadPingpongScript(){const e="https://cdn.payermax.com/dropin/js/pmdropin.min.js",t=document.createElement("script");t.src=e,t.onload=this.onScriptLoad,document.body.appendChild(t)},onScriptLoad(){const e=window.PMdropin;this.initParams=JSON.parse(sessionStorage.getItem("params")||"{}");const t=e.create("card",{clientKey:this.initParams.clientKey,sessionKey:this.initParams.sessionKey,language:this.$i18n.locale,sandbox:this.initParams.sand_box});t.mount(".frame-card"),t.on("form-check",e=>{this.isFormValid=e.isFormValid,this.cardChosen=e.isFormValid||Boolean(e.from)}),t.on("ready",()=>this.onReady()),this.cardInstance=t},onReady(){console.log("cmp ready!")},onCmpError(){this.$router.go(-1),setTimeout(()=>this.$root.$emit("adyenInitError"),200)},submitForm(){const e=this.cardInstance;return this.cardChosen?this.isFormValid?(e.emit("setDisabled",!0),void e.emit("canMakePayment").then(t=>{const{code:n}=t;switch(n){case"APPLY_SUCCESS":{var i;const e=null===t||void 0===t||null===(i=t.data)||void 0===i?void 0:i.paymentToken;this.requestPay(e);break}default:}e.emit("setDisabled",!1)}).catch(t=>{e.emit("setDisabled",!1),console.log(t)})):(e.emit("canMakePayment"),null):this.$toast.err("Please select a payment method!")},requestPay(e){const t=this.initParams,n={reference:t.payment_order_id,sek:t.sessionKey,pt:e,subject:t.name};this.$loading.show(),l["d"].post(t.pay_url,n).then(e=>{const{code:t,data:n}=e;switch(t){case 0:{const{status:e}=n;"SUCCESS"===e&&this.$router.replace("/completed?rf=1");break}default:this.$toast.err(this.$t("cb_page_title_err"))}}).finally(()=>this.$loading.hide())}},created(){this.loadPingpongScript()},beforeDestroy(){sessionStorage.removeItem("params")}},u=d,p=(n("07f4"),n("2877")),m=Object(p["a"])(u,i,a,!1,null,null,null);t["default"]=m.exports},e9ff:function(e,t,n){"use strict";n.r(t);var i=function(){var e=this,t=e._self._c;return t("div",{class:["payment-callback-page",e.$gameName],attrs:{id:"payment-callback-page"}},["completed"===e.status?[t("div",{staticClass:"info-wrapper"},[t("div",{class:["image","image-status__"+e.status]}),t("section",[t("div",{staticClass:"title-desc"},[e._v(e._s(e.$t(e.title[e.status])))]),t("div",{staticClass:"command"},[e._v(e._s(e.$t(e.tips[e.status])))])])]),t("div",{staticClass:"btn btn-back",on:{click:e.backGame}},[e._v(e._s(e.$t(e.backGameTxt)))])]:"fail"===e.status?[t("div",{staticClass:"info-wrapper"},[t("div",{class:["image","image-status__"+e.status]}),t("section",[t("div",{staticClass:"title-desc"},[e._v(e._s(e.$t(e.title[e.status])))]),t("div",{staticClass:"command"},[e._v(e._s(e.$t(e.tips[e.status])))])])]),e.$route.query.tip_msg?t("p",{staticClass:"mycard-error-tips"},[e._v(e._s(e.$route.query.tip_msg))]):e._e(),t("div",{staticClass:"btn btn-back",on:{click:e.backGame}},[e._v(e._s(e.$t(e.backGameTxt)))])]:"pending"===e.status?[t("div",{staticClass:"info-wrapper",staticStyle:{"flex-direction":"column"}},[t("div",{class:["image","image-status__"+e.status]}),t("section",[t("div",{class:["title-desc","title-desc__"+e.status]},[e._v(e._s(e.$t("cb_page_pending_desc")))]),t("div",{staticClass:"command"},[e._v(e._s(e.$t("cb_page_pending_tips")))])]),t("div",{staticClass:"btn-wrapper"},[t("div",{staticClass:"btn-status btn-status-not-pay",on:{click:function(t){return e.navTo("/")}}},[e._v(e._s(e.$t("btn_status_not_pay")))]),t("div",{staticClass:"btn-status btn-status-has-pay",on:{click:e.judgeStatus}},[e._v(e._s(e.$t("btn_status_has_pay")))])])])]:[e._v("Configuration Error!")]],2)},a=[],r=n("af82"),s=n("2f62"),o=n("4f6d"),c={name:"PaymentCallback",data(){return{status:this.$route.path.replace("/",""),title:{completed:"cb_pay_succeed",fail:"cb_page_title_err"},tips:{completed:"cb_view_tips",fail:"cb_view_err_tips"},interval:"",timeStop:!1}},computed:{...Object(s["c"])(["IS_CHECKOUT_SDK_V2"]),backGameTxt(){return this.IS_CHECKOUT_SDK_V2?"order-back-btn-txt":"cb_back_home"}},methods:{clearInterval(){this.interval&&(clearInterval(this.interval),this.interval="")},getPaymentStatus(){const e=this.$route.query,t=e.foreignInvoice||e.orderId||e.OrderId,n={transaction_id:t,hideErrToast:!0};Object(r["o"])(n).then(e=>{0===e.code&&(this.$router.replace("/completed"),this.clearInterval())})},adapterStatus(){const e={fail:2,completed:1,pending:0};return e[this.status]},backGame(){if(this.IS_CHECKOUT_SDK_V2)return Object(o["a"])();const e=location.href.includes("ir=ad")||location.href.includes("ir=aw")||location.href.includes("ir=cko")||location.href.includes("rf=1");this.$router.replace("/"),e&&setTimeout(()=>window.location.reload(),300)},navTo(e){this.$router.replace(e).then(e=>{e.fullPath.includes("/gallery")&&window.location.reload()})},judgeStatus(){this.interval&&(clearInterval(this.interval),this.interval=""),this.$root.$emit("showPop","CallbackPendingTips")}},created(){window.fetchOrderStatus=this.adapterStatus.bind(this),"pending"===this.status&&(this.getPaymentStatus(),this.interval=setInterval(()=>{this.getPaymentStatus()},2e3),setTimeout(()=>{this.clearInterval(),this.timeStop=!0},6e4))},mounted(){if(this.$store.state.IS_CHECKOUT_SDK&&!this.IS_CHECKOUT_SDK_V2){const e=document.querySelector(".btn-back");e.style.display="none"}},beforeDestroy(){this.clearInterval()}},l=c,d=(n("0219"),n("2877")),u=Object(d["a"])(l,i,a,!1,null,"365b0c90",null);t["default"]=u.exports},ebe8:function(e,t,n){"use strict";n("3ff4")},ee51:function(e,t,n){},f68e:function(e,t,n){},f81c:function(e,t,n){}}]);