(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pageSdkV2"],{"35cc":function(t,o,s){},"4f6d":function(t,o,s){"use strict";s.d(o,"a",(function(){return a}));var e=s("72c2");function a(){const t=document.createElement("a");t.href=e["a"].ios()?window.$gcbk("gameinfo.appGameDeepLinkIos"):window.$gcbk("gameinfo.appGameDeepLinkAndroid"),t.style.display="none",document.body.appendChild(t),t.click(),document.body.removeChild(t)}},"5e29":function(t,o,s){"use strict";s.r(o);var e=function(){var t=this,o=t._self._c;return o("div",{class:["shopping-wrapper",t.$gameName]},[o("sdk2-header"),o("div",{staticClass:"body-wrapper"},[o("sdk2-user-and-game-info"),o("div",{staticClass:"scroll-wrapper"},[o("div",{staticClass:"scroll-content"},[o("direct-gift-package",{staticStyle:{display:"none"}}),o("sdk2-package-info"),o("coupon-choose",{directives:[{name:"show",rawName:"v-show",value:t.hasCoupon,expression:"hasCoupon"}]}),o("channel-choose"),o("login-module",{directives:[{name:"show",rawName:"v-show",value:!1,expression:"false"}]}),o("sdk2-tip")],1)])],1),o("checkout-footer",{style:{"z-index":t.showCouponPop?-1:1},attrs:{"request-loading":t.requestLoading},on:{purchaseGoods:function(o){return t.judgeRisk()}}})],1)},a=[],n=s("72a3"),c=s("1f1f"),i=s("878c"),_=s("8d29"),p=s("e28d"),u=s("0075"),r=function(){var t=this,o=t._self._c;return o("header",[o("div",{staticClass:"btn-back",on:{click:t.backAppGame}},[o("i")]),o("div",{staticClass:"fp-logo"}),o("div",{staticClass:"toggle-btn",on:{click:t.goStore}},[o("i",{staticClass:"diamond-toggle"}),t._v(" Topup "),o("img",{directives:[{name:"lazy",rawName:"v-lazy",value:t.$gcbk("images.iconDiamond"),expression:"$gcbk('images.iconDiamond')"}],staticClass:"diamond-icon"})])])},P=[],l=s("fa7d"),d=s("4f6d"),m={name:"sdk2Header",methods:{goStore(){Object(l["l"])(Object({NODE_ENV:"production",VUE_APP_PROD_ENV:"ONLINE",VUE_APP_PREFIX_AME:"https://ame.funplus.com",VUE_APP_PREFIX_ACCOUNT:"https://store-account.funplus.com/api/account",VUE_APP_LOGIN_PAGE_URL:"https://store-funplusid.funplus.com/login",VUE_APP_PREFIX_API_KOA:"https://koa-store-api.funplus.com/api",VUE_APP_PREFIX_TOKEN_KOA:"https://koa-store-coin.kingsgroupgames.com/api",VUE_APP_PREFIX_ACCOUNT_KOA:"https://koa-store-account.funplus.com/api/account",VUE_APP_PREFIX_AME_KOA:"https://ame-koa.kingsgroupgames.com",VUE_APP_PREFIX_TOKEN_KOACN:"https://koa-store-coin-tx.kingsgroup.cn/api",VUE_APP_PREFIX_ACCOUNT_KOACN:"https://koa-store-account-tx.kingsgroup.cn/api/account",VUE_APP_PREFIX_AME_KOACN:"https://ame-koa-tx.kingsgroup.cn",VUE_APP_PREFIX_STORE_KOARP:"https://paykoa.com",VUE_APP_VipIntroducePageUrl_aof:"/vip",VUE_APP_VipIntroducePageUrl_koa:"https://vip.funplus.com/koa",VUE_APP_VipIntroducePageUrl_rom:"https://vip.funplus.com/rom",VUE_APP_VipIntroducePageUrl_koaCn:"https://vip.funplus.com/koa-cn",VUE_APP_OlD_STORE_URL_KOA:"https://koa-store.funplus.com",VUE_APP_CN_ADDRESS_KOA:"https://paykoa.cn",VUE_APP_ROM_ADDRESS_KOA:"https://payromgame.com",VUE_APP_PREFIX_TOKEN_DC:"https://store.funplus.com/dcdarklegion/token",VUE_APP_PREFIX_AME_DC:"https://ame-dcdl.kingsgroupgames.com",VUE_APP_PREFIX_ACCOUNT_DC:"https://store.funplus.com/dcdarklegion/account",VUE_APP_PREFIX_STORE_DCRP:"https://store-master.funplus.com/dcdarklegion",VUE_APP_PREFIX_TOKEN_SSCP:"https://store.funplus.com/stateofsurvival/token",VUE_APP_PREFIX_ACCOUNT_SSCP:"https://store.funplus.com/global/account",VUE_APP_PREFIX_AME_SSCP:"https://ame-ss.funplus.com",VUE_APP_PREFIX_TOKEN_STCP:"https://store.funplus.com/stormshot/token",VUE_APP_PREFIX_ACCOUNT_STCP:"https://store.funplus.com/global/account",VUE_APP_PREFIX_AME_STCP:"https://ame-st.funplus.com",VUE_APP_PREFIX_TOKEN_MCCP:"https://store.funplus.com/mistycontinent/token",VUE_APP_PREFIX_ACCOUNT_MCCP:"https://store.funplus.com/global/account",VUE_APP_PREFIX_AME_MCCP:"https://ame-mc.funplus.com",VUE_APP_PREFIX_TOKEN_GOGCP:"https://store.funplus.com/gunsofglory/token",VUE_APP_PREFIX_ACCOUNT_GOGCP:"https://store.funplus.com/global/account",VUE_APP_PREFIX_AME_GOGCP:"https://ame-gog.funplus.com",VUE_APP_PREFIX_TOKEN_ROMCP:"https://store.funplus.com/realmofmystery/token",VUE_APP_PREFIX_ACCOUNT_ROMCP:"https://store.funplus.com/global/account",VUE_APP_PREFIX_AME_ROMCP:"https://ame-koa.kingsgroupgames.com",VUE_APP_PREFIX_TOKEN_SSV2:"https://ssv2-store-api.funplus.com/api",VUE_APP_PREFIX_AME_SSV2:"https://ame-ssv2.funplus.com",VUE_APP_PREFIX_TOKEN_FOUNDATION:"https://store.funplus.com/foundation/token",VUE_APP_PREFIX_ACCOUNT_FOUNDATION:"https://store.funplus.com/ali/account",VUE_APP_PREFIX_AME_FOUNDATION:"https://ame-foundation.funplus.com",VUE_APP_PREFIX_STORE_SSRP:"https://ss-pay.funplus.com",VUE_APP_PREFIX_TOKEN_SSD:"https://store.funplus.com/tilessurvive/token",VUE_APP_PREFIX_ACCOUNT_SSD:"https://store.funplus.com/global/account",VUE_APP_PREFIX_STORE_SSDRP:"https://store.funplus.com/tilessurvive/",VUE_APP_PREFIX_AME_SSD:"https://ame-ts.funplus.com",VUE_APP_PREFIX_TOKEN_MO:"https://store.funplus.com/seaofconquest/token",VUE_APP_PREFIX_ACCOUNT_MO:"https://store.funplus.com/ali/account",VUE_APP_PREFIX_AME_MO:"https://ame-mo.kingsgroupgames.com",VUE_APP_PREFIX_STORE_MORP:"https://soc-store.funplus.com",VUE_APP_PREFIX_STORE_STRP:"https://st-store.funplus.com",BASE_URL:"https://kg-web-cdn.akamaized.net/online/user-platform-web/web-pay-hub/dist_online/"})["VUE_APP_PREFIX_STORE_"+this.$gameName.toUpperCase()])},backAppGame:d["a"]}},E=m,f=(s("ca9b"),s("2877")),h=Object(f["a"])(E,r,P,!1,null,"70d0324b",null),g=h.exports,A=function(){var t=this,o=t._self._c;return o("section",{staticClass:"info-wrapper"},[o("img",{directives:[{name:"lazy",rawName:"v-lazy",value:t.gameLogo,expression:"gameLogo"}],staticClass:"logo"}),o("div",{staticClass:"info"},[o("div",{staticClass:"game-name"},[t._v(t._s(t.gameName))]),o("div",{staticClass:"user-name"},[t._v(t._s(t.$t("my-role"))+": "+t._s(t.$store.state.userinfo.name||"-"))])])])},C=[],k={name:"sdk2UserAndGameInfo",data(){return{gameName:this.$gcbk("gameinfo.gameName"),gameLogo:this.$gcbk("images.logoPath")}}},O=k,U=(s("d460"),Object(f["a"])(O,A,C,!1,null,"173e7069",null)),R=U.exports,v=function(){var t=this,o=t._self._c;return o("common-part",{class:["package-part",t.$gameName],attrs:{"label-font":t.$t("sdk2_product_name"),id:"package-part"}},[o("div",{staticClass:"package-wrapper"},[o("div",{staticClass:"package-icon"}),o("div",{staticClass:"info-wrapper"},[o("div",{staticClass:"name"},[t._v(t._s(t.goodsName||"-"))]),o("div",{staticClass:"price"},[t._v(t._s(t.chosenDiamond.no_tax_price)+t._s(t.currencyUnit))])])]),t.calState.isShow?o("div",{staticClass:"default-coupon"},[o("div",{staticClass:"coupon-icon"}),o("div",{staticClass:"coupon-desc"},[o("over-size-scale",[t._v(t._s(t.calState.description))])],1),o("div",{staticClass:"tips-btn",on:{click:function(o){return t.$root.$emit("showPop","sdk2Tips",{type:"constructions"})}}}),t.calState.discountPrice?o("div",{staticClass:"discount"},[t._v(t._s(t.calState.discountPrice))]):t._e()]):t._e()])},I=[],V=s("3772"),S=s("2f62"),b=s("da93"),N={name:"sdk2PackageInfo",components:{OverSizeScale:b["a"],CommonPart:V["a"]},data(){return{goodsName:"-"}},computed:{...Object(S["c"])(["currencyUnit"]),...Object(S["b"])("formdata",["FinalPriceState"]),...Object(S["c"])("formdata",["chosenCoupon","defaultRebateInfo","defaultDiscountInfo","chosenDiamond"]),calState(){const t={type:"",isShow:!1,description:"",discountPrice:""},o=this.chosenCoupon,s=this.defaultDiscountInfo;if("direct_first_pay"===o.type||!o.feType&&"direct_fixed_discount"===s.type){const e="direct_first_pay"===o.type?o:s;t.isShow=!0,t.discountPrice=`- ${e.discount_amount}${this.currencyUnit}`,t.description=this.$t("sdk2_bonus_"+e.type,{0:e.rateWidthOutPercent+"% OFF"}),t.type=e.type}const e=this.defaultRebateInfo;if("direct_first_pay_rebate"===o.type||!o.feType&&"direct_fixed_rebate"===e.type){const s="direct_first_pay_rebate"===o.type?o:e;t.isShow=!0;const a=Math.floor(s.coin-s.level_coin),n=`${a} ${this.$vt("tokenName")}`;t.description=this.$t("sdk2_bonus_"+s.type,{0:n}),t.type=s.type}return t}},created(){this.$root.$on("updateSdk2PackageName",t=>{window.defaultPackageName=this.goodsName=t}),window._calState=()=>this.calState}},F=N,y=(s("87f8"),Object(f["a"])(F,v,I,!1,null,"70b41223",null)),T=y.exports,X=function(){var t=this,o=t._self._c;return o("common-part",{staticClass:"tips-part"},[o("div",{staticClass:"tips"},[t._v(" You are purchasing a digital license for this product. For full terms, see "),o("span",{on:{click:function(o){return t.$root.$emit("showPop","sdk2Tips",{type:"policy"})}}},[t._v("purchase policy")]),t._v(". ")])])},w=[],$={name:"sdk2Tip",components:{CommonPart:V["a"]}},D=$,M=(s("8db8"),Object(f["a"])(D,X,w,!1,null,"fd054c46",null)),K=M.exports,x=s("5001"),G={name:"Pay",mixins:[u["a"]],components:{Sdk2Tip:K,Sdk2PackageInfo:T,sdk2UserAndGameInfo:R,Sdk2Header:g,CheckoutFooter:p["a"],CouponChoose:i["a"],ChannelChoose:c["a"],LoginModule:n["a"],DirectGiftPackage:_["a"]},computed:{...Object(S["c"])("formdata",["chosenCoupon"]),...Object(S["c"])(["urlParams"])},data(){return{hasCoupon:!1,showCouponPop:!1}},created(){this.$root.$on("updateSdk2CouponList",t=>{this.chosenCoupon.type&&this.chosenCoupon.type.includes("direct_first_pay")?this.hasCoupon=!1:this.hasCoupon=t.length>0}),this.$root.$on("showCouponPop",t=>{this.showCouponPop=t}),this.$root.$on("loginEnd",t=>{const o={};this.urlParams.oid&&(o.oid=this.urlParams.oid),t&&Object(x["logForSdk2OpenedSuccess"])(o)})}},j=G,L=(s("9837"),Object(f["a"])(j,e,a,!1,null,"36bbd276",null));o["default"]=L.exports},7797:function(t,o,s){},"7f35":function(t,o,s){},8706:function(t,o,s){},"87f8":function(t,o,s){"use strict";s("cc11")},"8db8":function(t,o,s){"use strict";s("7797")},9837:function(t,o,s){"use strict";s("7f35")},ca9b:function(t,o,s){"use strict";s("8706")},cc11:function(t,o,s){},d460:function(t,o,s){"use strict";s("35cc")}}]);