{"timestamp": "2025-07-15T07:58:32.464Z", "summary": {"passed": 15, "failed": 2, "warnings": 3, "passRate": 75}, "details": [{"type": "pass", "message": "Vue配置包含Gzip压缩", "details": ""}, {"type": "pass", "message": "Vue配置包含代码分割", "details": ""}, {"type": "pass", "message": "Vue配置包含支付SDK分离", "details": ""}, {"type": "pass", "message": "Axios已升级到安全版本", "details": ""}, {"type": "pass", "message": "Vue已升级到2.7版本", "details": ""}, {"type": "pass", "message": "已安装Gzip压缩插件", "details": ""}, {"type": "pass", "message": "已安装图片优化插件", "details": ""}, {"type": "warn", "message": "无法运行安全审计", "details": "Command failed: npm audit --audit-level=high --json"}, {"type": "info", "message": "node_modules 大小: 476M", "details": ""}, {"type": "pass", "message": "src/utils/performance-monitor.js 存在", "details": ""}, {"type": "pass", "message": "src/utils/api-cache.js 存在", "details": ""}, {"type": "pass", "message": "src/server/http.optimized.js 存在", "details": ""}, {"type": "pass", "message": "config/service-worker.optimized.js 存在", "details": ""}, {"type": "pass", "message": "scripts/performance-test.js 存在", "details": ""}, {"type": "warn", "message": "性能监控未集成到main.js", "details": ""}, {"type": "pass", "message": "生产环境已禁用source map", "details": ""}, {"type": "pass", "message": "已配置性能预算", "details": ""}, {"type": "pass", "message": "已配置缓存组", "details": ""}, {"type": "fail", "message": "构建测试失败", "details": "Command failed: npm run build:release\n ERROR  ValidationError: Invalid options object. Copy Plugin has been initialized using an options object that does not match the API schema.\n         - options[0] has an unknown property 'from'. These properties are valid:\n           object { patterns, options? }\n         - options[1] has an unknown property 'to'. These properties are valid:\n           object { patterns, options? }\n         - options[2] has an unknown property 'from'. These properties are valid:\n           object { patterns, options? }\nValidationError: Invalid options object. Copy Plugin has been initialized using an options object that does not match the API schema.\n - options[0] has an unknown property 'from'. These properties are valid:\n   object { patterns, options? }\n - options[1] has an unknown property 'to'. These properties are valid:\n   object { patterns, options? }\n - options[2] has an unknown property 'from'. These properties are valid:\n   object { patterns, options? }\n    at validate (/Users/<USER>/work/pay-web/web-pay-hub/node_modules/copy-webpack-plugin/node_modules/schema-utils/dist/validate.js:166:11)\n    at new CopyPlugin (/Users/<USER>/work/pay-web/web-pay-hub/node_modules/copy-webpack-plugin/dist/index.js:172:5)\n    at Object.<anonymous> (/Users/<USER>/work/pay-web/web-pay-hub/vue.config.js:24:7)\n    at Module._compile (internal/modules/cjs/loader.js:999:30)\n    at Object.Module._extensions..js (internal/modules/cjs/loader.js:1027:10)\n    at Module.load (internal/modules/cjs/loader.js:863:32)\n    at Function.Module._load (internal/modules/cjs/loader.js:708:14)\n    at Module.require (internal/modules/cjs/loader.js:887:19)\n    at require (internal/modules/cjs/helpers.js:74:18)\n    at exports.loadModule (/Users/<USER>/work/pay-web/web-pay-hub/node_modules/@vue/cli-shared-utils/lib/module.js:86:14)\nnpm ERR! code ELIFECYCLE\nnpm ERR! errno 1\nnpm ERR! web-pay-unique_common@0.2.0 build:release: `vue-cli-service build --mode release`\nnpm ERR! Exit status 1\nnpm ERR! \nnpm ERR! Failed at the web-pay-unique_common@0.2.0 build:release script.\nnpm ERR! This is probably not a problem with npm. There is likely additional logging output above.\n\nnpm ERR! A complete log of this run can be found in:\nnpm ERR!     /Users/<USER>/.npm/_logs/2025-07-15T07_58_32_431Z-debug.log\n"}, {"type": "fail", "message": "JavaScript总大小: 2.90MB (过大，建议: <0.5MB)", "details": ""}, {"type": "info", "message": "CSS总大小: 869.07KB", "details": ""}, {"type": "warn", "message": "没有发现Gzip压缩文件", "details": ""}]}