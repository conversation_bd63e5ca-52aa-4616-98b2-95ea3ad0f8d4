{"version": 3, "sources": ["webpack:///./src/assets/common/icon/fp-logo.png", "webpack:///./src/components/CheckoutCounterCN.vue?0824", "webpack:///./src/components/AddScreenBtn.vue?4453", "webpack:///./src/components/EntranceOfBoon.vue?f166", "webpack:///./src/views/Pay.vue", "webpack:///./src/components/CommonFooter.vue", "webpack:///src/components/CommonFooter.vue", "webpack:///./src/components/CommonFooter.vue?2c33", "webpack:///./src/components/CommonFooter.vue?8643", "webpack:///./src/components/CheckoutCounterTax.vue", "webpack:///./src/components/CheckoutCounter.js", "webpack:///src/components/CheckoutCounterTax.vue", "webpack:///./src/components/CheckoutCounterTax.vue?596e", "webpack:///./src/components/CheckoutCounterTax.vue?acff", "webpack:///./src/components/CheckoutCounterCN.vue", "webpack:///src/components/CheckoutCounterCN.vue", "webpack:///./src/components/CheckoutCounterCN.vue?1acf", "webpack:///./src/components/CheckoutCounterCN.vue?308d", "webpack:///./src/components/AddScreenBtn.vue", "webpack:///src/components/AddScreenBtn.vue", "webpack:///./src/components/AddScreenBtn.vue?8327", "webpack:///./src/components/AddScreenBtn.vue?0a3e", "webpack:///./src/components/AddIosSafariBtn.vue", "webpack:///src/components/AddIosSafariBtn.vue", "webpack:///./src/components/AddIosSafariBtn.vue?3fa0", "webpack:///./src/components/AddIosSafariBtn.vue?de23", "webpack:///./src/components/DiamondChooseKOA.vue", "webpack:///./src/components/ExtraDiamond.vue", "webpack:///src/components/ExtraDiamond.vue", "webpack:///./src/components/ExtraDiamond.vue?aa0c", "webpack:///./src/components/ExtraDiamond.vue?c363", "webpack:///src/components/DiamondChooseKOA.vue", "webpack:///./src/components/DiamondChooseKOA.vue?9269", "webpack:///./src/components/DiamondChooseKOA.vue?21c1", "webpack:///./src/components/EntranceOfBoon.vue", "webpack:///src/components/EntranceOfBoon.vue", "webpack:///./src/components/EntranceOfBoon.vue?ca5b", "webpack:///./src/components/EntranceOfBoon.vue?21b7", "webpack:///./src/components/CheckoutCounterSDK.vue", "webpack:///src/components/CheckoutCounterSDK.vue", "webpack:///./src/components/CheckoutCounterSDK.vue?7bdd", "webpack:///./src/components/CheckoutCounterSDK.vue?b2fb", "webpack:///src/views/Pay.vue", "webpack:///./src/views/Pay.vue?bea1", "webpack:///./src/views/Pay.vue?4362", "webpack:///./src/components/DiamondChooseKOA.vue?9cac", "webpack:///./src/assets/common/channel/mycard-logo.png", "webpack:///./src/components/CommonFooter.vue?ac38", "webpack:///./src/components/CheckoutCounterSDK.vue?6e83", "webpack:///./src/components/ExtraDiamond.vue?736e", "webpack:///./src/components/AddIosSafariBtn.vue?eb0f", "webpack:///./src/views/Pay.vue?ee78", "webpack:///./src/components/CheckoutCounterTax.vue?862c"], "names": ["module", "exports", "render", "_vm", "this", "_c", "_self", "class", "$gameName", "sdk", "IS_CHECKOUT_SDK", "attrs", "isPc", "staticClass", "_v", "_s", "$vt", "$t", "$store", "state", "functionSwitch", "showPcDiscountTips", "_e", "on", "$event", "$root", "$emit", "boon", "gameinfo", "isCn", "disable", "requestLoading", "getters", "judgeRisk", "vip", "isNewUser", "is<PERSON>ogin", "country", "mainBody", "isMobile", "showMobilePolicy", "directives", "name", "rawName", "value", "expression", "staticRenderFns", "showFooter", "staticStyle", "require", "_m", "computed", "includes", "component", "expandMode", "scopedSlots", "_u", "key", "fn", "taxCost", "extraCost", "active", "proxy", "isArZone", "FinalPriceState", "rawNowPrice", "rawOriginPrice", "offCountTips", "domProps", "currencyUnit", "finalNowPrice", "showTaxBtn", "finalOriginPrice", "components", "CommonPart", "data", "mapState", "mapGetters", "chosen<PERSON><PERSON><PERSON><PERSON>", "taxation", "<PERSON><PERSON><PERSON><PERSON>", "extra_fee_amount", "watch", "newValue", "mixins", "checkoutCounter", "gameCode", "FE_INDEX", "feType", "_f", "discount_price", "price", "nowPrice", "rateWidthOutPercent", "deduct_price", "rate", "nowPriceWidthTax", "defaultDiscountInfo", "created", "$on", "item", "deferred<PERSON>rompt", "goInstall", "displayMode", "isStandalone", "window", "matchMedia", "matches", "document", "referrer", "startsWith", "navigator", "standalone", "__deferredPrompt", "undefined", "methods", "showBtn", "addEventListener", "e", "preventDefault", "$gtag", "event", "event_label", "prompt", "userChoice", "then", "choiceResult", "outcome", "console", "log", "displayModeCheck", "setInterval", "clearInterval", "ua", "userAgent", "toLowerCase", "$gcbk", "unwatch", "$watch", "val", "$nextTick", "gsap", "from", "height", "duration", "clearProps", "isShowBtn", "showTimes", "defaultShowTimes", "hadInstall", "showGuide", "isShowGuide", "stopPropagation", "gameName", "slot", "Number", "localStorage", "getItem", "isQuickApp", "is<PERSON><PERSON><PERSON><PERSON><PERSON>", "indexOf", "setItem", "rejudge", "projectId", "pwaOpenAction", "params", "p0", "p1", "p2", "$loading", "show", "ameHoldByGet", "res", "calcDisplayMode", "code", "length", "cancel", "finally", "hide", "game", "toUpperCase", "_l", "diamondList", "diamondItem", "index", "product_id", "soldOut", "toggleStatus", "type", "lastDiamondCalc", "totalDiamond", "showBonus", "diamondBonus", "coin", "chosen<PERSON><PERSON>", "target", "composing", "showPopTips", "diamondState", "priceState", "originPrice", "isKOA", "firstPayProducts", "bonusState", "total_purchase_times", "purchased_times", "chosenS<PERSON><PERSON><PERSON><PERSON><PERSON>", "showList", "currency_symbol", "extraList", "commit", "minCustomDiamondNum", "ExtraDiamond", "OverSizeScale", "canICustom", "lastDiamondItem", "isInit", "isFirstPayUsed", "level_currency_price", "currency", "tax_rate", "taxRate", "obj", "Object", "assign", "defaultPrice", "priceHelper", "pickChosenCouponInfo", "formatState", "couponInfo", "no_tax_price", "firstPayCoupon", "productId", "chosen<PERSON><PERSON><PERSON>n<PERSON><PERSON>", "isDiamondOwn95Off", "formdata", "isDiamondOwnRebate", "getRebateCoin", "levelCoin", "defaultRebateDynamicInfoAll", "level_coin", "discount", "Math", "floor", "minimumDiamondId", "smallD<PERSON>ondDoubleDiscount", "showDoubleDiamond", "isDiscountUsed", "loadDiamondList", "getTokenList", "store_from", "sort", "a", "b", "map", "initCustomParams", "entries", "$route", "query", "pd", "findIndex", "initFixCouponByDiamond4", "$toast", "err", "diamond", "cb", "toggleCustom", "bind", "aim", "splice", "push", "initSmallDiamond", "getAmeDo", "is_received", "judge<PERSON><PERSON><PERSON><PERSON><PERSON>bleDiscountStatus", "setTimeout", "smallDiamondEvent", "getRate", "toFixed", "getActivityListForToken", "defaultDiscountOrigin", "fixed_discount", "fixedRebateOrigin", "fixed_rebate", "go", "showDoubleExperience", "rotate", "loginAction", "getLoginReward", "getPwaReward", "clicked", "gain", "getStatus", "gotDailyReward", "informServer", "taskId", "ameParams", "ameDoByGet", "getReward", "checkAddBtn", "btn", "querySelector", "body", "style", "zoom", "paddingTop", "getPWADisplayMode", "<PERSON><PERSON><PERSON><PERSON>", "props", "CheckoutCounterSDK", "RefundPolicy", "EntranceOfBoon", "DiamondChooseKOA", "AddScreenBtn", "AddIosSafariBtn", "CheckoutFooter", "CheckoutCounter", "CouponChoose", "ChannelChoose", "LoginModule", "<PERSON>Footer", "CheckoutCounterCn", "CommonFooterCn", "CommonFooter<PERSON><PERSON><PERSON>", "PrivatePermission", "DirectGiftPackage", "PayMixin", "showDiamondPop", "gallery", "$imageLoader", "img", "Image", "src", "imageUrl", "onload", "onerror", "initShowDoubleExperience", "p3", "double_flage"], "mappings": "qHAAAA,EAAOC,QAAU,0lF,+GCAjB,W,oCC<PERSON>,W,6DCAA,W,2CCAA,IAAIC,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACE,MAAM,CAAC,mBAAoBJ,EAAIK,UAAW,CAAEC,IAAKN,EAAIO,kBAAmBC,MAAM,CAAC,GAAK,qBAAqB,CAAER,EAAIS,OAAST,EAAIO,gBAAiB,CAACL,EAAG,UAAU,CAACQ,YAAY,sBAAsB,CAACR,EAAG,kBAAkBA,EAAG,sBAAsBA,EAAG,MAAM,CAACQ,YAAY,cAAc,CAACV,EAAIW,GAAGX,EAAIY,GAAGZ,EAAIa,IAAI,cAAc,KAAKX,EAAG,OAAO,CAACF,EAAIW,GAAGX,EAAIY,GAAGZ,EAAIc,GAAG,qBAAqB,KAAKZ,EAAG,SAASA,EAAG,MAAM,CAACQ,YAAY,uCAAuC,CAACR,EAAG,MAAM,CAACQ,YAAY,aAAa,CAACR,EAAG,MAAM,CAACQ,YAAY,SAASR,EAAG,MAAM,CAACQ,YAAY,SAASR,EAAG,IAAI,CAACQ,YAAY,eAAe,CAACR,EAAG,OAAO,CAACF,EAAIW,GAAGX,EAAIY,GAAGZ,EAAIa,IAAI,0BAA2Bb,EAAIe,OAAOC,MAAMC,eAAeC,mBAAoBhB,EAAG,OAAO,CAACF,EAAIW,GAAGX,EAAIY,GAAGZ,EAAIa,IAAI,sBAAsBb,EAAImB,OAAOjB,EAAG,MAAM,CAACQ,YAAY,sBAAsBU,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOrB,EAAIsB,MAAMC,MAAM,UAAW,yBAAyB,CAACrB,EAAG,KAAKF,EAAIW,GAAGX,EAAIY,GAAGZ,EAAIc,GAAG,uBAAuB,SAASZ,EAAG,MAAM,CAACQ,YAAY,cAAc,CAACR,EAAG,gBAAiBF,EAAIwB,KAAMtB,EAAG,oBAAoBF,EAAImB,KAAKjB,EAAG,iBAAiBA,EAAG,wBAAwBA,EAAG,kBAAmBF,EAAIe,OAAOC,MAAMS,SAASC,KAAMxB,EAAG,uBAAuBA,EAAG,oBAAoBA,EAAG,MAAM,CAACQ,YAAY,YAAY,CAACR,EAAG,OAAO,CAACQ,YAAY,YAAYN,MAAM,CAAC,CAACuB,QAAS3B,EAAI4B,gBAAkB5B,EAAIe,OAAOc,QAAQ,gCAAgCT,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOrB,EAAI8B,eAAe,CAAC9B,EAAIW,GAAGX,EAAIY,GAAGZ,EAAIc,GAAG,aAAa,KAAMd,EAAI+B,IAAIC,YAAchC,EAAIiC,QAAS/B,EAAG,KAAKF,EAAImB,SAAuC,OAA7BnB,EAAIe,OAAOC,MAAMkB,SAAoBlC,EAAIiC,QAAS/B,EAAG,cAAc,CAACA,EAAG,uBAAuB,GAAGF,EAAImB,MAAM,MAAM,GAAInB,EAAIe,OAAOC,MAAMS,SAASC,KAAMxB,EAAG,oBAAoBF,EAAImB,KAAMnB,EAAIe,OAAOC,MAAMS,SAASU,SAAUjC,EAAG,sBAAsBA,EAAG,kBAAkBF,EAAImB,KAAMnB,EAAIoC,WAAapC,EAAIO,gBAAiB,CAACL,EAAG,MAAM,CAACQ,YAAY,uBAAuB,CAACR,EAAG,kBAAkBA,EAAG,sBAAsBA,EAAG,gBAAiBF,EAAIwB,KAAMtB,EAAG,oBAAoBF,EAAImB,KAAKjB,EAAG,iBAAiBA,EAAG,wBAAwBA,EAAG,kBAAgD,OAA7BF,EAAIe,OAAOC,MAAMkB,SAAoBlC,EAAIiC,QAAS/B,EAAG,cAAc,CAACA,EAAG,uBAAuB,GAAIF,EAAIqC,iBAAkBnC,EAAG,iBAAiBF,EAAImB,KAAMnB,EAAIe,OAAOC,MAAMS,SAASC,KAAMxB,EAAG,oBAAoBF,EAAImB,MAAM,GAAGjB,EAAG,kBAAkB,CAACM,MAAM,CAAC,kBAAkBR,EAAI4B,gBAAgBR,GAAG,CAAC,cAAgB,SAASC,GAAQ,OAAOrB,EAAI8B,iBAAiB9B,EAAImB,KAAMnB,EAAIO,gBAAiB,CAACL,EAAG,MAAM,CAACQ,YAAY,oBAAoB,CAACR,EAAG,uBAAuBA,EAAG,iBAAiBA,EAAG,kBAAgD,OAA7BF,EAAIe,OAAOC,MAAMkB,SAAoBlC,EAAIiC,QAAS/B,EAAG,cAAc,CAACA,EAAG,uBAAuB,GAAGF,EAAImB,KAAKjB,EAAG,yBAAyB,CAACM,MAAM,CAAC,kBAAkBR,EAAI4B,gBAAgBR,GAAG,CAAC,cAAgB,SAASC,GAAQ,OAAOrB,EAAI8B,gBAAgB5B,EAAG,eAAe,CAACoC,WAAW,CAAC,CAACC,KAAK,OAAOC,QAAQ,SAASC,OAAO,EAAOC,WAAW,aAAa,GAAGxC,EAAG,kBAAkB,CAACM,MAAM,CAAC,kBAAkBR,EAAI4B,gBAAgBR,GAAG,CAAC,cAAgB,SAASC,GAAQ,OAAOrB,EAAI8B,gBAAgB5B,EAAG,kBAAkBF,EAAImB,MAAM,IAErjGwB,EAAkB,GCFlB5C,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAQF,EAAI4C,WAAY1C,EAAG,MAAM,CAACQ,YAAY,aAAa,CAACR,EAAG,MAAM,CAAC2C,YAAY,CAAC,iBAAiB,cAAc,gBAAgB,QAAQrC,MAAM,CAAC,IAAMsC,EAAQ,QAAqC,IAAM,aAAa9C,EAAI+C,GAAG,KAAK/C,EAAImB,MAE/RwB,EAAkB,CAAC,WAAY,IAAI3C,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,IAAI,CAACF,EAAIW,GAAG,kFAAkFT,EAAG,IAAI,CAAC2C,YAAY,CAAC,MAAQ,WAAWrC,MAAM,CAAC,KAAO,sCAAsC,OAAS,WAAW,CAACR,EAAIW,GAAG,oBAAoBX,EAAIW,GAAG,OAAOT,EAAG,IAAI,CAAC2C,YAAY,CAAC,MAAQ,WAAWrC,MAAM,CAAC,KAAO,wCAAwC,OAAS,WAAW,CAACR,EAAIW,GAAG,0BAA0BX,EAAIW,GAAG,SAAST,EAAG,IAAI,CAAC2C,YAAY,CAAC,MAAQ,WAAWrC,MAAM,CAAC,KAAO,mDAAmD,OAAS,WAAW,CAACR,EAAIW,GAAG,mBAAmBX,EAAIW,GAAG,UCatnB,GACf4B,KAAA,eACAS,SAAA,CACAJ,aACA,qBAAAK,SAAA,KAAA5C,cCnBqV,I,wBCQjV6C,EAAY,eACd,EACA,EACA,GACA,EACA,KACA,WACA,MAIa,EAAAA,E,4CCnBXnD,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAQF,EAAImD,WAAYjD,EAAG,cAAc,CAACE,MAAM,CAAC,SAAUJ,EAAIK,WAAWG,MAAM,CAAC,GAAK,2BAA2B4C,YAAYpD,EAAIqD,GAAG,CAAC,CAACC,IAAI,QAAQC,GAAG,WAAW,MAAO,CAACrD,EAAG,MAAM,CAACQ,YAAY,aAAa,CAACV,EAAIW,GAAGX,EAAIY,GAAGZ,EAAIc,GAAG,qBAAsBd,EAAIwD,QAAStD,EAAG,MAAM,CAACQ,YAAY,OAAO,CAACV,EAAIW,GAAGX,EAAIY,GAAGZ,EAAIc,GAAG,eAAed,EAAImB,KAAMnB,EAAIyD,UAAWvD,EAAG,MAAM,CAACQ,YAAY,OAAO,CAACV,EAAIW,GAAGX,EAAIY,GAAGZ,EAAIc,GAAG,iBAAiBd,EAAImB,KAAKjB,EAAG,MAAM,CAACQ,YAAY,SAAS,CAACV,EAAIW,GAAG,IAAIX,EAAIY,GAAGZ,EAAIc,GAAG,kBAAkBZ,EAAG,OAAO,CAACQ,YAAY,OAAON,MAAM,CAACsD,OAAQ1D,EAAImD,YAAY/B,GAAG,CAAC,MAAQ,SAASC,GAAQrB,EAAImD,YAAcnD,EAAImD,cAAc,CAACnD,EAAIW,GAAG,KAAKX,EAAIY,GAAGZ,EAAIc,GAAG,aAAaZ,EAAG,SAASyD,OAAM,IAAO,MAAK,EAAM,aAAa,CAACzD,EAAG,MAAM,CAACQ,YAAY,iBAAiB,CAACR,EAAG,OAAO,CAACQ,YAAY,YAAYN,MAAM,CAAC,aAAcJ,EAAI4D,WAAW,CAAC5D,EAAIW,GAAGX,EAAIY,GAAGZ,EAAI6D,gBAAgBC,gBAAiB9D,EAAI6D,gBAAgBE,eAAgB7D,EAAG,OAAO,CAACE,MAAM,CAAC,eAAgB,CAAC,aAAcJ,EAAI4D,YAAY,CAAC5D,EAAIW,GAAGX,EAAIY,GAAGZ,EAAI6D,gBAAgBE,mBAAmB/D,EAAImB,KAAMnB,EAAI6D,gBAAgBG,aAAc9D,EAAG,MAAM,CAACQ,YAAY,iBAAiBuD,SAAS,CAAC,UAAYjE,EAAIY,GAAGZ,EAAI6D,gBAAgBG,iBAAiBhE,EAAImB,OAAQnB,EAAIwD,QAAStD,EAAG,MAAM,CAACQ,YAAY,eAAe,CAACV,EAAIW,GAAGX,EAAIY,GAAGZ,EAAIwD,SAAS,IAAIxD,EAAIY,GAAGZ,EAAIkE,iBAAiBlE,EAAImB,KAAMnB,EAAIyD,UAAWvD,EAAG,MAAM,CAACQ,YAAY,eAAe,CAACV,EAAIW,GAAGX,EAAIY,GAAGZ,EAAIyD,WAAW,IAAIzD,EAAIY,GAAGZ,EAAIkE,iBAAiBlE,EAAImB,KAAKjB,EAAG,MAAM,CAACQ,YAAY,eAAe,CAACV,EAAIW,GAAGX,EAAIY,GAAGZ,EAAI6D,gBAAgBM,oBAAoBjE,EAAG,cAAc,CAACE,MAAM,CAAC,SAAUJ,EAAIK,WAAWG,MAAM,CAAC,GAAK,2BAA2B4C,YAAYpD,EAAIqD,GAAG,CAAC,CAACC,IAAI,QAAQC,GAAG,WAAW,MAAO,CAACrD,EAAG,MAAM,CAACQ,YAAY,SAAS,CAACV,EAAIW,GAAGX,EAAIY,GAAGZ,EAAIc,GAAG,kBAAmBd,EAAIoE,WAAYlE,EAAG,OAAO,CAACQ,YAAY,OAAON,MAAM,CAACsD,OAAQ1D,EAAImD,YAAY/B,GAAG,CAAC,MAAQ,SAASC,GAAQrB,EAAImD,YAAcnD,EAAImD,cAAc,CAACnD,EAAIW,GAAG,KAAKX,EAAIY,GAAGZ,EAAIc,GAAG,aAAaZ,EAAG,OAAOF,EAAImB,OAAOwC,OAAM,MAAS,CAACzD,EAAG,MAAM,CAACQ,YAAY,cAAcF,MAAM,CAAC,GAAK,gBAAgB,CAACN,EAAG,OAAO,CAACQ,YAAY,YAAYN,MAAM,CAAC,aAAcJ,EAAI4D,WAAW,CAAC5D,EAAIW,GAAGX,EAAIY,GAAGZ,EAAI6D,gBAAgBM,kBAAmBnE,EAAI6D,gBAAgBQ,iBAAkBnE,EAAG,OAAO,CAACQ,YAAY,eAAeN,MAAM,CAAC,aAAcJ,EAAI4D,WAAW,CAAC5D,EAAIW,GAAGX,EAAIY,GAAGZ,EAAI6D,gBAAgBQ,qBAAqBrE,EAAImB,KAAMnB,EAAI6D,gBAAgBG,aAAc9D,EAAG,MAAM,CAACQ,YAAY,iBAAiBuD,SAAS,CAAC,UAAYjE,EAAIY,GAAGZ,EAAI6D,gBAAgBG,iBAAiBhE,EAAImB,UAEv+EwB,EAAkB,G,wBCCP,GACbJ,KAAM,qBACN+B,WAAY,CAAEC,mBACdC,OACE,MAAO,CACLrB,YAAY,IAGhBH,SAAU,IACLyB,eAAS,CAAC,YAAa,WAAY,eAAgB,uBACnDA,eAAS,WAAY,CAAC,gBAAiB,gBAAiB,eAAgB,WACxEA,eAAS,WAAY,CAAC,uBACtBA,eAAS,WAAY,CAAC,eACtBC,eAAW,WAAY,CAAC,kBAAmB,gBAAiB,qBAC/DlB,UACE,OAAOvD,KAAK0E,aAAaC,UAAY3E,KAAK4D,gBAAgBe,UAAY3E,KAAK4E,cAAcD,UAE3FnB,YACE,OAAOxD,KAAK0E,aAAaG,kBAAoB7E,KAAK4D,gBAAgBiB,kBAAoB7E,KAAK4E,cAAcC,kBAE3GV,aACE,OAAOnE,KAAKuD,SAAWvD,KAAKwD,YAGhCsB,MAAO,CACLX,WAAYY,GACLA,IAAU/E,KAAKkD,YAAa,MCSxB,GACfZ,KAAA,qBACA0C,OAAA,CAAAC,ICxC2V,ICQvV,G,UAAY,eACd,EACA,EACA,GACA,EACA,KACA,WACA,OAIa,I,QCnBXnF,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,cAAc,CAACE,MAAMJ,EAAIe,OAAOC,MAAMS,SAAS0D,SAAStC,YAAY,CAAC,MAAQ,QAAQrC,MAAM,CAAC,aAAa,OAAO,CAACN,EAAG,MAAM,CAACQ,YAAY,cAAcF,MAAM,CAAC,GAAK,gBAAgB,CAAER,EAAI2E,aAAaS,SAAU,CAAClF,EAAG,OAAO,CAACQ,YAAY,YAAYN,MAAM,CAAC,aAAcJ,EAAI4D,WAAW,CAA4B,cAA1B5D,EAAI2E,aAAaU,OAAsB,CAACrF,EAAIW,GAAGX,EAAIY,GAAGZ,EAAIkE,cAAclE,EAAIY,GAAGZ,EAAIsF,GAAG,cAAPtF,CAAsBA,EAAI2E,aAAaY,mBAAmBvF,EAAImB,KAAgC,oBAA1BnB,EAAI2E,aAAaU,OAA4B,CAACrF,EAAIW,GAAGX,EAAIY,GAAGZ,EAAIkE,cAAclE,EAAIY,GAAGZ,EAAIsF,GAAG,cAAPtF,CAAsBA,EAAI2E,aAAaY,mBAAmBvF,EAAImB,KAAgC,gBAA1BnB,EAAI2E,aAAaU,OAAwB,CAACrF,EAAIW,GAAGX,EAAIY,GAAGZ,EAAIkE,cAAclE,EAAIY,GAAGZ,EAAIsF,GAAG,cAAPtF,CAAsBA,EAAI2E,aAAaa,UAAUxF,EAAImB,KAAgC,kBAA1BnB,EAAI2E,aAAaU,OAA0B,CAACrF,EAAIW,GAAGX,EAAIY,GAAGZ,EAAIkE,cAAclE,EAAIY,GAAGZ,EAAIsF,GAAG,cAAPtF,CAAsBA,EAAI2E,aAAaa,UAAUxF,EAAImB,MAAM,GAA8B,kBAA1BnB,EAAI2E,aAAaU,OAA0BnF,EAAG,OAAO,CAACE,MAAM,CAAC,eAAgB,CAAC,aAAcJ,EAAI4D,YAAY,CAAC5D,EAAIW,GAAGX,EAAIY,GAAGZ,EAAIkE,cAAclE,EAAIY,GAAGZ,EAAIsF,GAAG,cAAPtF,CAAsBA,EAAI6E,cAAcY,UAAYzF,EAAI6E,cAAcW,WAAWxF,EAAImB,KAAKjB,EAAG,MAAM,CAACQ,YAAY,kBAAkB,CAA4B,cAA1BV,EAAI2E,aAAaU,OAAsB,CAACrF,EAAIW,GAAGX,EAAIY,GAAG,MAAMZ,EAAI2E,aAAae,2BAA2B1F,EAAImB,KAAgC,oBAA1BnB,EAAI2E,aAAaU,OAA4B,CAACrF,EAAIW,GAAGX,EAAIY,GAAGZ,EAAI2E,aAAae,qBAAqB,UAAU1F,EAAImB,KAAgC,gBAA1BnB,EAAI2E,aAAaU,OAAwB,CAACnF,EAAG,OAAO,CAACE,MAAM,CAAC,aAAcJ,EAAI4D,WAAW,CAAC5D,EAAIW,GAAG,KAAKX,EAAIY,GAAGZ,EAAIkE,cAAclE,EAAIY,GAAGZ,EAAI2E,aAAagB,kBAAkB3F,EAAImB,KAAgC,kBAA1BnB,EAAI2E,aAAaU,OAA0B,CAACnF,EAAG,OAAO,CAACE,MAAM,CAAC,aAAcJ,EAAI4D,WAAW,CAAC5D,EAAIW,GAAG,KAAKX,EAAIY,GAAGZ,EAAI2E,aAAaiB,MAAM,KAAK1F,EAAG,IAAI,CAACQ,YAAY,oBAAoBV,EAAImB,MAAM,IAAIjB,EAAG,OAAO,CAACE,MAAM,CAAC,aAAcJ,EAAI4D,WAAW,CAAC5D,EAAIW,GAAGX,EAAIY,GAAGZ,EAAIkE,cAAclE,EAAIY,GAAGZ,EAAIsF,GAAG,cAAPtF,CAAsBA,EAAI6E,cAAcgB,kBAAoB7F,EAAI6E,cAAcW,YAAY,MAEz8D7C,EAAkB,GC0BP,GACfJ,KAAA,kBACA+B,WAAA,CAAAC,mBACAC,OACA,OACAsB,oBAAA,KAGA9C,SAAA,IACAyB,eAAA,4CACAA,eAAA,gEACAA,eAAA,iCAEAsB,UACA,KAAAzE,MAAA0E,IAAA,yBAAAC,IACA,KAAAH,oBAAAG,MC3C0V,ICQtV,G,UAAY,eACd,EACA,EACA,GACA,EACA,KACA,WACA,OAIa,I,gCCnBXlG,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACE,MAAM,CAAC,wBAAyB,CAACJ,EAAIK,aAAa,CAACH,EAAG,aAAa,CAACM,MAAM,CAAC,KAAO,cAAc,CAAER,EAAIkG,gBAAgBlG,EAAIe,OAAOC,MAAMoB,SAAUlC,EAAG,MAAM,CAACQ,YAAY,6BAA6BU,GAAG,CAAC,MAAQpB,EAAImG,YAAY,CAACnG,EAAIW,GAAGX,EAAIY,GAAGZ,EAAIc,GAAG,qBAAqBd,EAAImB,OAAQnB,EAAIkG,gBAAgBlG,EAAIe,OAAOC,MAAMP,KAAMP,EAAG,MAAM,CAACQ,YAAY,0BAA0B,CAACV,EAAIW,GAAG,IAAIX,EAAIY,GAAGZ,EAAIc,GAAG,oBAAoB,IAAId,EAAIY,GAAGZ,EAAIc,GAAG,oBAAoB,KAAKZ,EAAG,OAAO,CAACQ,YAAY,YAAYU,GAAG,CAAC,MAAQpB,EAAImG,YAAY,CAACnG,EAAIW,GAAGX,EAAIY,GAAGZ,EAAIc,GAAG,uBAAuBd,EAAImB,MAAM,IAE1oBwB,EAAkB,GCatB,SAAAyD,IACA,MAAAC,EAAAC,OAAAC,WAAA,8BAAAC,QACA,OAAAC,SAAAC,SAAAC,WAAA,kBACA,MACAC,UAAAC,YAAAR,EACA,aAEA,UAEe,OACf9D,KAAA,eACAiC,OACA,OACA0B,eAAAI,OAAAQ,uBAAAC,IAGAC,QAAA,CACAC,UACAX,OAAAY,iBAAA,sBAAAC,IAEAA,EAAAC,iBAEA,KAAAlB,eAAAiB,KAGAhB,YACA,KAAAkB,MAAAC,MAAA,wBAAAC,YAAA,KAAAxG,OAAAC,MAAAoB,SAAA,gBACA,KAAA8D,eAAAsB,SAEA,KAAAtB,eAAAuB,WACAC,KAAAC,IACA,gBAAAA,EAAAC,QAAA,CACAC,QAAAC,IAAA,iCACA,MAAAC,EAAAC,YAAA,KACA,eAAA5B,MACA6B,cAAAF,GACA,KAAAzG,MAAAC,MAAA,uBAEA,UAEAsG,QAAAC,IAAA,kCAEA,KAAA5B,oBAAAa,MAIAhB,UACA,MAAAmC,GAAAtB,UAAAuB,WAAA,IAAAC,cAGA,GAFAF,EAAAjF,SAAA,gBAAAgE,UAEA,KAAAoB,MAAA,mCAAAtH,OAAAC,MAAAP,KAAA,CACA,MAAA6H,EAAA,KAAAC,OAAA,iBAAAC,IACAA,IACA,KAAAC,UAAA,IAAAC,WAAAC,KAAA,2BAAAC,OAAA,EAAAC,SAAA,GAAAC,WAAA,YACAR,UCrEqV,ICQjV,G,UAAY,eACd,EACA,EACA,GACA,EACA,KACA,WACA,OAIa,I,QCnBXvI,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACE,MAAM,CAAC,kBAAmBJ,EAAIK,WAAWG,MAAM,CAAC,GAAK,oBAAoB,CAACN,EAAG,aAAa,CAACM,MAAM,CAAC,KAAO,cAAc,CAAGR,EAAI+I,WAAa/I,EAAIgJ,UAAYhJ,EAAIiJ,mBAAsBhJ,KAAKiJ,WAAYhJ,EAAG,MAAM,CAACQ,YAAY,mCAAmCU,GAAG,CAAC,MAAQpB,EAAImJ,YAAY,CAACnJ,EAAIW,GAAGX,EAAIY,GAAGZ,EAAIc,GAAG,qBAAqBd,EAAImB,OAAOjB,EAAG,aAAa,CAACM,MAAM,CAAC,KAAO,aAAa,CAACN,EAAG,MAAM,CAACoC,WAAW,CAAC,CAACC,KAAK,OAAOC,QAAQ,SAASC,MAAOzC,EAAIoJ,YAAa1G,WAAW,gBAAgBhC,YAAY,YAAYN,MAAM,CAAEsD,OAAQ1D,EAAIoJ,aAAchI,GAAG,CAAC,MAAQ,SAASC,GAAQrB,EAAIoJ,aAAc,KAAS,CAAClJ,EAAG,MAAM,CAACQ,YAAY,aAAaU,GAAG,CAAC,MAAQ,SAASC,GAAQA,EAAOgI,qBAAsB,CAACnJ,EAAG,MAAM,CAACQ,YAAY,QAAQU,GAAG,CAAC,MAAQ,SAASC,GAAQrB,EAAIoJ,aAAc,MAAUlJ,EAAG,MAAM,CAACQ,YAAY,SAAS,CAACV,EAAIW,GAAGX,EAAIY,GAAGZ,EAAIc,GAAG,mBAAmBZ,EAAG,MAAM,CAACQ,YAAY,YAAY,CAACV,EAAIW,GAAGX,EAAIY,GAAGZ,EAAIc,GAAG,gBAAgB,IAAId,EAAIY,GAAGZ,EAAIc,GAAG,mBAAmBZ,EAAG,MAAM,CAACQ,YAAY,SAASR,EAAG,MAAM,CAACQ,YAAY,QAAQ,CAACV,EAAIW,GAAGX,EAAIY,GAAGZ,EAAIsJ,UAAU,kBAAkBpJ,EAAG,MAAM,CAACQ,YAAY,UAAUR,EAAG,OAAO,CAACQ,YAAY,MAAMF,MAAM,CAAC,KAAO,cAAc,IAAM,QAAQ,CAACN,EAAG,MAAM,CAACQ,YAAY,KAAKF,MAAM,CAAC,KAAO,GAAG+I,KAAK,MAAMrJ,EAAG,OAAO,CAACQ,YAAY,MAAMF,MAAM,CAAC,KAAO,cAAc,IAAM,QAAQ,CAACN,EAAG,MAAM,CAACQ,YAAY,MAAMF,MAAM,CAAC,KAAO,GAAG+I,KAAK,IAAIrJ,EAAG,WAAW,CAACqJ,KAAK,GAAG,CAACvJ,EAAIW,GAAGX,EAAIY,GAAGZ,EAAIc,GAAG,oBAAoB,GAAGZ,EAAG,MAAM,CAACQ,YAAY,QAAQ,CAACR,EAAG,MAAM,CAACF,EAAIW,GAAGX,EAAIY,GAAGZ,EAAIc,GAAG,qBAAqBZ,EAAG,MAAM,CAACQ,YAAY,SAAS,QAAQ,IAEllDiC,EAAkB,G,YCkCP,GACfJ,KAAA,kBACAiC,OACA,OACAuE,WAAA,EACAK,aAAA,EACAJ,UAAAQ,OAAAlD,OAAAmD,aAAAC,QAAA,kBACAT,iBAAA,EACAC,YAAA,IAGAnD,UACA,MAAAmC,EAAA5B,OAAAM,UAAAuB,UAAAC,cACAuB,EAAArD,OAAAM,UAAAC,WACA,IAAA+C,EAAA1B,EAAA2B,QAAA,mBAAA3B,EAAA2B,QAAA,cAAA3B,EAAA2B,QAAA,eACA,IAAA3B,EAAA2B,QAAA,eAAA3B,EAAA2B,QAAA,iBAAA3B,EAAA2B,QAAA,YACA,IAAA3B,EAAA2B,QAAA,aAAA3B,EAAA2B,QAAA,WACAD,GAAA,KAAA7I,OAAAC,MAAAoB,WAAAuH,IACA,KAAAX,UAAA,KAAAC,mBACA,KAAAF,WAAA,GAEAzC,OAAAmD,aAAAK,QAAA,kBAAAd,UAAA,IAIAY,GAAA,KAAA7I,OAAAC,MAAAoB,WAAAuH,GACA,KAAArI,MAAA0E,IAAA,wBAAA+D,WAGA,KAAAzI,MAAA0E,IAAA,yBACA,KAAAoD,aAAA,KAGApC,QAAA,CACAmC,YACA,KAAA9B,MAAAC,MAAA,eAAAC,YAAA,WACA,KAAAyB,UAAA,KAAAC,iBACA3C,OAAAmD,aAAAK,QAAA,kBAAAd,WACA,KAAAI,aAAA,GAEAW,UACA,gBAAAC,EAAA,cAAAC,GAAA,KAAA5B,MAAA,wBACA,IAAA4B,EAAA,OACA,MAAAC,EAAA,CAAAC,GAAA,MAAAC,GAAAJ,EAAAK,GAAAJ,GACA,KAAAK,SAAAC,OACAC,eAAAN,GACAxC,KAAA+C,IACA,kBAAAC,iBAAA,SACA,WAAAlG,EAAA,QAAAmG,GAAAF,EACA,OAAAE,IACA,KAAAzB,WAAA,IAAA1E,EAAAoG,QAEA,KAAA1B,YAAA,CACA,MAAA2B,EAAA7C,YAAA,KACA,eAAA0C,mBACAzC,cAAA4C,GACA,KAAA3B,YAAA,IAEA,QAIA4B,QAAA,SAAAR,SAAAS,UAGA/H,SAAA,CACAsG,WACA,YAAAjB,MAAA,4BAAAtH,OAAAC,MAAAS,SAAAuJ,MAAA,IAAAC,iBCvGwV,ICQpV,G,UAAY,eACd,EACA,EACA,GACA,EACA,KACA,WACA,OAIa,I,QCnBXlL,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,cAAc,CAACE,MAAM,CAAC,uBAAwBJ,EAAIK,WAAYG,MAAM,CAAC,GAAK,wBAAwB4C,YAAYpD,EAAIqD,GAAG,CAAC,CAACC,IAAI,QAAQC,GAAG,WAAW,MAAO,CAACrD,EAAG,MAAM,CAACQ,YAAY,cAAc,CAACR,EAAG,OAAO,CAACQ,YAAY,QAAQU,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOrB,EAAIsB,MAAMC,MAAM,2BAA2B,CAACvB,EAAIW,GAAG,IAAIX,EAAIY,GAAGZ,EAAIc,GAAG,gBAAgB,KAAKZ,EAAG,IAAI,CAACQ,YAAY,mBAAoBV,EAAIoC,SAAUlC,EAAG,MAAM,CAACQ,YAAY,sBAAsBU,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOrB,EAAIsB,MAAMC,MAAM,UAAW,yBAAyB,CAACrB,EAAG,KAAKF,EAAIW,GAAGX,EAAIY,GAAGZ,EAAIc,GAAG,uBAAuB,OAAOd,EAAImB,SAASwC,OAAM,MAAS,CAACzD,EAAG,MAAM,CAACQ,YAAY,wBAAwB,CAACV,EAAIkL,GAAIlL,EAAImL,aAAa,SAASC,EAAYC,GAAO,OAAOnL,EAAG,MAAM,CAACoD,IAAI8H,EAAYE,WAAWlL,MAAM,CAAC,eAAe,CAAC,uBAAuBgL,EAAYE,aAAetL,EAAI6E,cAAcyG,YAAc,CAAE,WAAYF,EAAYG,UAAWnK,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOrB,EAAIwL,aAAaH,MAAU,CAAuB,IAArBD,EAAYK,KAAY,CAACvL,EAAG,MAAM,CAACQ,YAAY,OAAO,CAACR,EAAG,MAAM,CAACQ,YAAY,YAAY,CAACV,EAAIW,GAAGX,EAAIY,GAAGZ,EAAI0L,gBAAgBC,cAAc,KAAKzL,EAAG,IAAI,CAACQ,YAAY,mBAAmBR,EAAG,aAAa,CAACM,MAAM,CAAC,KAAO,UAAU,CAAER,EAAI4L,UAAW1L,EAAG,MAAM,CAACQ,YAAY,SAAS,CAACR,EAAG,MAAM,CAACQ,YAAY,qBAAqB,CAACV,EAAIW,GAAG,IAAIX,EAAIY,GAAGZ,EAAI+B,IAAI8J,aAAe7L,EAAI0L,gBAAgBC,cAAc,KAAKzL,EAAG,MAAMF,EAAIW,GAAG,KAAKT,EAAG,SAASF,EAAImB,OAAOjB,EAAG,MAAM,CAACQ,YAAY,yBAAyB,CAACR,EAAG,OAAO,CAACQ,YAAY,aAAa,CAACV,EAAIW,GAAGX,EAAIY,GAAGZ,EAAI0L,gBAAgBI,SAAS5L,EAAG,KAAKF,EAAIW,GAAG,OAAOT,EAAG,QAAQ,CAACoC,WAAW,CAAC,CAACC,KAAK,QAAQC,QAAQ,UAAUC,MAAOzC,EAAI+L,UAAWrJ,WAAW,cAAclC,MAAM,CAAC,KAAO,SAAS,SAAW,IAAIyD,SAAS,CAAC,MAASjE,EAAI+L,WAAY3K,GAAG,CAAC,MAAQ,SAASC,GAAWA,EAAO2K,OAAOC,YAAiBjM,EAAI+L,UAAU1K,EAAO2K,OAAOvJ,WAAUvC,EAAG,MAAM,CAACQ,YAAY,OAAOU,GAAG,CAAC,MAAQ,SAASC,GAAQA,EAAOgI,kBAAkBrJ,EAAIkM,aAAc,KAAQ,CAAElM,EAAIkM,YAAahM,EAAG,MAAM,CAACQ,YAAY,yBAAyB,CAACV,EAAIW,GAAGX,EAAIY,GAAGZ,EAAIa,IAAI,8BAA8Bb,EAAImB,UAAU,GAAGjB,EAAG,MAAM,CAACQ,YAAY,UAAU,CAACR,EAAG,MAAM,CAACE,MAAM,CAAC,MAAM,CAAC,aAAcJ,EAAI4D,YAAY,CAAC5D,EAAIW,GAAGX,EAAIY,GAAGZ,EAAImM,aAAaf,GAAagB,WAAW3G,aAAczF,EAAImM,aAAaf,GAAagB,WAAWC,YAAanM,EAAG,MAAM,CAACE,MAAM,CAAC,SAAS,CAAC,aAAcJ,EAAI4D,YAAY,CAAC1D,EAAG,MAAM,CAACF,EAAIW,GAAGX,EAAIY,GAAGZ,EAAImM,aAAaf,GAAagB,WAAWC,kBAAkBrM,EAAImB,QAAQ,CAACjB,EAAG,MAAM,CAACQ,YAAY,OAAO,CAACR,EAAG,MAAM,CAACQ,YAAY,YAAY,CAACV,EAAIW,GAAG,IAAIX,EAAIY,GAAGwK,EAAYU,MAAM,KAAK5L,EAAG,IAAI,CAACQ,YAAY,mBAAmBR,EAAG,aAAa,CAACM,MAAM,CAAC,KAAO,UAAU,CAAER,EAAI4L,UAAW1L,EAAG,MAAM,CAACQ,YAAY,SAAS,CAACR,EAAG,MAAM,CAACQ,YAAY,qBAAqB,CAACV,EAAIW,GAAG,IAAIX,EAAIY,GAAGZ,EAAI+B,IAAI8J,aAAeT,EAAYU,MAAM,KAAK5L,EAAG,MAAMF,EAAIW,GAAG,KAAKT,EAAG,SAASF,EAAImB,OAAOjB,EAAG,MAAM,CAACE,MAAM,CAAC,QAAS,SAAWiL,KAAUD,EAAYG,QAASrL,EAAG,MAAM,CAACQ,YAAY,kBAAkBV,EAAImB,MAAM,GAAGjB,EAAG,MAAM,CAACQ,YAAY,UAAU,CAACR,EAAG,MAAM,CAACE,MAAM,CAAC,MAAM,CAAC,aAAcJ,EAAI4D,YAAY,CAAC5D,EAAIW,GAAGX,EAAIY,GAAGZ,EAAImM,aAAaf,GAAagB,WAAW3G,aAAczF,EAAImM,aAAaf,GAAagB,WAAWC,YAAanM,EAAG,MAAM,CAACE,MAAM,CAAC,SAAS,CAAC,aAAcJ,EAAI4D,YAAY,CAAC1D,EAAG,MAAM,CAACF,EAAIW,GAAGX,EAAIY,GAAGZ,EAAImM,aAAaf,GAAagB,WAAWC,kBAAkBrM,EAAImB,QAASnB,EAAIsM,QAAUtM,EAAIiC,SAAWjC,EAAIuM,iBAAiBnB,EAAYE,YAAapL,EAAG,MAAM,CAACE,MAAM,CAAC,QAAS,WAAW,CAACF,EAAG,IAAI,CAACQ,YAAY,YAAY,CAACR,EAAG,kBAAkB,CAACF,EAAIW,GAAG,UAAU,GAAGT,EAAG,IAAI,CAACQ,YAAY,OAAO,CAACV,EAAIW,GAAG,WAAWX,EAAImB,KAAMnB,EAAImM,aAAaf,GAAaoB,WAAWf,KAAMvL,EAAG,MAAM,CAACQ,YAAY,gBAAgB,CAAoD,WAAlDV,EAAImM,aAAaf,GAAaoB,WAAWf,KAAmB,CAACvL,EAAG,MAAM,CAACQ,YAAY,QAAQ,CAACR,EAAG,kBAAkB,CAACF,EAAIW,GAAGX,EAAIY,GAAGZ,EAAIc,GAAG,mBAAmB,GAAGZ,EAAG,MAAM,CAACQ,YAAY,OAAO,CAACV,EAAIW,GAAGX,EAAIY,GAAGZ,EAAImM,aAAaf,GAAaoB,WAAWV,OAAO5L,EAAG,IAAI,CAACQ,YAAY,oBAAoBV,EAAImB,KAAwD,WAAlDnB,EAAImM,aAAaf,GAAaoB,WAAWf,KAAmB,CAACvL,EAAG,MAAM,CAACQ,YAAY,YAAY,CAACV,EAAIW,GAAGX,EAAIY,GAAGZ,EAAImM,aAAaf,GAAaoB,WAAW5G,SAAS1F,EAAG,MAAM,CAACQ,YAAY,OAAO,CAACV,EAAIW,GAAG,UAAUX,EAAImB,MAAM,GAAGnB,EAAImB,KAAMiK,EAAYqB,sBAAwBrB,EAAYsB,gBAAiBxM,EAAG,MAAM,CAACQ,YAAY,oBAAoB,CAACV,EAAIW,GAAG,IAAIX,EAAIY,GAAGwK,EAAYsB,iBAAiB,IAAI1M,EAAIY,GAAGwK,EAAYqB,sBAAsB,OAAOzM,EAAImB,MAAM,MAAOnB,EAAImL,YAAYP,OAAgF5K,EAAImB,KAA5EjB,EAAG,MAAM,CAACQ,YAAY,SAAS,CAACV,EAAIW,GAAGX,EAAIY,GAAGZ,EAAIc,GAAG,oBAA6B,GAAiC,OAA7Bd,EAAIe,OAAOC,MAAMkB,SAAsC,QAAlBlC,EAAIK,UAAqBH,EAAG,iBAAiBF,EAAImB,MAAM,IAEnkJwB,EAAkB,G,4CCFlB5C,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACQ,YAAY,wBAAwBU,GAAG,CAAC,MAAQ,SAASC,GAAQA,EAAOgI,qBAAsB,CAACnJ,EAAG,MAAM,CAACQ,YAAY,kBAAkB,CAACR,EAAG,MAAM,CAACE,MAAM,CAAC,gBAAiB,CAAC,uBAAwBJ,EAAI2M,qBAAqBrB,aAAalK,GAAG,CAAC,MAAQ,SAASC,GAAQrB,EAAI4M,UAAY5M,EAAI4M,YAAY,CAAC5M,EAAI+C,GAAG,GAAG7C,EAAG,MAAM,CAACQ,YAAY,cAAc,CAAEV,EAAI2M,qBAAqBrB,WAAYpL,EAAG,MAAM,CAACQ,YAAY,kBAAkB,CAACR,EAAG,OAAO,CAACQ,YAAY,YAAY,CAACV,EAAIW,GAAGX,EAAIY,GAAGZ,EAAI2M,qBAAqBb,MAAM,KAAK5L,EAAG,OAAOA,EAAG,OAAO,CAACF,EAAIW,GAAGX,EAAIY,GAAGZ,EAAI2M,qBAAqBnH,OAAO,IAAIxF,EAAIY,GAAGZ,EAAI2M,qBAAqBE,sBAAsB3M,EAAG,MAAM,CAACQ,YAAY,QAAQ,CAACV,EAAIW,GAAGX,EAAIY,GAAGZ,EAAIc,GAAG,iBAAiBZ,EAAG,IAAI,CAACE,MAAM,CAAC,CAAC,gBAAiBJ,EAAI4M,iBAAiB1M,EAAG,aAAa,CAACM,MAAM,CAAC,KAAO,WAAW,CAAER,EAAI4M,SAAU1M,EAAG,MAAM,CAACQ,YAAY,eAAeV,EAAIkL,GAAIlL,EAAI8M,WAAW,SAAS1B,EAAYC,GAAO,OAAOnL,EAAG,MAAM,CAACoD,IAAI+H,EAAMjL,MAAM,CAAC,cAAe,CAAE,qBAAsBgL,EAAYE,aAAetL,EAAI2M,qBAAqBrB,aAAclK,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOrB,EAAIwL,aAAaH,MAAU,CAACnL,EAAG,OAAO,CAACQ,YAAY,YAAY,CAACV,EAAIW,GAAGX,EAAIY,GAAGwK,EAAYU,MAAM,KAAK5L,EAAG,OAAOA,EAAG,OAAO,CAACF,EAAIW,GAAGX,EAAIY,GAAGwK,EAAY5F,OAAO,IAAIxF,EAAIY,GAAGwK,EAAYyB,yBAAwB,GAAG7M,EAAImB,QAAQ,MAEt1CwB,EAAkB,CAAC,WAAY,IAAI3C,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACQ,YAAY,gBAAgB,CAACR,EAAG,MAAM,CAACM,MAAM,CAAC,IAAMsC,EAAQ,QAA4C,IAAM,UC6BhL,GACfP,KAAA,eACAiC,OACA,OACAoI,UAAA,EACAE,UAAA,KAGA9J,SAAA,IACAyB,eAAA,8BACAkI,uBACA,YAAA5L,OAAAc,QAAA,0BAAAgD,cAAA,KAGAmC,QAAA,CACAwE,aAAAH,GACA,KAAAtK,OAAAgM,OAAA,iCAAAD,UAAAzB,IACA,KAAAuB,UAAA,IAGA7G,UACA,KAAAzE,MAAA0E,IAAA,uBAAAvD,IACA,KAAAqK,UAAArK,IAEA,KAAAnB,MAAA0E,IAAA,iBACA,KAAA4G,UAAA,MCxDqV,ICQjV,G,UAAY,eACd,EACA,EACA,GACA,EACA,KACA,WACA,OAIa,K,QCoFf,MAAAI,GAAA1G,OAAA+B,MAAA,8BACe,QACf9F,KAAA,mBACA+B,WAAA,CAAA2I,gBAAA1I,kBAAA2I,sBACA1I,OACA,OACA2G,YAAA,GACAgC,YAAA,EACAC,gBAAA,GACArB,UAAAiB,GACAd,aAAA,IAGAlJ,SAAA,IACAyB,eAAA,sIACAA,eAAA,2CACAA,eAAA,2BACAA,eAAA,wBACAA,eAAA,iBACAC,eAAA,kJACAD,eAAA,oDACAA,eAAA,yBACAmH,YACA,YAAAyB,QAAA,KAAAC,iBAAA,KAAA3I,aAAAS,UAAA,KAAArD,IAAAsL,QAEA3B,kBACA,MAAA6B,qBAAA/H,EAAA,SAAAgI,EAAA,KAAA1B,EAAA2B,SAAAC,GAAA,KAAAN,gBAEAO,EAAAC,OAAAC,OAAA,QAAAT,gBAAA,CACAzB,aAAAG,EAAA,KAAAC,UACAA,WAAA,KAAAA,UACA+B,aAAAC,eAAAf,GAAAxH,EAAAgI,GACA/H,SAAAsI,eAAA,KAAAhC,UAAAvG,EAAAgI,GACA3H,iBAAAkI,eAAAvI,EAAA,KAAAuG,WAAA2B,GAAA,GAAAF,KAGA,OADAG,EAAA/I,SAAAmJ,eAAAJ,EAAA9H,iBAAA8H,EAAAlI,SAAA+H,GACAG,GAGAxB,eACA,MAAAtH,EAAA,KAAAA,cACAF,EAAA,KAAAA,aACAqJ,GAAAC,EAAA7B,EAAA8B,KACA,wDAAAjL,SAAA0B,EAAAU,SACA4I,EAAAxC,KAAA,SACAwC,EAAArI,KAAAjB,EAAAiB,KAEAwG,EAAAC,YAAA6B,EAAAX,qBACAnB,EAAA3G,SAAAyI,EAAAC,cACA,gBAAAxJ,EAAAU,QACA4I,EAAAxC,KAAA,SACAwC,EAAArI,KAAA,GAAAjB,EAAAgB,gBAAAd,EAAAgI,kBACAT,EAAAC,YAAA6B,EAAAX,qBACAnB,EAAA3G,SAAAyI,EAAAC,cACA,2EAAAlL,SAAA0B,EAAAU,QAAA6I,EAAA7I,UACA4I,EAAAxC,KAAA,SACAwC,EAAAnC,KAAAoC,EAAAtI,MAAAjB,EAAAiB,KAEAwG,EAAA3G,SAAAyI,EAAAX,uBAGA,OAAAnC,IACA,MAAA6C,EAAA,CACAxC,KAAA,GACA7F,KAAA,GACAkG,KAAA,GAEAM,EAAA,CAAA3G,SAAA,EAAA4G,YAAA,GAEA,QAAAE,iBAAAnB,EAAAE,YAAA,CACA,MAAA8C,EAAA,KAAA7B,iBAAAnB,EAAAE,YACA0C,EAAAC,EAAA7B,EAAAgC,QACA,GAAAhD,EAAAE,aAAAzG,EAAAyG,YAAA3G,EAAA0J,YAAAjD,EAAAE,WACA0C,EAAAC,EAAA7B,EAAAzH,QACA,QAAA2J,kBAAAlD,EAAAE,YAAA,CACA,MAAAgD,EAAA,KAAAA,kBAAAlD,EAAAE,YACA0C,EAAAC,EAAA7B,EAAAkC,QACA,QAAAC,kBAAAnD,GAEA6C,EAAAxC,KAAA,SACAwC,EAAArI,KAAA,KAAA7E,OAAAC,MAAAwN,SAAA1I,oBAAAJ,oBAAA,IAEA0G,EAAAC,YAAA,KAAAkC,kBAAAnD,GAAAmC,qBACAnB,EAAA3G,SAAA,KAAA8I,kBAAAnD,GAAA+C,kBACA,QAAAM,mBAAArD,GAAA,CAKA,GAHA6C,EAAAxC,KAAA,SACAwC,EAAAnC,KAAA,KAAA4C,cAAAtD,GAEA,YAAA/K,UAAA,CACA,MAAAsO,EAAA,KAAA5N,OAAAC,MAAAwN,SAAAI,4BAAAxD,EAAAE,YAAAuD,WACAC,EAAA,KAAAJ,cAAAtD,GAAAuD,EACAV,EAAAnC,KAAAiD,KAAAC,MAAA,IAAAF,GAAA,IAGA1C,EAAA3G,SAAA,KAAAgJ,mBAAArD,GAAA+C,kBAGA/B,EAAA3G,SAAA2F,EAAAmC,qBAGA,IAAAnC,EAAAK,OAAAW,EAAA3G,SAAA,KAAAiG,gBAAAjG,UAIA,MAAAwJ,EAAA,KAAA5G,MAAA,2BACA,QAAA6G,4BAAA9D,EAAAE,aAAA2D,EAAA,CACA,MAAAE,MACA,mBAAAxK,aAAAU,SACA,KAAAR,cAAAyG,aAAA2D,IAAA,KAAAtK,aAAAS,UACA,KAAAP,cAAAyG,aAAA2D,IAEA,KAAAG,gBAAAD,MACAlB,EAAAnC,KAAAV,EAAAU,KACAmC,EAAAxC,KAAA,UAaA,OATAW,EAAA3G,WAAA2G,EAAA3G,UAAA,IAAA2F,EAAAyB,iBACAT,EAAAC,cAAAD,EAAAC,aAAA,IAAAjB,EAAAyB,iBAOA,KAAAP,QAAA2B,EAAAxC,UAAA1E,GACA,CACAyF,WAAAyB,EACA7B,iBAKApF,QAAA,CACAqI,kBACA,KAAA/E,SAAAC,OACA+E,eAAA,CAAAC,WAAA,iBACA7H,KAAA+C,IACA,WAAAjG,EAAA,KAAAmG,GAAAF,EACA,OAAAE,EAAA,CACA,KAAAQ,YAAA3G,EAAAgL,KAAA,CAAAC,EAAAC,IAAAD,EAAA3D,KAAA4D,EAAA5D,MACA,KAAAX,YAAA,KAAAA,YAAAwE,IAAA,CAAA1J,EAAAoF,KACA,MAAAkC,qBAAA/H,EAAA,SAAAgI,EAAAC,SAAAC,GAAAzH,EAKA,OAJAA,EAAAJ,iBAAAkI,eAAAvI,GAAAkI,GAAA,GAAAF,GAEAvH,EAAAoF,QACApF,EAAAsF,SAAAtF,EAAAwG,sBAAAxG,EAAAyG,kBAAAzG,EAAAwG,uBAAAxG,EAAAyG,gBACAzG,IAEA,KAAA2J,iBAAA,KAAAzE,aACA,UAAA7H,EAAAb,KAAAmL,OAAAiC,QAAArL,GACA,GAAA/B,EAAA6I,aAAA,KAAAwE,OAAAC,MAAAC,GAAA,YAAAjP,OAAAgM,OAAA,4BAAAvI,EAAAlB,IAEA,GAAAkB,KAAAoG,OAAA,CACA,MAAAS,EAAA,KAAAF,YAAA8E,UAAAhK,MAAAsF,SACA,KAAAxK,OAAAgM,OAAA,4BAAAvI,EAAA6G,GAAA,IAGA,cAAAhL,WAAA,KAAA6P,wBAAA,KAAA/E,YAAA,SAEA,KAAAgF,OAAAC,IAAA,KAAAtP,GAAA,wBAGAgK,QAAA,SAAAR,SAAAS,SAEAS,aAAAH,GACA,QAAA8B,YAAA9B,IAAA,KAAAF,YAAAP,OAAA,GACA,QAAAc,gBAAAC,eAAA,KAAA9G,cAAA8G,aAAA,YACA,KAAArK,MAAAC,MAAA,2BAAA8O,QAAA,KAAA3E,gBAAA4E,GAAA,KAAAC,aAAAC,KAAA,YACA,CACA,MAAAxE,EAAA,KAAAb,YAAAE,GACA,GAAAW,EAAAT,QAAA,YAAA4E,OAAAC,IAAA,KAAAtP,GAAA,qBAEA,QAAAqK,YAAAE,GAAAC,aAAA,KAAAzG,cAAAyG,WAAA,YACA,KAAAvK,OAAAgM,OAAA,iCAAA5B,YAAAE,IAGA,KAAAtK,OAAAgM,OAAA,0BAEA6C,iBAAAzE,EAAA,IACA,IAAAE,GAAA,EACA,UAAA/H,EAAAb,KAAAmL,OAAAiC,QAAA1E,GACA,OAAA1I,EAAAgJ,KAAA,CACAJ,EAAA/H,EACA,MAIA,QAAA+H,EAAA,CACA,MAAAoF,EAAAtF,EAAAuF,OAAArF,EAAA,MACAF,EAAAwF,KAAAF,GAEA,KAAArD,gBAAAqD,EACA,KAAAtD,YAAA,IAGAoD,aAAAxE,GACAA,IACA,KAAAA,aAEA,KAAAhL,OAAAgM,OAAA,iCAAArB,kBAIAkF,mBACA,SAAAxG,EAAA,GAAAC,GAAA,KAAAhC,MAAA,qCACA6B,EAAA,CAAAC,GAAA,MAAAC,KAAAC,MACAwG,eAAA3G,GACAxC,KAAA+C,IACA,WAAAE,EAAA,KAAAnG,GAAAiG,EACA,IAAAE,GACA,KAAA5J,OAAAgM,OAAA,uBAAAvI,EAAAsM,eAIA,KAAAC,kCAGAA,iCACA,MAAA7G,EAAA,CAAAC,GAAA,MAAAC,GAAA,GAAAC,GAAA,MAEAH,EAAAG,GAAA,KACAwG,eAAA3G,GACAxC,KAAA+C,IACA,WAAAE,GAAAF,EACA,IAAAE,GACAqG,WAAA,KACA,KAAA1P,MAAAC,MAAA,mBACA,QAIA0P,oBACA,KAAA3P,MAAA0E,IAAA,oBACA,QAAAoJ,eAAA,YACA,WAAAvK,cAAAiH,MAAA,KAAAqE,OAAAC,IAAA,KAAAtP,GAAA,sCAGA,KAAAQ,MAAA0E,IAAA,uBACA,QAAAoJ,eAAA,YACA,MAAAvK,EAAA,KAAAA,cACAF,EAAA,KAAAA,aAEA,MAAAE,EAAAiH,MAAA,kCAAA7I,SAAA0B,EAAAU,UACAwC,QAAAC,IAAA,iBACA,KAAA/G,OAAAgM,OAAA,gCAMAmD,wBAAAG,GACA,MAAAa,EAAAzO,IAAA,OAAAA,IAAA0O,QAAA,GACAjH,EAAA,GACAA,EAAA1E,MAAA6K,EAAA7K,MACA0E,EAAAoB,WAAA+E,EAAA/E,WAEA,KAAAhB,SAAAC,OACA6G,eAAAlH,GACAxC,KAAA+C,IACA,WAAAE,EAAA,KAAAnG,GAAAiG,EACA,OAAAE,EAAA,CACA,IAAA0G,EAAA7M,EAAA8M,gBAAA,GACAD,IAAA1B,IAAA,CAAA1J,EAAAoF,KAAA,IACApF,EACAZ,OAAA,wBACAD,SAAA,yBAAAiG,EACA3F,oBAAAwL,EAAAjL,EAAA6I,aAEAuC,EAAAzG,QAAA,KAAA7J,OAAAgM,OAAA,oCAAAsE,EAAA,IAEA,IAAAE,EAAA/M,EAAAgN,cAAA,GACAD,IAAA5B,IAAA,CAAA1J,EAAAoF,KAAA,IACApF,EACAZ,OAAA,eACAK,oBAAAwL,EAAAjL,EAAA6I,UACAlJ,KAAAsL,EAAAjL,EAAA6I,UAAA,IACA1J,SAAA,gBAAAiG,EACAgD,UAAAnE,EAAAoB,cAEAiG,EAAA3G,QAAA,KAAA7J,OAAAgM,OAAA,oCAAAwE,EAAA,OAGAzG,QAAA,SAAAR,SAAAS,UAGAhF,UACA,KAAAsJ,kBACA,KAAAH,4BAAA,KAAA+B,oBAEA,KAAA3P,MAAA0E,IAAA,WAAAhF,IACA,IAAAA,IAEA,KAAAqO,kBACA,KAAAH,4BAAA,KAAA0B,sBAGA,KAAAtP,MAAA0E,IAAA,iBACA,KAAAkG,aAAA,MClZyV,MCQrV,I,UAAY,eACd,GACA,EACA,GACA,EACA,KACA,WACA,OAIa,M,QCnBXnM,GAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,cAAc,CAACQ,YAAY,iBAAiB,CAACR,EAAG,MAAM,CAACQ,YAAY,mBAAmBU,GAAG,CAAC,MAAQpB,EAAIyR,KAAK,CAAEzR,EAAIe,OAAOC,MAAMwN,SAASkD,qBAAsBxR,EAAG,MAAM,CAACQ,YAAY,+BAA+BR,EAAG,MAAM,CAACQ,YAAY,QAAQN,MAAM,CAACuR,OAAQ3R,EAAI2R,UAAU3R,EAAIW,GAAG,IAAIX,EAAIY,GAAGZ,EAAIc,GAAG,oBAAoB,KAAKZ,EAAG,UAEpYyC,GAAkB,GCctB,MAAAqH,UAAA,GAAAC,cAAA,eAAA2H,GAAA,eAAAC,GAAA,aAAAC,IAAAxL,OAAA+B,MAAA,wBAEe,QACf9F,KAAA,iBACA+B,WAAA,CAAAC,mBACAC,OACA,OACAuN,SAAA,EACAC,MAAA,IAGAhP,SAAA,IACAyB,eAAA,wBACAkN,SACA,YAAA1P,UAAA,KAAA8P,UAAA,KAAAC,OAGAhL,QAAA,CACAiL,YACA,MAAA/H,EAAA,CACAC,GAAA,MACAC,GAAAJ,GACAK,GAAA,GAAAwH,MAAAC,MAEA,KAAAxH,SAAAC,OACAC,eAAAN,GACAxC,KAAA+C,IACA,WAAAjG,EAAA,QAAAmG,GAAAF,EACA,IAAAE,IACA,IAAAnG,EAAAoG,SAAA,KAAAoH,MAAA,GAGA,aAAAjR,OAAAC,MAAAS,SAAA0D,WACA,KAAA6M,KAAA,KAAAA,MAAA,KAAAjR,OAAAC,MAAAwN,SAAA0D,mBAIApH,QAAA,SAAAR,SAAAS,SAGA0G,KACA,KAAAM,SAAA,EACA,KAAAzQ,MAAAC,MAAA,sBAIA4Q,aAAAC,GACA,MAAAC,EAAA,CAAAlI,GAAA,MAAAC,GAAAJ,IACAsI,eAAA,CAAAjI,GAAA+H,KAAAC,IACA3K,KAAA+C,IACA,WAAAE,GAAAF,EACA,IAAAE,GAAA,KAAA4H,UAAAH,MAGAG,UAAAH,GACA,MAAAlI,EAAA,CAAAG,IAAA+H,EAAA,GACAC,EAAA,CAAAlI,GAAA,MAAAC,GAAAJ,IACA,KAAAM,SAAAC,OACAsG,eAAA,IAAA3G,KAAAmI,IACAvH,QAAA,SAAAR,SAAAS,SAGAyH,cAEA,KAAAxK,wBAAA,KACA,MAAAyK,EAAAhM,SAAAiM,cAAA,+BACAC,EAAAlM,SAAAiM,cAAA,wBACAD,GACAA,EAAAG,MAAAC,KAAA,IACAF,EAAAC,MAAAE,WAAA,UAEAH,EAAAC,MAAAE,WAAA,cAEA,OAGA/M,UACA,KAAAzE,MAAA0E,IAAA,WAAAhF,IAEA,eAAA+R,OAAA,KAAAZ,aAAAlI,KAEA3D,OAAAjF,OAAA,KAAAC,MACA,KAAAA,MAAA0E,IAAA,6BAAAmM,aAAAlI,MAEA,KAAAkI,aAAAP,IAEA,KAAAK,cAGA,KAAAlR,OAAAC,MAAAoB,UAAA,KAAAoQ,eAEAQ,gBACA,KAAAhL,aAAAC,cAAA,KAAAD,eC5GuV,MCQnV,I,UAAY,eACd,GACA,GACA,IACA,EACA,KACA,KACA,OAIa,M,qBCnBXjI,GAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,cAAc,CAACQ,YAAY,yBAAyBN,MAAM,CAACJ,EAAIK,WAAWG,MAAM,CAAC,UAAYR,EAAIc,GAAG,cAAc,GAAK,2BAA2B,CAAEd,EAAImD,WAAYjD,EAAG,MAAM,CAACQ,YAAY,SAASN,MAAM,CAACJ,EAAIK,YAAY,CAACH,EAAG,MAAM,CAACQ,YAAY,iBAAiB,CAACR,EAAG,OAAO,CAACQ,YAAY,aAAa,CAACV,EAAIW,GAAGX,EAAIY,GAAGZ,EAAIc,GAAG,kBAAkB,SAASZ,EAAG,OAAO,CAACQ,YAAY,YAAYN,MAAM,CAAC,aAAcJ,EAAI4D,WAAW,CAAC5D,EAAIW,GAAGX,EAAIY,GAAGZ,EAAI6D,gBAAgBC,gBAAiB9D,EAAI6D,gBAAgBE,eAAgB7D,EAAG,OAAO,CAACE,MAAM,CAAC,eAAgB,CAAC,aAAcJ,EAAI4D,YAAY,CAAC5D,EAAIW,GAAGX,EAAIY,GAAGZ,EAAI6D,gBAAgBE,mBAAmB/D,EAAImB,KAAMnB,EAAI6D,gBAAgBG,aAAc9D,EAAG,MAAM,CAACQ,YAAY,iBAAiBuD,SAAS,CAAC,UAAYjE,EAAIY,GAAGZ,EAAI6D,gBAAgBG,iBAAiBhE,EAAImB,OAAQnB,EAAIwD,QAAStD,EAAG,MAAM,CAACQ,YAAY,eAAe,CAACV,EAAIW,GAAGX,EAAIY,GAAGZ,EAAIc,GAAG,YAAY,MAAMd,EAAIY,GAAGZ,EAAIwD,SAAS,IAAIxD,EAAIY,GAAGZ,EAAIkE,iBAAiBlE,EAAImB,KAAMnB,EAAIyD,UAAWvD,EAAG,MAAM,CAACQ,YAAY,eAAe,CAACV,EAAIW,GAAGX,EAAIY,GAAGZ,EAAIc,GAAG,cAAc,MAAMd,EAAIY,GAAGZ,EAAIyD,WAAW,IAAIzD,EAAIY,GAAGZ,EAAIkE,iBAAiBlE,EAAImB,KAAKjB,EAAG,MAAM,CAACQ,YAAY,eAAe,CAACV,EAAIW,GAAGX,EAAIY,GAAGZ,EAAI6D,gBAAgBM,kBAAkBjE,EAAG,OAAO,CAACQ,YAAY,OAAON,MAAM,CAACsD,OAAQ1D,EAAImD,YAAY/B,GAAG,CAAC,MAAQ,SAASC,GAAQrB,EAAImD,YAAcnD,EAAImD,cAAc,CAACnD,EAAIW,GAAG,KAAKX,EAAIY,GAAGZ,EAAIc,GAAG,aAAaZ,EAAG,SAASA,EAAG,MAAM,CAACQ,YAAY,SAASN,MAAM,CAACJ,EAAIK,YAAY,CAACH,EAAG,MAAM,CAACQ,YAAY,iBAAiB,CAACR,EAAG,OAAO,CAACQ,YAAY,YAAYN,MAAM,CAAC,aAAcJ,EAAI4D,WAAW,CAAC5D,EAAIW,GAAGX,EAAIY,GAAGZ,EAAI6D,gBAAgBM,eAAiB,QAASnE,EAAI6D,gBAAgBQ,iBAAkBnE,EAAG,OAAO,CAACQ,YAAY,eAAeN,MAAM,CAAC,aAAcJ,EAAI4D,WAAW,CAAC5D,EAAIW,GAAGX,EAAIY,GAAGZ,EAAI6D,gBAAgBQ,qBAAqBrE,EAAImB,KAAMnB,EAAI6D,gBAAgBG,aAAc9D,EAAG,MAAM,CAACQ,YAAY,iBAAiBuD,SAAS,CAAC,UAAYjE,EAAIY,GAAGZ,EAAI6D,gBAAgBG,iBAAiBhE,EAAImB,OAAQnB,EAAIoE,WAAYlE,EAAG,OAAO,CAACQ,YAAY,OAAON,MAAM,CAACsD,OAAQ1D,EAAImD,YAAY/B,GAAG,CAAC,MAAQ,SAASC,GAAQrB,EAAImD,YAAcnD,EAAImD,cAAc,CAACnD,EAAIW,GAAG,KAAKX,EAAIY,GAAGZ,EAAIc,GAAG,aAAaZ,EAAG,OAAOF,EAAImB,OAAOjB,EAAG,MAAM,CAACQ,YAAY,YAAYN,MAAM,CAAC,CAACuB,QAAS3B,EAAI4B,gBAAkB5B,EAAIe,OAAOc,QAAQ,gCAAgCT,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOrB,EAAIuB,MAAM,oBAAoB,CAACvB,EAAIW,GAAG,IAAIX,EAAIY,GAAGZ,EAAIc,GAAG,aAAa,KAAMd,EAAI+B,IAAIC,UAAW9B,EAAG,KAAKF,EAAImB,UAEn5EwB,GAAkB,GCCP,IACfJ,KAAA,qBACA0Q,MAAA,mBACAhO,OAAA,CAAAC,ICN2V,MCQvV,I,UAAY,eACd,GACA,GACA,IACA,EACA,KACA,WACA,OAIa,M,qBCiFA,IACf3C,KAAA,MACA+B,WAAA,CACA4O,sBACA3O,kBACA4O,iBAAA,2HACAC,kBACAC,oBACAC,eACAC,kBACAC,sBACAC,kBACAC,oBACAC,qBACAC,mBACAC,eACAC,oBACAC,mBAAA,2HACAC,uBAAA,2HACAC,sBAAA,2HACAC,2BAEAjP,OAAA,CAAAkP,SACAnN,QAAA,CACAoN,iBACA,KAAA9S,MAAA0E,IAAA,4BACA,MAAAqO,EAAA,KAAAC,aAAA,mBAGAC,EAAA,IAAAC,MACAD,EAAAE,IAAAJ,EAAA,GAAAK,SACA,KAAApK,SAAAC,OAEAgK,EAAAI,OAAA,KACA,KAAArT,MAAAC,MAAA,2BACA,KAAA+I,SAAAS,QAEAwJ,EAAAK,QAAA,KACA,KAAAtK,SAAAS,WAIA8J,2BACA,QAAAtU,gBAAA,YACA+R,eAAA,CACAnI,GAAA,MACAC,GAAA,GACAC,GAAA,KACAyK,GAAA,QAEApN,KAAA+C,IACA,WAAAE,EAAA,KAAAnG,GAAAiG,EACA,IAAAE,GACA,KAAA5J,OAAAgM,OAAA,kCAAAvI,EAAAuQ,eAAA,OAKAhP,UACA,KAAAuG,OAAA,KAAAuI,2BACA,KAAAT,mBChK4U,MCQxU,I,UAAY,eACd,GACArU,EACA4C,GACA,EACA,KACA,WACA,OAIa,gB,oECnBf,W,qBCAA9C,EAAOC,QAAU,IAA0B,kD,kCCA3C,W,yDCAA,W,kCCAA,W,yDCAA,W,gFCAA,W,kCCAA", "file": "js/pagePay.3cf1a321.js", "sourcesContent": ["module.exports = \"data:image/png;base64,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\"", "export * from \"-!../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--9-oneOf-1-0!../../node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--9-oneOf-1-2!../../node_modules/sass-loader/dist/cjs.js??ref--9-oneOf-1-3!../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./CheckoutCounterCN.vue?vue&type=style&index=0&id=5a809613&prod&scoped=true&lang=scss\"", "export * from \"-!../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--9-oneOf-1-0!../../node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--9-oneOf-1-2!../../node_modules/sass-loader/dist/cjs.js??ref--9-oneOf-1-3!../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./AddScreenBtn.vue?vue&type=style&index=0&id=d8c45582&prod&scoped=true&lang=scss\"", "export * from \"-!../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--9-oneOf-1-0!../../node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--9-oneOf-1-2!../../node_modules/sass-loader/dist/cjs.js??ref--9-oneOf-1-3!../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./EntranceOfBoon.vue?vue&type=style&index=0&id=5f9ee2ec&prod&lang=scss\"", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{class:['shopping-wrapper', _vm.$gameName, { sdk: _vm.IS_CHECKOUT_SDK }],attrs:{\"id\":\"page-pay-wrapper\"}},[(_vm.isPc && !_vm.IS_CHECKOUT_SDK)?[_c('section',{staticClass:\"pc-content-wrapper\"},[_c('add-screen-btn'),_c('add-ios-safari-btn'),_c('div',{staticClass:\"page-title\"},[_vm._v(_vm._s(_vm.$vt('pageTitle'))+\" \"),_c('span',[_vm._v(_vm._s(_vm.$t('mobile_available'))+\" \"),_c('i')])]),_c('div',{staticClass:\"content-center content-center__main\"},[_c('div',{staticClass:\"left-part\"},[_c('div',{staticClass:\"logo\"}),_c('div',{staticClass:\"name\"}),_c('p',{staticClass:\"description\"},[_c('span',[_vm._v(_vm._s(_vm.$vt('whatIsDiamondTitle')))]),(_vm.$store.state.functionSwitch.showPcDiscountTips)?_c('span',[_vm._v(_vm._s(_vm.$vt('discount95Tips')))]):_vm._e()]),_c('div',{staticClass:\"charge-construction\",on:{\"click\":function($event){return _vm.$root.$emit('showPop', 'ChargeConstruction')}}},[_c('i'),_vm._v(_vm._s(_vm.$t('construction_title'))+\" \")])]),_c('div',{staticClass:\"right-part\"},[_c('login-module'),(_vm.boon)?_c('entrance-of-boon'):_vm._e(),_c('coupon-choose'),_c('diamond-choose-k-o-a'),_c('channel-choose'),(_vm.$store.state.gameinfo.isCn)?_c('checkout-counter-cn'):_c('checkout-counter'),_c('div',{staticClass:\"shop-btn\"},[_c('span',{staticClass:\"click-btn\",class:[{disable: _vm.requestLoading || _vm.$store.getters['riskPolicy/forbiddenAccess']}],on:{\"click\":function($event){return _vm.judgeRisk()}}},[_vm._v(_vm._s(_vm.$t('shop_now'))+\" \"),(_vm.vip.isNewUser || !_vm.isLogin)?_c('i'):_vm._e()])]),(_vm.$store.state.country === 'DE' && _vm.isLogin)?_c('common-part',[_c('private-permission')],1):_vm._e()],1)])],1),(_vm.$store.state.gameinfo.isCn)?_c('common-footer-cn'):_vm._e(),(_vm.$store.state.gameinfo.mainBody)?_c('CommonFooterPuzala'):_c('common-footer')]:_vm._e(),(_vm.isMobile && !_vm.IS_CHECKOUT_SDK)?[_c('div',{staticClass:\"mobile-body-wrapper\"},[_c('add-screen-btn'),_c('add-ios-safari-btn'),_c('login-module'),(_vm.boon)?_c('entrance-of-boon'):_vm._e(),_c('coupon-choose'),_c('diamond-choose-k-o-a'),_c('channel-choose'),(_vm.$store.state.country === 'DE' && _vm.isLogin)?_c('common-part',[_c('private-permission')],1):(_vm.showMobilePolicy)?_c('refund-policy'):_vm._e(),(_vm.$store.state.gameinfo.isCn)?_c('common-footer-cn'):_vm._e()],1),_c('checkout-footer',{attrs:{\"request-loading\":_vm.requestLoading},on:{\"purchaseGoods\":function($event){return _vm.judgeRisk()}}})]:_vm._e(),(_vm.IS_CHECKOUT_SDK)?[_c('div',{staticClass:\"sdk-body-wrapper\"},[_c('direct-gift-package'),_c('coupon-choose'),_c('channel-choose'),(_vm.$store.state.country === 'DE' && _vm.isLogin)?_c('common-part',[_c('private-permission')],1):_vm._e(),_c('checkout-counter-s-d-k',{attrs:{\"request-loading\":_vm.requestLoading},on:{\"purchaseGoods\":function($event){return _vm.judgeRisk()}}}),_c('login-module',{directives:[{name:\"show\",rawName:\"v-show\",value:(false),expression:\"false\"}]})],1),_c('checkout-footer',{attrs:{\"request-loading\":_vm.requestLoading},on:{\"purchaseGoods\":function($event){return _vm.judgeRisk()}}}),_c('common-footer')]:_vm._e()],2)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "var render = function render(){var _vm=this,_c=_vm._self._c;return (_vm.showFooter)?_c('div',{staticClass:\"copyright\"},[_c('img',{staticStyle:{\"vertical-align\":\"text-bottom\",\"padding-right\":\"10px\"},attrs:{\"src\":require(\"../assets/common/icon/fp-logo.png\"),\"alt\":\"funplus\"}}),_vm._m(0)]):_vm._e()\n}\nvar staticRenderFns = [function (){var _vm=this,_c=_vm._self._c;return _c('p',[_vm._v(\" © FUNPLUS INTERNATIONAL AG(Bahnhofstrasse 2, 6300 Zug) - ALL RIGHTS RESERVED \"),_c('a',{staticStyle:{\"color\":\"#ddb463\"},attrs:{\"href\":\"https://funplus.com/privacy-policy/\",\"target\":\"_blank\"}},[_vm._v(\"Privacy Policy\")]),_vm._v(\" , \"),_c('a',{staticStyle:{\"color\":\"#ddb463\"},attrs:{\"href\":\"https://funplus.com/terms-conditions/\",\"target\":\"_blank\"}},[_vm._v(\"Terms and Conditions\")]),_vm._v(\" and \"),_c('a',{staticStyle:{\"color\":\"#ddb463\"},attrs:{\"href\":\"https://funplus.com/terms-conditions/#section-13\",\"target\":\"_blank\"}},[_vm._v(\"Refund Policy\")]),_vm._v(\". \")])\n}]\n\nexport { render, staticRenderFns }", "<template>\n  <div v-if=\"showFooter\" class=\"copyright\">\n    <img src=\"../assets/common/icon/fp-logo.png\" alt=\"funplus\" style=\"vertical-align: text-bottom; padding-right: 10px;\">\n    <p>\n      © FUNPLUS INTERNATIONAL AG(Bahnhofstrasse 2, 6300 Zug) - ALL RIGHTS RESERVED\n      <a href=\"https://funplus.com/privacy-policy/\" target=\"_blank\" style=\"color:#ddb463\">Privacy Policy</a>\n      ,\n      <a href=\"https://funplus.com/terms-conditions/\" target=\"_blank\" style=\"color:#ddb463\">Terms and Conditions</a>\n      and\n      <a href=\"https://funplus.com/terms-conditions/#section-13\" target=\"_blank\" style=\"color:#ddb463\">Refund Policy</a>.\n    </p>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'CommonFooter',\n  computed: {\n    showFooter () {\n      return !['aof', 'rom'].includes(this.$gameName)\n    }\n  }\n}\n</script>\n\n<style scoped lang=\"scss\">\n.copyright{\n  height: 40PX;\n  font-size: 13PX;\n  line-height: 40PX;\n  text-align: center;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: #c5c5c5;;\n  background-color: black;\n\n  img{\n    height: 20PX;\n    position: relative;\n    top: -2PX;\n  }\n}\n</style>\n", "import mod from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./CommonFooter.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./CommonFooter.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./CommonFooter.vue?vue&type=template&id=ae65575c&scoped=true\"\nimport script from \"./CommonFooter.vue?vue&type=script&lang=js\"\nexport * from \"./CommonFooter.vue?vue&type=script&lang=js\"\nimport style0 from \"./CommonFooter.vue?vue&type=style&index=0&id=ae65575c&prod&scoped=true&lang=scss\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"ae65575c\",\n  null\n  \n)\n\nexport default component.exports", "var render = function render(){var _vm=this,_c=_vm._self._c;return (_vm.expandMode)?_c('common-part',{class:['expand', _vm.$gameName],attrs:{\"id\":\"checkout-counter-expand\"},scopedSlots:_vm._u([{key:\"label\",fn:function(){return [_c('div',{staticClass:\"sub-total\"},[_vm._v(_vm._s(_vm.$t('tax-sub-total')))]),(_vm.taxCost)?_c('div',{staticClass:\"tax\"},[_vm._v(_vm._s(_vm.$t('tax-txt')))]):_vm._e(),(_vm.extraCost)?_c('div',{staticClass:\"tax\"},[_vm._v(_vm._s(_vm.$t('extra-txt')))]):_vm._e(),_c('div',{staticClass:\"total\"},[_vm._v(\" \"+_vm._s(_vm.$t('totalPrice')))]),_c('span',{staticClass:\"rate\",class:{active: _vm.expandMode},on:{\"click\":function($event){_vm.expandMode = !_vm.expandMode}}},[_vm._v(\"+ \"+_vm._s(_vm.$t('tax-txt'))),_c('i')])]},proxy:true}],null,false,2468513191)},[_c('div',{staticClass:\"price-wrapper\"},[_c('span',{staticClass:\"now-price\",class:{'is-ar-zone': _vm.isArZone}},[_vm._v(_vm._s(_vm.FinalPriceState.rawNowPrice))]),(_vm.FinalPriceState.rawOriginPrice)?_c('span',{class:['origin-price', {'is-ar-zone': _vm.isArZone}]},[_vm._v(_vm._s(_vm.FinalPriceState.rawOriginPrice))]):_vm._e(),(_vm.FinalPriceState.offCountTips)?_c('div',{staticClass:\"off-count-tips\",domProps:{\"innerHTML\":_vm._s(_vm.FinalPriceState.offCountTips)}}):_vm._e()]),(_vm.taxCost)?_c('div',{staticClass:\"tax-wrapper\"},[_vm._v(_vm._s(_vm.taxCost)+\" \"+_vm._s(_vm.currencyUnit))]):_vm._e(),(_vm.extraCost)?_c('div',{staticClass:\"tax-wrapper\"},[_vm._v(_vm._s(_vm.extraCost)+\" \"+_vm._s(_vm.currencyUnit))]):_vm._e(),_c('div',{staticClass:\"final-price\"},[_vm._v(_vm._s(_vm.FinalPriceState.finalNowPrice))])]):_c('common-part',{class:['normal', _vm.$gameName],attrs:{\"id\":\"checkout-counter-normal\"},scopedSlots:_vm._u([{key:\"label\",fn:function(){return [_c('div',{staticClass:\"total\"},[_vm._v(_vm._s(_vm.$t('totalPrice')))]),(_vm.showTaxBtn)?_c('span',{staticClass:\"rate\",class:{active: _vm.expandMode},on:{\"click\":function($event){_vm.expandMode = !_vm.expandMode}}},[_vm._v(\"+ \"+_vm._s(_vm.$t('tax-txt'))),_c('i')]):_vm._e()]},proxy:true}])},[_c('div',{staticClass:\"total-price\",attrs:{\"id\":\"total-price\"}},[_c('span',{staticClass:\"now-price\",class:{'is-ar-zone': _vm.isArZone}},[_vm._v(_vm._s(_vm.FinalPriceState.finalNowPrice))]),(_vm.FinalPriceState.finalOriginPrice)?_c('span',{staticClass:\"origin-price\",class:{'is-ar-zone': _vm.isArZone}},[_vm._v(_vm._s(_vm.FinalPriceState.finalOriginPrice))]):_vm._e(),(_vm.FinalPriceState.offCountTips)?_c('div',{staticClass:\"off-count-tips\",domProps:{\"innerHTML\":_vm._s(_vm.FinalPriceState.offCountTips)}}):_vm._e()])])\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "import CommonPart from '@/components/common/CommonPart.vue'\nimport { mapGetters, mapState } from 'vuex'\n\nexport default {\n  name: 'CheckoutCounterTax',\n  components: { CommonPart },\n  data () {\n    return {\n      expandMode: false\n    }\n  },\n  computed: {\n    ...mapState(['urlParams', 'isArZone', 'currencyUnit', 'IS_CHECKOUT_SDK']),\n    ...mapState('formdata', ['chosenChannel', 'chosenDiamond', 'chosenCoupon', 'vip']),\n    ...mapState('gameinfo', ['defaultDiscount']),\n    ...mapState('userinfo', ['isLogin']),\n    ...mapGetters('formdata', ['FinalPriceState', 'getRebateCoin', 'getSDKRebateCoin']),\n    taxCost () {\n      return this.chosenCoupon.taxation || this.FinalPriceState.taxation || this.chosenDiamond.taxation\n    },\n    extraCost () {\n      return this.chosenCoupon.extra_fee_amount || this.FinalPriceState.extra_fee_amount || this.chosenDiamond.extra_fee_amount\n    },\n    showTaxBtn () {\n      return this.taxCost || this.extraCost\n    }\n  },\n  watch: {\n    showTaxBtn (newValue) {\n      if (!newValue) this.expandMode = false\n    }\n  }\n}\n", "<template>\n  <common-part v-if=\"expandMode\" :class=\"['expand', $gameName]\" id=\"checkout-counter-expand\">\n    <template #label>\n      <div class=\"sub-total\">{{ $t('tax-sub-total') }}</div>\n      <div class=\"tax\" v-if=\"taxCost\">{{ $t('tax-txt') }}</div>\n      <div class=\"tax\" v-if=\"extraCost\">{{ $t('extra-txt') }}</div>\n      <div class=\"total\"> {{ $t('totalPrice') }}</div>\n      <span class=\"rate\" @click=\"expandMode = !expandMode\" :class=\"{active: expandMode}\">+ {{ $t('tax-txt') }}<i></i></span>\n    </template>\n    <!--sub total-->\n    <div class=\"price-wrapper\">\n      <span class=\"now-price\" :class=\"{'is-ar-zone': isArZone}\">{{ FinalPriceState.rawNowPrice }}</span>\n      <span v-if=\"FinalPriceState.rawOriginPrice\" :class=\"['origin-price', {'is-ar-zone': isArZone}]\">{{ FinalPriceState.rawOriginPrice }}</span>\n      <div v-if=\"FinalPriceState.offCountTips\" class=\"off-count-tips\" v-html=\"FinalPriceState.offCountTips\"></div>\n    </div>\n    <!--Tax-->\n    <div class=\"tax-wrapper\" v-if=\"taxCost\">{{ taxCost }} {{ currencyUnit }}</div>\n    <!--额外费用-->\n    <div class=\"tax-wrapper\" v-if=\"extraCost\">{{ extraCost }} {{ currencyUnit }}</div>\n    <!--最终价格-->\n    <div class=\"final-price\">{{ FinalPriceState.finalNowPrice }}</div>\n  </common-part>\n  <common-part v-else :class=\"['normal', $gameName]\" id=\"checkout-counter-normal\">\n    <template #label>\n      <div class=\"total\">{{ $t('totalPrice') }}</div>\n      <span class=\"rate\" v-if=\"showTaxBtn\" @click=\"expandMode = !expandMode\" :class=\"{active: expandMode}\">+ {{ $t('tax-txt') }}<i></i></span>\n    </template>\n    <div class=\"total-price\" id=\"total-price\">\n      <span class=\"now-price\" :class=\"{'is-ar-zone': isArZone}\">{{ FinalPriceState.finalNowPrice }}</span>\n      <span v-if=\"FinalPriceState.finalOriginPrice\" class=\"origin-price\" :class=\"{'is-ar-zone': isArZone}\">{{ FinalPriceState.finalOriginPrice }}</span>\n      <div v-if=\"FinalPriceState.offCountTips\" class=\"off-count-tips\" v-html=\"FinalPriceState.offCountTips\"></div>\n    </div>\n  </common-part>\n</template>\n\n<script>\nimport checkoutCounter from '@/components/CheckoutCounter.js'\n\nexport default {\n  name: 'CheckoutCounterTax',\n  mixins: [checkoutCounter]\n}\n</script>\n\n<style scoped lang=\"scss\">\n@use \"~@/utils/utils.scss\" as utils;\n.normal{\n  color:#000;\n  ::v-deep{\n    .label{\n      position: relative;\n      .total{\n        font-size: 22PX;\n        font-family: PingFangSC-Semibold, PingFang SC;\n        font-weight: 600;\n        color: #FFFFFF;\n        line-height: 30PX;\n      }\n      .rate{\n        font-size: 16PX;\n        font-family: PingFangSC-Regular, PingFang SC;\n        font-weight: 400;\n        color: #FEB522;\n        line-height: 22PX;\n        cursor: pointer;\n        i{\n          @include utils.bgCenter('common/icon/tax-arrow.png', 14PX, 14PX);\n          display: inline-block;\n          margin-left: 2PX;\n          transition: all .3s;\n        }\n\n        &.active i{\n          transform: rotate(180deg);\n        }\n      }\n    }\n  }\n\n  .total-price {\n    font-weight: bold;\n    color: #FFFFFF;\n    line-height: 30PX;\n\n    .is-ar-zone {\n      display: inline-block;\n    }\n\n    .now-price{\n      font-size: 26PX;\n    }\n\n    .origin-price {\n      text-decoration: line-through;\n      font-weight: bold;\n      color: #7F7F7F;\n      font-size: 16PX;\n      margin-left: 10PX;\n    }\n\n    .off-count-tips {\n      display: inline-block;\n      background: #FEB522;\n      position: relative;\n      font-weight: bold;\n      color: #735100;\n      font-size: 10PX;\n      top: -19PX;\n      left: 5PX;\n      padding: 0 6PX;\n      border-radius: 4PX;\n      height: 18PX;\n      line-height: 18PX;\n\n      &:after,&:before{\n        content: ' ';\n        height: 2PX;\n        width: 2PX;\n        background-color: black;\n        left: 30%;\n        position: absolute;\n        z-index: 1;\n        transform: translateX(-50%);\n      }\n\n      &:after{\n        top: 0;\n        border-bottom-left-radius: 50%;\n        border-bottom-right-radius: 50%;\n      }\n\n      &:before{\n        bottom: 0;\n        border-top-left-radius: 50%;\n        border-top-right-radius: 50%;\n      }\n\n      ::v-deep{\n        .diamond-icon {\n          @include utils.bgCenterForKoaIcon('koa/diamond/diamond.png', 11px, 9px);\n          display: inline-block;\n          margin-left: 1px;\n          position: relative;\n        }\n      }\n    }\n  }\n}\n.expand{\n  @extend .normal;\n\n  .sub-total{\n    font-size: 18PX;\n    font-family: PingFangSC-Regular, PingFang SC;\n    font-weight: 400;\n    color: #FFFFFF;\n    line-height: 25PX;\n    white-space: nowrap;\n    overflow: hidden;\n    text-overflow: ellipsis;\n  }\n\n  .tax{\n    font-size: 18PX;\n    font-family: PingFangSC-Regular, PingFang SC;\n    font-weight: 400;\n    color: #FFFFFF;\n    line-height: 25PX;\n    margin-top: 2PX;\n    white-space: nowrap;\n    overflow: hidden;\n    text-overflow: ellipsis;\n  }\n\n  .total{\n    margin-top: 8px;\n    line-height: 42PX!important;\n  }\n\n  .price-wrapper{\n    display: flex;\n    align-items: flex-end;\n    height: 25PX;\n    position: relative;\n\n    .now-price{\n      font-size: 18PX;\n      font-family: PingFangSC-Regular, PingFang SC;\n      font-weight: 400;\n      color: #FFFFFF;\n      line-height: 25PX;\n    }\n\n    .origin-price{\n      font-size: 16PX;\n      font-family: PingFangSC-Regular, PingFang SC;\n      font-weight: 400;\n      color: #7F7F7F;\n      line-height: 22PX;\n      margin-left: 20PX;\n      text-decoration: line-through;\n    }\n\n    .off-count-tips{\n      line-height: 16PX;\n      background: #FEB522;\n      border-radius: 3PX;\n      padding: 0 5PX;\n      font-size: 10PX;\n      font-family: PingFangSC-Semibold, PingFang SC;\n      font-weight: 600;\n      color: #735100;\n      position: relative;\n      align-self: self-start;\n      top: -14PX;\n      left: -24PX;\n\n      ::v-deep{\n        .diamond-icon {\n          @include utils.bgCenterForKoaIcon('koa/diamond/diamond.png', 11px, 9px);\n          display: inline-block;\n          margin-left: 1px;\n          position: relative;\n        }\n      }\n    }\n  }\n\n  .tax-wrapper{\n    margin-top: 2PX;\n    font-size: 18PX;\n    font-family: PingFangSC-Regular, PingFang SC;\n    font-weight: 400;\n    color: #FFFFFF;\n    line-height: 25PX;\n  }\n\n  .final-price{\n    font-size: 30PX;\n    font-family: PingFangSC-Semibold, PingFang SC;\n    font-weight: 600;\n    color: #FFFFFF;\n    line-height: 42PX;\n    margin-top: 8PX;\n  }\n}\n\n/* dc */\n.normal.dc{\n  .off-count-tips{\n    background: #FFE14D;\n    color: #393A3E;\n    ::v-deep{\n      .diamond-icon {\n        @include utils.bgCenterForDC('diamond/diamond-icon.png', 13px, 13px);\n        position: relative;\n        top: 2px;\n        margin-left: 0;\n      }\n    }\n  }\n}\n.expand.dc{\n  .price-wrapper{\n    .off-count-tips{\n      background: #FFE14D;\n      color: #393A3E;\n      ::v-deep{\n        .diamond-icon {\n          @include utils.bgCenterForDC('diamond/diamond-icon.png', 13px, 13px);\n          position: relative;\n          top: 2px;\n          margin-left: 0;\n        }\n      }\n    }\n  }\n}\n\n/* ssv */\n.normal.ssv{\n  ::v-deep{\n    .label{\n      .rate{\n        color: #FF5E0F;\n        i{\n          @include utils.bgCenterForSSV('checkout/tax-arrow.png', 14PX, 14PX);\n          display: inline-block;\n          margin-left: 2PX;\n          transition: all .3s;\n        }\n\n        &.active i{\n          transform: rotate(180deg);\n        }\n      }\n    }\n  }\n  .total-price {\n    color: #FF5E0F;\n  }\n  .origin-price{\n    color: #F5895A;\n  }\n  .off-count-tips {\n    background: rgb(255,93,6);\n    color: #FFFFFF;\n  }\n}\n.expand.ssv{\n  .price-wrapper{\n    .origin-price{\n      color: #7F7F7F;\n    }\n  }\n  .final-price{\n    color: #FF5E0F;\n  }\n}\n\n.normal.ssv2{\n  ::v-deep{\n    .label{\n      .rate{\n        color: #FF5E0F;\n        i{\n          @include utils.bgCenterForSSV('checkout/tax-arrow.png', 14PX, 14PX);\n          display: inline-block;\n          margin-left: 2PX;\n          transition: all .3s;\n        }\n\n        &.active i{\n          transform: rotate(180deg);\n        }\n      }\n    }\n  }\n  .total-price {\n    color: #FF5E0F;\n  }\n  .origin-price{\n    color: #F5895A;\n  }\n  .off-count-tips {\n    background: rgb(255,93,6);\n    color: #FFFFFF;\n  }\n}\n.expand.ssv2{\n  .price-wrapper{\n    .origin-price{\n      color: #7F7F7F;\n    }\n  }\n  .final-price{\n    color: #FF5E0F;\n  }\n}\n</style>\n", "import mod from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./CheckoutCounterTax.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./CheckoutCounterTax.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./CheckoutCounterTax.vue?vue&type=template&id=d3472866&scoped=true\"\nimport script from \"./CheckoutCounterTax.vue?vue&type=script&lang=js\"\nexport * from \"./CheckoutCounterTax.vue?vue&type=script&lang=js\"\nimport style0 from \"./CheckoutCounterTax.vue?vue&type=style&index=0&id=d3472866&prod&scoped=true&lang=scss\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"d3472866\",\n  null\n  \n)\n\nexport default component.exports", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('common-part',{class:_vm.$store.state.gameinfo.gameCode,staticStyle:{\"color\":\"#000\"},attrs:{\"label-font\":\"合计\"}},[_c('div',{staticClass:\"total-price\",attrs:{\"id\":\"total-price\"}},[(_vm.chosenCoupon.FE_INDEX)?[_c('span',{staticClass:\"now-price\",class:{'is-ar-zone': _vm.isArZone}},[(_vm.chosenCoupon.feType==='first_pay')?[_vm._v(_vm._s(_vm.currencyUnit)+_vm._s(_vm._f(\"formatPrice\")(_vm.chosenCoupon.discount_price)))]:_vm._e(),(_vm.chosenCoupon.feType==='discount_coupon')?[_vm._v(_vm._s(_vm.currencyUnit)+_vm._s(_vm._f(\"formatPrice\")(_vm.chosenCoupon.discount_price)))]:_vm._e(),(_vm.chosenCoupon.feType==='cash_coupon')?[_vm._v(_vm._s(_vm.currencyUnit)+_vm._s(_vm._f(\"formatPrice\")(_vm.chosenCoupon.price)))]:_vm._e(),(_vm.chosenCoupon.feType==='rebate_coupon')?[_vm._v(_vm._s(_vm.currencyUnit)+_vm._s(_vm._f(\"formatPrice\")(_vm.chosenCoupon.price)))]:_vm._e()],2),(_vm.chosenCoupon.feType!=='rebate_coupon')?_c('span',{class:['origin-price', {'is-ar-zone': _vm.isArZone}]},[_vm._v(_vm._s(_vm.currencyUnit)+_vm._s(_vm._f(\"formatPrice\")(_vm.chosenDiamond.nowPrice || _vm.chosenDiamond.price)))]):_vm._e(),_c('div',{staticClass:\"off-count-tips\"},[(_vm.chosenCoupon.feType==='first_pay')?[_vm._v(_vm._s(`首充 ${_vm.chosenCoupon.rateWidthOutPercent} 折`))]:_vm._e(),(_vm.chosenCoupon.feType==='discount_coupon')?[_vm._v(_vm._s(_vm.chosenCoupon.rateWidthOutPercent)+\"折 优惠券\")]:_vm._e(),(_vm.chosenCoupon.feType==='cash_coupon')?[_c('span',{class:{'is-ar-zone': _vm.isArZone}},[_vm._v(\"减 \"+_vm._s(_vm.currencyUnit)+_vm._s(_vm.chosenCoupon.deduct_price))])]:_vm._e(),(_vm.chosenCoupon.feType==='rebate_coupon')?[_c('span',{class:{'is-ar-zone': _vm.isArZone}},[_vm._v(\"送 \"+_vm._s(_vm.chosenCoupon.rate)+\" \"),_c('i',{staticClass:\"diamond-icon\"})])]:_vm._e()],2)]:_c('span',{class:{'is-ar-zone': _vm.isArZone}},[_vm._v(_vm._s(_vm.currencyUnit)+_vm._s(_vm._f(\"formatPrice\")(_vm.chosenDiamond.nowPriceWidthTax || _vm.chosenDiamond.price)))])],2)])\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\n  <common-part :class=\"$store.state.gameinfo.gameCode\" label-font=\"合计\" style=\"color:#000;\">\n    <div class=\"total-price\" id=\"total-price\">\n      <!-- 有券的情况 -->\n      <template v-if=\"chosenCoupon.FE_INDEX\">\n        <span class=\"now-price\" :class=\"{'is-ar-zone': isArZone}\">\n           <template v-if=\"chosenCoupon.feType==='first_pay'\">{{ currencyUnit }}{{ chosenCoupon.discount_price | formatPrice }}</template>\n           <template v-if=\"chosenCoupon.feType==='discount_coupon'\">{{ currencyUnit }}{{ chosenCoupon.discount_price | formatPrice }}</template>\n           <template v-if=\"chosenCoupon.feType==='cash_coupon'\">{{ currencyUnit }}{{ chosenCoupon.price | formatPrice }}</template>\n           <template v-if=\"chosenCoupon.feType==='rebate_coupon'\">{{ currencyUnit }}{{ chosenCoupon.price | formatPrice }}</template>\n        </span>\n        <span v-if=\"chosenCoupon.feType!=='rebate_coupon'\" :class=\"['origin-price', {'is-ar-zone': isArZone}]\">{{ currencyUnit }}{{ chosenDiamond.nowPrice || chosenDiamond.price | formatPrice }}</span>\n        <div class=\"off-count-tips\">\n          <template v-if=\"chosenCoupon.feType==='first_pay'\">{{ `首充 ${chosenCoupon.rateWidthOutPercent} 折` }}</template>\n          <template v-if=\"chosenCoupon.feType==='discount_coupon'\">{{ chosenCoupon.rateWidthOutPercent }}折 优惠券</template>\n          <template v-if=\"chosenCoupon.feType==='cash_coupon'\"><span :class=\"{'is-ar-zone': isArZone}\">减 {{ currencyUnit }}{{ chosenCoupon.deduct_price }}</span></template>\n          <template v-if=\"chosenCoupon.feType==='rebate_coupon'\"><span :class=\"{'is-ar-zone': isArZone}\">送 {{ chosenCoupon.rate }} <i class=\"diamond-icon\"></i></span></template>\n        </div>\n      </template>\n      <!-- 其他游戏无任何活动 -->\n      <span v-else :class=\"{'is-ar-zone': isArZone}\">{{ currencyUnit }}{{ chosenDiamond.nowPriceWidthTax || chosenDiamond.price | formatPrice }}</span>\n    </div>\n  </common-part>\n</template>\n\n<script>\nimport CommonPart from '@/components/common/CommonPart.vue'\nimport { mapState } from 'vuex'\nexport default {\n  name: 'CheckoutCounter',\n  components: { CommonPart },\n  data () {\n    return {\n      defaultDiscountInfo: {}\n    }\n  },\n  computed: {\n    ...mapState(['urlParams', 'isArZone', 'currencyUnit']),\n    ...mapState('formdata', ['chosenChannel', 'chosenDiamond', 'chosenCoupon']),\n    ...mapState('gameinfo', ['defaultDiscount'])\n  },\n  created () {\n    this.$root.$on('setDefaultDiscountInfo', item => {\n      this.defaultDiscountInfo = item\n    })\n  }\n}\n</script>\n\n<style scoped lang=\"scss\">\n@use \"~@/utils/utils.scss\" as utils;\n.total-price {\n  font-weight: bold;\n  color: #FF5E0F;\n\n  .is-ar-zone {\n    display: inline-block;\n  }\n\n  .now-price{\n    font-size: 26px;\n  }\n\n  .origin-price {\n    text-decoration: line-through;\n    font-weight: bold;\n    color: #F5895A;\n    font-size: 16PX;\n    margin-left: 10PX;\n  }\n\n  .off-count-tips {\n    display: inline-block;\n    background: rgb(255,93,6);\n    position: relative;\n    font-weight: bold;\n    color: #FFFFFF;\n    font-size: 10PX;\n    top: -19PX;\n    left: 5PX;\n    padding: 0 6PX;\n    border-radius: 4PX;\n    height: 18PX;\n    line-height: 18PX;\n\n    &:after,&:before{\n      content: ' ';\n      height: 2PX;\n      width: 2PX;\n      background-color: black;\n      left: 30%;\n      position: absolute;\n      z-index: 1;\n      transform: translateX(-50%);\n    }\n\n    &:after{\n      top: 0;\n      border-bottom-left-radius: 50%;\n      border-bottom-right-radius: 50%;\n    }\n\n    &:before{\n      bottom: 0;\n      border-top-left-radius: 50%;\n      border-top-right-radius: 50%;\n    }\n\n    .diamond-icon {\n      @include utils.bgCenter('koa/diamond/diamond.png', 11px, 9px);\n      display: inline-block;\n      margin-left: 1px;\n      position: relative;\n    }\n  }\n}\n\n.KOA{\n  .total-price{\n    color: #FFFFFF;\n    font-size: 26px;\n    font-family: PingFangSC-Semibold, PingFang SC;\n    font-weight: 600;\n    line-height: 34px;\n  }\n\n  .origin-price{\n    font-size: 16PX;\n    color: #C9C9C9;\n  }\n\n  .off-count-tips{\n    background: rgb(228,184,86);\n    color: #735100;\n  }\n}\n</style>\n", "import mod from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./CheckoutCounterCN.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./CheckoutCounterCN.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./CheckoutCounterCN.vue?vue&type=template&id=5a809613&scoped=true\"\nimport script from \"./CheckoutCounterCN.vue?vue&type=script&lang=js\"\nexport * from \"./CheckoutCounterCN.vue?vue&type=script&lang=js\"\nimport style0 from \"./CheckoutCounterCN.vue?vue&type=style&index=0&id=5a809613&prod&scoped=true&lang=scss\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"5a809613\",\n  null\n  \n)\n\nexport default component.exports", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{class:['add-screen-pc-wrapper', [_vm.$gameName]]},[_c('transition',{attrs:{\"name\":\"addScreen\"}},[(_vm.deferredPrompt&&_vm.$store.state.isMobile)?_c('div',{staticClass:\"add-to-main-screen__mobile\",on:{\"click\":_vm.goInstall}},[_vm._v(_vm._s(_vm.$t('add-to-screen')))]):_vm._e()]),(_vm.deferredPrompt&&_vm.$store.state.isPc)?_c('div',{staticClass:\"add-to-main-screen__pc\"},[_vm._v(\" \"+_vm._s(_vm.$t('add_screen_des1'))+\" \"+_vm._s(_vm.$t('add_screen_des2'))+\" \"),_c('span',{staticClass:\"click-btn\",on:{\"click\":_vm.goInstall}},[_vm._v(_vm._s(_vm.$t('add-to-screen')))])]):_vm._e()],1)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\n  <div :class=\"['add-screen-pc-wrapper', [$gameName]]\">\n    <transition name=\"addScreen\">\n      <div v-if=\"deferredPrompt&&$store.state.isMobile\" @click=\"goInstall\" class=\"add-to-main-screen__mobile\">{{  $t('add-to-screen')  }}</div>\n    </transition>\n\n    <div v-if=\"deferredPrompt&&$store.state.isPc\" class=\"add-to-main-screen__pc\">\n      {{ $t('add_screen_des1') }}\n      {{ $t('add_screen_des2') }}\n      <span @click=\"goInstall\" class=\"click-btn\">{{  $t('add-to-screen')  }}</span>\n    </div>\n  </div>\n</template>\n\n<script>\nfunction displayMode () {\n  const isStandalone = window.matchMedia('(display-mode: standalone)').matches\n  if (document.referrer.startsWith('android-app://')) {\n    return 'twa'\n  } else if (navigator.standalone || isStandalone) {\n    return 'standalone'\n  }\n  return 'browser'\n}\nexport default {\n  name: 'AddScreenBtn',\n  data () {\n    return {\n      deferredPrompt: window.__deferredPrompt || undefined\n    }\n  },\n  methods: {\n    showBtn () {\n      window.addEventListener('beforeinstallprompt', (e) => {\n        // 防止 Chrome 67 及更早版本自动显示安装提示\n        e.preventDefault()\n        // 稍后再触发此事件\n        this.deferredPrompt = e\n      })\n    },\n    goInstall () {\n      this.$gtag.event('click_chrome_install', { event_label: this.$store.state.isMobile ? 'mobile' : 'pc' })\n      this.deferredPrompt.prompt()\n      // 等待用户反馈\n      this.deferredPrompt.userChoice\n        .then((choiceResult) => {\n          if (choiceResult.outcome === 'accepted') {\n            console.log('User accepted the A2HS prompt')\n            const displayModeCheck = setInterval(() => {\n              if (displayMode() === 'standalone') {\n                clearInterval(displayModeCheck)\n                this.$root.$emit('installSuccessful')\n              }\n            }, 1000)\n          } else {\n            console.log('User dismissed the A2HS prompt')\n          }\n          this.deferredPrompt = undefined\n        })\n    }\n  },\n  created () {\n    const ua = (navigator.userAgent || '').toLowerCase()\n    ua.includes('chrome') && this.showBtn()\n\n    if (this.$gcbk('switch.enableAnimation', false) && this.$store.state.isPc) {\n      const unwatch = this.$watch('deferredPrompt', (val) => {\n        if (val) {\n          this.$nextTick(() => gsap && gsap.from('.add-to-main-screen__pc', { height: 0, duration: .4, clearProps: 'height' }))\n          unwatch()\n        }\n      })\n    }\n  }\n}\n</script>\n\n<style scoped lang=\"scss\">\n@use \"~@/utils/utils.scss\" as utils;\n@include utils.setMobileContent{\n  .addScreen-enter{\n    transform: translateY(-100%);\n    opacity: 0;\n  }\n  .addScreen-leave-to{\n    transform: translateY(100%);\n    opacity: 0;\n  }\n  .addScreen-enter-active,\n  .addScreen-leave-active{\n    transition: all .3s;\n  }\n\n  .add-to-main-screen__mobile{\n    height: 50px;\n    top: 10px;\n    right: 15px;\n    border-radius: 35px;\n    font-size: 24px;\n    line-height: 50px;\n    padding: 0 30px;\n    position: fixed;\n    background-color: rgba(255, 255, 255, .7);\n    cursor: pointer;\n    z-index: 100;\n  }\n}\n@include utils.setPcContent{\n  .add-to-main-screen__pc{\n    text-align: center;\n    height: 60PX;\n    line-height: 60PX;\n    background-color: black;\n    margin-top: -18PX;\n    font-size: 16PX;\n    font-family: PingFangSC-Light, PingFang SC;\n    font-weight: 300;\n    color: #B0B0B0;\n\n    span{\n      background: #FF5E0F;\n      border-radius: 18PX;\n      line-height: 36PX;\n      height: 36PX;\n      font-size: 16PX;\n      margin: 0 30PX;\n      padding: 0 25PX;\n      color: #FFFFFF;\n      display: inline-block;\n      cursor: pointer;\n    }\n  }\n}\n\n.dc{\n  .add-to-main-screen__pc{\n    display: flex;\n    align-items: center;\n    justify-content: center;\n\n    span{\n      @extend .dc-btn-decoration;\n      @extend .dc-stroke;\n      border-radius: 0;\n      @include utils.flexCenter;\n    }\n  }\n}\n</style>\n", "import mod from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./AddScreenBtn.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./AddScreenBtn.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./AddScreenBtn.vue?vue&type=template&id=d8c45582&scoped=true\"\nimport script from \"./AddScreenBtn.vue?vue&type=script&lang=js\"\nexport * from \"./AddScreenBtn.vue?vue&type=script&lang=js\"\nimport style0 from \"./AddScreenBtn.vue?vue&type=style&index=0&id=d8c45582&prod&scoped=true&lang=scss\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"d8c45582\",\n  null\n  \n)\n\nexport default component.exports", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{class:['add-ios-wrapper', _vm.$gameName],attrs:{\"id\":\"add-ios-wrapper\"}},[_c('transition',{attrs:{\"name\":\"addScreen\"}},[((_vm.isShowBtn && _vm.showTimes < _vm.defaultShowTimes) || !this.hadInstall)?_c('div',{staticClass:\"add-to-main-screen__mobile heart\",on:{\"click\":_vm.showGuide}},[_vm._v(_vm._s(_vm.$t('add-to-screen')))]):_vm._e()]),_c('transition',{attrs:{\"name\":\"iosGuide\"}},[_c('div',{directives:[{name:\"show\",rawName:\"v-show\",value:(_vm.isShowGuide),expression:\"isShowGuide\"}],staticClass:\"ios-guide\",class:{ active: _vm.isShowGuide },on:{\"click\":function($event){_vm.isShowGuide = false}}},[_c('div',{staticClass:\"guide-wrap\",on:{\"click\":function($event){$event.stopPropagation();}}},[_c('div',{staticClass:\"close\",on:{\"click\":function($event){_vm.isShowGuide = false}}}),_c('div',{staticClass:\"title\"},[_vm._v(_vm._s(_vm.$t('ios-guide-0')))]),_c('div',{staticClass:\"subtitle\"},[_vm._v(_vm._s(_vm.$t('ios-guide-2'))+\" \"+_vm._s(_vm.$t('ios-guide-3')))]),_c('div',{staticClass:\"logo\"}),_c('div',{staticClass:\"game\"},[_vm._v(_vm._s(_vm.gameName)+\" TopupCenter\")]),_c('div',{staticClass:\"phone\"}),_c('i18n',{staticClass:\"txt\",attrs:{\"path\":\"ios-guide-4\",\"tag\":\"div\"}},[_c('div',{staticClass:\"up\",attrs:{\"slot\":0},slot:0})]),_c('i18n',{staticClass:\"txt\",attrs:{\"path\":\"ios-guide-5\",\"tag\":\"div\"}},[_c('div',{staticClass:\"add\",attrs:{\"slot\":0},slot:0}),_c('template',{slot:1},[_vm._v(_vm._s(_vm.$t('ios-guide-1')))])],2),_c('div',{staticClass:\"mune\"},[_c('div',[_vm._v(_vm._s(_vm.$t('ios-guide-1')))])]),_c('div',{staticClass:\"arr\"})],1)])])],1)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\n  <div :class=\"['add-ios-wrapper', $gameName]\" id=\"add-ios-wrapper\">\n    <transition name=\"addScreen\">\n      <div v-if=\"(isShowBtn && showTimes < defaultShowTimes) || !this.hadInstall\" @click=\"showGuide\" class=\"add-to-main-screen__mobile heart\">{{  $t('add-to-screen')  }}</div>\n    </transition>\n\n    <transition name=\"iosGuide\">\n      <div v-show=\"isShowGuide\" class=\"ios-guide\" :class=\"{ active: isShowGuide }\" @click=\"isShowGuide = false\">\n        <div class=\"guide-wrap\" @click.stop>\n          <div class=\"close\" @click=\"isShowGuide = false\"></div>\n          <div class=\"title\">{{ $t('ios-guide-0') }}</div>\n          <div class=\"subtitle\">{{ $t('ios-guide-2') }} {{ $t('ios-guide-3') }}</div>\n          <div class=\"logo\"></div>\n          <div class=\"game\">{{ gameName }} TopupCenter</div>\n          <div class=\"phone\"></div>\n          <i18n path=\"ios-guide-4\" tag=\"div\" class=\"txt\">\n            <div :slot=\"0\" class=\"up\"></div>\n          </i18n>\n          <i18n path=\"ios-guide-5\" tag=\"div\" class=\"txt\">\n            <div :slot=\"0\" class=\"add\"></div>\n            <template :slot=\"1\">{{ $t('ios-guide-1') }}</template>\n          </i18n>\n          <div class=\"mune\">\n            <div>{{ $t('ios-guide-1') }}</div>\n          </div>\n          <div class=\"arr\"></div>\n        </div>\n      </div>\n    </transition>\n  </div>\n</template>\n\n<script>\nimport { ameHoldByGet } from '@/server'\nimport { calcDisplayMode } from '@/utils/utils'\n\nexport default {\n  name: 'AddIosSafariBtn',\n  data () {\n    return {\n      isShowBtn: false,\n      isShowGuide: false,\n      showTimes: Number(window.localStorage.getItem('_i_g_times') || 0),\n      defaultShowTimes: 3,\n      hadInstall: true\n    }\n  },\n  created () {\n    const ua = window.navigator.userAgent.toLowerCase()\n    const isQuickApp = window.navigator.standalone\n    var issafariBrowser = (ua.indexOf('applewebkit') > -1 && ua.indexOf('mobile') > -1 && ua.indexOf('safari') > -1 &&\n      ua.indexOf('linux') === -1 && ua.indexOf('android') === -1 && ua.indexOf('chrome') === -1 &&\n      ua.indexOf('ios') === -1 && ua.indexOf('browser') === -1)\n    if (issafariBrowser && this.$store.state.isMobile && !isQuickApp) {\n      if (this.showTimes < this.defaultShowTimes) {\n        this.isShowBtn = true\n      }\n      window.localStorage.setItem('_i_g_times', this.showTimes + 1)\n    }\n\n    // 重新判断pwa安装\n    if (issafariBrowser && this.$store.state.isMobile && !isQuickApp) {\n      this.$root.$on('loginSuccess', () => this.rejudge())\n    }\n\n    this.$root.$on('mobileSafariGuide', () => {\n      this.isShowGuide = true\n    })\n  },\n  methods: {\n    showGuide () {\n      this.$gtag.event('click_guide', { event_label: 'safari' })\n      this.showTimes = this.defaultShowTimes\n      window.localStorage.setItem('_i_g_times', this.showTimes)\n      this.isShowGuide = true\n    },\n    rejudge () {\n      const { projectId, pwaOpenAction } = this.$gcbk('apiParams.boonAme', {})\n      if (!pwaOpenAction) return\n      const params = { p0: 'web', p1: projectId, p2: pwaOpenAction }\n      this.$loading.show()\n      ameHoldByGet(params)\n        .then(res => {\n          if (calcDisplayMode() === 'standalone') return false\n          const { data = [], code } = res\n          if (code === 0) {\n            this.hadInstall = data.length === 1\n\n            if (!this.hadInstall) {\n              const cancel = setInterval(() => {\n                if (calcDisplayMode() === 'standalone') {\n                  clearInterval(cancel)\n                  this.hadInstall = true\n                }\n              }, 2000)\n            }\n          }\n        })\n        .finally(() => this.$loading.hide())\n    }\n  },\n  computed: {\n    gameName(){\n      return this.$gcbk('gameinfo.sortName') || (this.$store.state.gameinfo.game || '').toUpperCase()\n    }\n  }\n}\n</script>\n\n<style scoped lang=\"scss\">\n@use \"~@/utils/utils.scss\" as utils;\n@keyframes breathe {\n  0%{ transform: scale(.98); }\n  50%{ transform: scale(1.05); }\n  100%{ transform: scale(.98); }\n}\n.add-ios-wrapper{\n  @include utils.setMobileContent{\n    .addScreen-enter{\n      transform: translateY(-100%);\n      opacity: 0;\n    }\n    .addScreen-leave-to{\n      transform: translateY(100%);\n      opacity: 0;\n    }\n    .addScreen-enter-active,\n    .addScreen-leave-active{\n      transition: all .3s;\n    }\n\n    .add-to-main-screen__mobile{\n      height: 50px;\n      top: 10px;\n      right: 15px;\n      border-radius: 35px;\n      font-size: 24px;\n      line-height: 50px;\n      padding: 0 30px;\n      position: fixed;\n      background-color: rgba(255, 255, 255, .7);\n      cursor: pointer;\n      z-index: 100;\n    }\n  }\n  .ios-guide {\n    position: fixed;\n    left: 0;\n    top: 0;\n    right: 0;\n    bottom: 0;\n    z-index: 10000;\n    background: rgba(0, 0, 0, 0.5);\n    transition: all .3s linear;\n    text-align: center;\n    &.iosGuide-enter{\n      .guide-wrap {\n        transform: translateY(100%);\n      }\n    }\n    &.iosGuide-leave-to{\n      .guide-wrap {\n        transform: translateY(100%);\n      }\n    }\n    .guide-wrap {\n      border-top-left-radius: 20px;\n      border-top-right-radius: 20px;\n      background-color: rgba(30, 30, 42, 1);\n      position: absolute;\n      left: 0;\n      bottom: 0;\n      width: 100%;\n      display: flex;\n      flex-direction: column;\n      align-items: center;\n      justify-content: center;\n      padding: 40px 10px 20px;\n      transition: all .3s linear;\n    }\n    .close {\n      width: 28px;\n      height: 28px;\n      position: absolute;\n      right: 30px;\n      top: 20px;\n      background-image: url(\"~@/assets/common/pwa/icon_guide_close.png\");\n      background-size: 100% 100%;\n      background-position: center center;\n      background-repeat: no-repeat;\n    }\n    .title {\n      font-size: 40px;\n      padding: 0 40px;\n      font-weight: 600;\n      color: #FFFFFF;\n      line-height: 56px;\n    }\n    .subtitle {\n      font-size: 28px;\n      font-weight: 400;\n      color: #A5A5A5;\n      line-height: 40px;\n      margin-top: 5px;\n    }\n    .logo {\n      background-size: 100% 100%;\n      background-position: center center;\n      background-repeat: no-repeat;\n      width: 130px;\n      height: 134px;\n      margin-top: 10px;\n    }\n    .game {\n      font-size: 30px;\n      font-weight: 600;\n      color: #FFFFFF;\n      line-height: 42px;\n    }\n    .phone {\n      background-image: url(\"~@/assets/koa/pwa/icon_guide_phone.png\");\n      background-size: 100% 100%;\n      background-position: center center;\n      background-repeat: no-repeat;\n      width: 240px;\n      height: 261px;\n      margin-top: -62px;\n      margin-bottom: -20px;\n    }\n    .txt {\n      font-size: 32px;\n      font-weight: 600;\n      color: #FFFFFF;\n      line-height: 1;\n      margin-top: 10px;\n      div {\n        display: inline-block;\n        vertical-align: middle;\n        background-size: 100% 100%;\n        background-position: center center;\n        background-repeat: no-repeat;\n        width: 68px;\n        height: 68px;\n      }\n      .up {\n        background-image: url(\"~@/assets/common/pwa/icon_guide_up.png\");\n      }\n      .add {\n        background-image: url(\"~@/assets/common/pwa/icon_guide_add.png\");\n      }\n    }\n    .mune {\n      margin-top: 15px;\n      width: 454px;\n      height: 226px;\n      background-image: url(\"~@/assets/common/pwa/guide_mune.png\");\n      background-size: 100% 100%;\n      background-position: center center;\n      background-repeat: no-repeat;\n      position: relative;\n      text-align: left;\n      div {\n        position: absolute;\n        height: 85px;\n        line-height: 85px;\n        bottom: 0;\n        left: 20px;\n        right: 80px;\n        font-size: 26px;\n        color: #585555;\n        white-space: nowrap;\n        overflow: hidden;\n        text-overflow: ellipsis;\n        font-weight: 600;\n      }\n    }\n    .arr {\n      width: 50px;\n      height: 50px;\n      background-image: url(\"~@/assets/common/pwa/icon_guide_arr.png\");\n      background-size: 100% 100%;\n      background-position: center center;\n      background-repeat: no-repeat;\n      margin-top: 20px;\n    }\n  }\n  .heart {\n    animation: breathe 1s ease-in-out infinite;\n  }\n}\n\n.add-ios-wrapper.koa{\n  .ios-guide{\n    .logo{\n      background-image: url(\"~@/assets/koa/pwa/icon_guide_logo.png\");\n    }\n    .phone{\n      background-image: url(\"~@/assets/koa/pwa/icon_guide_phone.png\");\n    }\n  }\n}\n.add-ios-wrapper.aof{\n  .ios-guide{\n    .logo{\n      background-image: url(\"~@/assets/koa/aof/icon_guide_logo.png\");\n    }\n    .phone{\n      background-image: url(\"~@/assets/koa/aof/icon_guide_phone.png\");\n    }\n  }\n}\n.add-ios-wrapper.rom{\n  .ios-guide{\n    .logo{\n      background-image: url(\"~@/assets/koa/rom/icon_guide_logo.png\");\n    }\n    .phone{\n      background-image: url(\"~@/assets/koa/rom/icon_guide_phone.png\");\n    }\n  }\n}\n.add-ios-wrapper.dc{\n  .ios-guide{\n    .logo{\n      background-image: url(\"~@/assets/dc/pwa/icon_guide_logo.png\");\n    }\n    .phone{\n      background-image: url(\"~@/assets/dc/pwa/icon_guide_phone.png\");\n    }\n  }\n}\n.add-ios-wrapper.ssv2{\n  .ios-guide{\n    .logo{\n      background-image: url(\"~@/assets/ss/ssv2/icon_guide_logo.png\");\n    }\n    .phone{\n      background-image: url(\"~@/assets/ss/ssv2/icon_guide_phone.png\");\n    }\n  }\n}\n</style>\n", "import mod from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./AddIosSafariBtn.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./AddIosSafariBtn.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./AddIosSafariBtn.vue?vue&type=template&id=1df0d6bd&scoped=true\"\nimport script from \"./AddIosSafariBtn.vue?vue&type=script&lang=js\"\nexport * from \"./AddIosSafariBtn.vue?vue&type=script&lang=js\"\nimport style0 from \"./AddIosSafariBtn.vue?vue&type=style&index=0&id=1df0d6bd&prod&scoped=true&lang=scss\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"1df0d6bd\",\n  null\n  \n)\n\nexport default component.exports", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('common-part',{class:['diamond-part-wrapper', _vm.$gameName ],attrs:{\"id\":\"diamond-part-wrapper\"},scopedSlots:_vm._u([{key:\"label\",fn:function(){return [_c('div',{staticClass:\"label-wrap\"},[_c('span',{staticClass:\"label\",on:{\"click\":function($event){return _vm.$root.$emit('showWhatIsDiamondPop')}}},[_vm._v(\" \"+_vm._s(_vm.$t('charge_gear'))+\" \"),_c('i',{staticClass:\"diamond-icon\"})]),(_vm.isMobile)?_c('div',{staticClass:\"charge-construction\",on:{\"click\":function($event){return _vm.$root.$emit('showPop', 'ChargeConstruction')}}},[_c('i'),_vm._v(_vm._s(_vm.$t('construction_title'))+\" \")]):_vm._e()])]},proxy:true}])},[_c('div',{staticClass:\"diamond-list-wrapper\"},[_vm._l((_vm.diamondList),function(diamondItem,index){return _c('div',{key:diamondItem.product_id,class:['diamond-item',{'diamond-item__active':diamondItem.product_id === _vm.chosenDiamond.product_id }, { 'sold-out': diamondItem.soldOut }],on:{\"click\":function($event){return _vm.toggleStatus(index)}}},[(diamondItem.type === 2)?[_c('div',{staticClass:\"top\"},[_c('div',{staticClass:\"coin-num\"},[_vm._v(_vm._s(_vm.lastDiamondCalc.totalDiamond)+\" \"),_c('i',{staticClass:\"diamond-icon\"})]),_c('transition',{attrs:{\"name\":\"bonus\"}},[(_vm.showBonus)?_c('div',{staticClass:\"bonus\"},[_c('div',{staticClass:\"bonus-description\"},[_vm._v(\"+\"+_vm._s(_vm.vip.diamondBonus * _vm.lastDiamondCalc.totalDiamond)+\" \"),_c('br'),_vm._v(\" \"),_c('i')])]):_vm._e()]),_c('div',{staticClass:\"diamond-input-wrapper\"},[_c('span',{staticClass:\"basic-num\"},[_vm._v(_vm._s(_vm.lastDiamondCalc.coin))]),_c('i'),_vm._v(\" x \"),_c('input',{directives:[{name:\"model\",rawName:\"v-model\",value:(_vm.chosenNum),expression:\"chosenNum\"}],attrs:{\"type\":\"number\",\"disabled\":\"\"},domProps:{\"value\":(_vm.chosenNum)},on:{\"input\":function($event){if($event.target.composing)return;_vm.chosenNum=$event.target.value}}}),_c('div',{staticClass:\"tips\",on:{\"click\":function($event){$event.stopPropagation();_vm.showPopTips = true}}},[(_vm.showPopTips)?_c('div',{staticClass:\"custom-num-range-tips\"},[_vm._v(_vm._s(_vm.$vt('customDiamondLimitTips')))]):_vm._e()])])],1),_c('div',{staticClass:\"bottom\"},[_c('div',{class:['now',{'is-ar-zone': _vm.isArZone}]},[_vm._v(_vm._s(_vm.diamondState(diamondItem).priceState.nowPrice))]),(_vm.diamondState(diamondItem).priceState.originPrice)?_c('div',{class:['origin',{'is-ar-zone': _vm.isArZone}]},[_c('del',[_vm._v(_vm._s(_vm.diamondState(diamondItem).priceState.originPrice))])]):_vm._e()])]:[_c('div',{staticClass:\"top\"},[_c('div',{staticClass:\"coin-num\"},[_vm._v(\" \"+_vm._s(diamondItem.coin)+\" \"),_c('i',{staticClass:\"diamond-icon\"})]),_c('transition',{attrs:{\"name\":\"bonus\"}},[(_vm.showBonus)?_c('div',{staticClass:\"bonus\"},[_c('div',{staticClass:\"bonus-description\"},[_vm._v(\"+\"+_vm._s(_vm.vip.diamondBonus * diamondItem.coin)+\" \"),_c('br'),_vm._v(\" \"),_c('i')])]):_vm._e()]),_c('div',{class:['image', 'image_' + index]}),(diamondItem.soldOut)?_c('div',{staticClass:\"sold-out-mask\"}):_vm._e()],1),_c('div',{staticClass:\"bottom\"},[_c('div',{class:['now',{'is-ar-zone': _vm.isArZone}]},[_vm._v(_vm._s(_vm.diamondState(diamondItem).priceState.nowPrice))]),(_vm.diamondState(diamondItem).priceState.originPrice)?_c('div',{class:['origin',{'is-ar-zone': _vm.isArZone}]},[_c('del',[_vm._v(_vm._s(_vm.diamondState(diamondItem).priceState.originPrice))])]):_vm._e()])],(_vm.isKOA && !_vm.isLogin && _vm.firstPayProducts[diamondItem.product_id])?_c('div',{class:['bonus', 'orange']},[_c('p',{staticClass:\"discount\"},[_c('over-size-scale',[_vm._v(\"20%\")])],1),_c('p',{staticClass:\"off\"},[_vm._v(\"OFF\")])]):_vm._e(),(_vm.diamondState(diamondItem).bonusState.type)?_c('div',{staticClass:\"common-bonus\"},[(_vm.diamondState(diamondItem).bonusState.type === 'rebate')?[_c('div',{staticClass:\"send\"},[_c('over-size-scale',[_vm._v(_vm._s(_vm.$t('bonus_tips')))])],1),_c('div',{staticClass:\"num\"},[_vm._v(_vm._s(_vm.diamondState(diamondItem).bonusState.coin)),_c('i',{staticClass:\"diamond-icon\"})])]:_vm._e(),(_vm.diamondState(diamondItem).bonusState.type === 'coupon')?[_c('div',{staticClass:\"discount\"},[_vm._v(_vm._s(_vm.diamondState(diamondItem).bonusState.rate))]),_c('div',{staticClass:\"off\"},[_vm._v(\"OFF\")])]:_vm._e()],2):_vm._e(),(diamondItem.total_purchase_times || diamondItem.purchased_times)?_c('div',{staticClass:\"forbidden-by-num\"},[_vm._v(\" \"+_vm._s(diamondItem.purchased_times)+\"/\"+_vm._s(diamondItem.total_purchase_times)+\" \")]):_vm._e()],2)}),(!_vm.diamondList.length)?_c('div',{staticClass:\"empty\"},[_vm._v(_vm._s(_vm.$t('nothingHere')))]):_vm._e()],2),(_vm.$store.state.country === 'TW' && _vm.$gameName === 'ssv')?_c('extra-diamond'):_vm._e()],1)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"extra-diamond-wrapper\",on:{\"click\":function($event){$event.stopPropagation();}}},[_c('div',{staticClass:\"select-wrapper\"},[_c('div',{class:['label-wrapper', {'label-wrapper-active': _vm.chosenSpecialDiamond.product_id}],on:{\"click\":function($event){_vm.showList = !_vm.showList}}},[_vm._m(0),_c('div',{staticClass:\"right-part\"},[(_vm.chosenSpecialDiamond.product_id)?_c('div',{staticClass:\"chosen-diamond\"},[_c('span',{staticClass:\"products\"},[_vm._v(_vm._s(_vm.chosenSpecialDiamond.coin)+\" \"),_c('i')]),_c('span',[_vm._v(_vm._s(_vm.chosenSpecialDiamond.price)+\" \"+_vm._s(_vm.chosenSpecialDiamond.currency_symbol))])]):_c('div',{staticClass:\"more\"},[_vm._v(_vm._s(_vm.$t('load_more')))]),_c('i',{class:[{'toggle-active': _vm.showList}]})])]),_c('transition',{attrs:{\"name\":\"option\"}},[(_vm.showList)?_c('div',{staticClass:\"option-list\"},_vm._l((_vm.extraList),function(diamondItem,index){return _c('div',{key:index,class:['option-item', { 'option-item-active': diamondItem.product_id === _vm.chosenSpecialDiamond.product_id }],on:{\"click\":function($event){return _vm.toggleStatus(index)}}},[_c('span',{staticClass:\"products\"},[_vm._v(_vm._s(diamondItem.coin)+\" \"),_c('i')]),_c('span',[_vm._v(_vm._s(diamondItem.price)+\" \"+_vm._s(diamondItem.currency_symbol))])])}),0):_vm._e()])],1)])\n}\nvar staticRenderFns = [function (){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"my-card-logo\"},[_c('img',{attrs:{\"src\":require(\"../assets/common/channel/mycard-logo.png\"),\"alt\":\"\"}})])\n}]\n\nexport { render, staticRenderFns }", "<template>\n  <div class=\"extra-diamond-wrapper\" @click.stop>\n    <div class=\"select-wrapper\">\n      <div :class=\"['label-wrapper', {'label-wrapper-active': chosenSpecialDiamond.product_id}]\" @click=\"showList = !showList\">\n        <div class=\"my-card-logo\">\n          <img src=\"../assets/common/channel/mycard-logo.png\" alt=\"\">\n        </div>\n        <div class=\"right-part\">\n          <div class=\"chosen-diamond\" v-if=\"chosenSpecialDiamond.product_id\">\n            <span class=\"products\">{{ chosenSpecialDiamond.coin }} <i></i></span>\n            <span>{{ chosenSpecialDiamond.price }} {{ chosenSpecialDiamond.currency_symbol }}</span>\n          </div>\n          <div class=\"more\" v-else>{{ $t('load_more') }}</div>\n          <i :class=\"[{'toggle-active': showList}]\"></i>\n        </div>\n      </div>\n      <transition name=\"option\">\n        <div class=\"option-list\" v-if=\"showList\">\n          <div :class=\"['option-item', { 'option-item-active': diamondItem.product_id === chosenSpecialDiamond.product_id }]\" @click=\"toggleStatus(index)\" v-for=\"(diamondItem, index) in extraList\" :key=\"index\">\n            <span class=\"products\">{{ diamondItem.coin }} <i></i></span>\n            <span>{{ diamondItem.price }} {{ diamondItem.currency_symbol }}</span>\n          </div>\n        </div>\n      </transition>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { mapState } from 'vuex'\n\nexport default {\n  name: 'extraDiamond',\n  data () {\n    return {\n      showList: false,\n      extraList: []\n    }\n  },\n  computed: {\n    ...mapState('formdata', ['chosenDiamond']),\n    chosenSpecialDiamond () {\n      return this.$store.getters['formdata/TWMyCard'] ? this.chosenDiamond : {}\n    }\n  },\n  methods: {\n    toggleStatus (index) {\n      this.$store.commit('formdata/setChosenDiamond', this.extraList[index])\n      this.showList = false\n    }\n  },\n  created () {\n    this.$root.$on('updateSpecialDiamond', value => {\n      this.extraList = value\n    })\n    this.$root.$on('BodyClick', () => {\n      this.showList = false\n    })\n  }\n}\n</script>\n\n<style scoped lang=\"scss\">\n@use \"~@/utils/utils.scss\" as utils;\n\n.option-enter-active{\n  transform: translateY(40px);\n  transition: all .3s ease-in-out;\n}\n.option-enter-to{\n  transform: translateY(0);\n}\n\n.option-leave-active {\n  transition: all .3s;\n}\n\n.option-leave-to{\n  opacity: 0;\n}\n\n.extra-diamond-wrapper{\n  display: inline-block;\n  position: relative;\n  z-index: 1;\n  width: 100%;\n  margin-top: 20px;\n\n  .label-wrapper{\n    display: flex;\n    align-items: center;\n    justify-content: space-between;\n    z-index: 2;\n    position: relative;\n    background: linear-gradient(to bottom, rgba(198, 165, 88, .2), rgb(158, 113, 61));\n    border: 1px solid #988B60;\n    height: 54px;\n    padding:0 20px;\n    border-radius: 4px;\n    cursor: pointer;\n\n    .my-card-logo{\n      height: 43px;\n      display: flex;\n      img{\n        height: 100%;\n      }\n    }\n\n    .right-part{\n      display: flex;\n      align-items: center;\n      justify-content: center;\n\n      .more{\n        font-size: 26px;\n        font-family: PingFangSC-Medium, PingFang SC;\n        font-weight: 500;\n        color: #FFFFFF;\n      }\n\n      .chosen-diamond{\n        display: flex;\n        justify-content: space-between;\n        align-items: center;\n        cursor: pointer;\n        font-size: 28px;\n        font-family: PingFangSC-Semibold, PingFang SC;\n        font-weight: 600;\n        color: #FFFFFF;\n\n        span{\n          display: flex;\n          align-items: center;\n          white-space: nowrap;\n\n          i{\n            @include utils.bgCenterForSSV('diamond/diamond.png', 29px, 29px);\n            margin-left: 4px;\n            margin-right: 20px;\n          }\n        }\n      }\n\n      i{\n        @include utils.bgCenterForCommon('channel/mycard-toggle.png', 32px, 32px);\n        transition: all .3s;\n        &.toggle-active{\n          transform: rotate(450deg);\n        }\n      }\n    }\n\n    &.label-wrapper-active{\n      position: relative;\n      box-sizing: border-box;\n      border-color: #FF5E0F;\n      &:before{\n        display: inline-block;\n        content: ' ';\n        position: absolute;\n        right: -1px;\n        top: -1px;\n        z-index: 1;\n        @include utils.bgCenterForCommon('channel/choose-active.png', 29px, 29px);\n      }\n    }\n  }\n\n  .option-list{\n    position: absolute;\n    top: 100%;\n    width: 100%;\n    z-index: 1;\n    background: #000000;\n    border-radius: 4px;\n    border: 1px solid #988B60;\n    margin-top: 10px;\n\n    .option-item{\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      cursor: pointer;\n      line-height: 53px;\n      font-size: 28px;\n      font-family: PingFangSC-Semibold, PingFang SC;\n      font-weight: 600;\n      color: #FFFFFF;\n      border-bottom: 1px solid #988B60;\n      padding: 0 20px;\n      transition: all .3s;\n\n      span{\n        display: flex;\n        align-items: center;\n        white-space: nowrap;\n\n        i{\n          @include utils.bgCenterForSSV('diamond/diamond.png', 18px, 18px);\n          margin-left: 5px;\n        }\n      }\n\n      &:last-of-type{\n        border: none;\n      }\n\n      &.option-item-active, &:hover{\n        background: rgba(255, 255, 255, .2);\n        color: #FF5E0F;\n      }\n    }\n  }\n}\n\n@include utils.setPcContent {\n  .extra-diamond-wrapper{\n    width: 426px;\n    .label-wrapper{\n      height: 44px;\n      .my-card-logo{\n        height: 33px;\n      }\n      .right-part{\n        .more{\n          font-size: 18px;\n        }\n        .chosen-diamond{\n          font-size: 18px;\n\n          span{\n            i{\n              @include utils.bgCenterForSSV('diamond/diamond.png', 18px, 18px);\n              margin-right: 30px;\n              margin-left: 2px;\n            }\n          }\n        }\n        i{\n          @include utils.bgCenterForCommon('channel/mycard-toggle.png', 24px, 24px)\n        }\n      }\n      &.label-wrapper-active{\n        &:before{\n          @include utils.bgCenterForCommon('channel/choose-active.png', 24px, 24px);\n        }\n      }\n    }\n    .option-list{\n      margin-top: 4px;\n      overflow-y: scroll;\n      max-height: 230px;\n\n      .option-item{\n        line-height: 40px;\n        font-size: 18px;\n\n        span{\n          i{\n            margin-left: 2px;\n          }\n        }\n      }\n    }\n  }\n}\n</style>\n", "import mod from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./ExtraDiamond.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./ExtraDiamond.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./ExtraDiamond.vue?vue&type=template&id=5c91cc60&scoped=true\"\nimport script from \"./ExtraDiamond.vue?vue&type=script&lang=js\"\nexport * from \"./ExtraDiamond.vue?vue&type=script&lang=js\"\nimport style0 from \"./ExtraDiamond.vue?vue&type=style&index=0&id=5c91cc60&prod&scoped=true&lang=scss\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"5c91cc60\",\n  null\n  \n)\n\nexport default component.exports", "<!-- ss 大额档位充值 -->\n<template>\n  <common-part :class=\"['diamond-part-wrapper', $gameName ]\" id=\"diamond-part-wrapper\">\n    <template #label>\n      <div class=\"label-wrap\">\n        <span class=\"label\" @click=\"$root.$emit('showWhatIsDiamondPop')\">\n          {{ $t('charge_gear') }} <i class=\"diamond-icon\"></i>\n        </span>\n        <!--<div v-if=\"isPc\" @click=\"$root.$emit('showPop', 'WhatIsDiamond')\" class=\"whats-diamond-pop-btn\"><i></i>{{ $t('how-to-use-diamond') }}</div>-->\n        <div v-if=\"isMobile\" class=\"charge-construction\" @click=\"$root.$emit('showPop', 'ChargeConstruction')\">\n          <i></i>{{ $t('construction_title') }}\n        </div>\n      </div>\n    </template>\n    <div class=\"diamond-list-wrapper\">\n      <div\n        :class=\"['diamond-item',{'diamond-item__active':diamondItem.product_id === chosenDiamond.product_id }, { 'sold-out': diamondItem.soldOut }]\"\n        v-for=\"(diamondItem,index) in diamondList\"\n        :key=\"diamondItem.product_id\"\n        @click=\"toggleStatus(index)\">\n        <template v-if=\"diamondItem.type === 2\">\n          <div class=\"top\">\n            <div class=\"coin-num\">{{ lastDiamondCalc.totalDiamond }} <i class=\"diamond-icon\"></i></div>\n            <transition name=\"bonus\">\n              <div class=\"bonus\" v-if=\"showBonus\">\n                <div class=\"bonus-description\">+{{ vip.diamondBonus * lastDiamondCalc.totalDiamond }} <br> <i></i></div>\n              </div>\n            </transition>\n            <div class=\"diamond-input-wrapper\">\n              <span class=\"basic-num\">{{ lastDiamondCalc.coin }}</span>\n              <i></i>\n              x\n              <input type=\"number\" v-model=\"chosenNum\" disabled>\n              <div class=\"tips\" @click.stop=\"showPopTips = true\">\n                <div v-if=\"showPopTips\" class=\"custom-num-range-tips\">{{ $vt('customDiamondLimitTips') }}</div>\n              </div>\n            </div>\n          </div>\n          <div class=\"bottom\">\n            <div :class=\"['now',{'is-ar-zone': isArZone}]\">{{ diamondState(diamondItem).priceState.nowPrice }}</div>\n            <div v-if=\"diamondState(diamondItem).priceState.originPrice\" :class=\"['origin',{'is-ar-zone': isArZone}]\">\n              <del>{{ diamondState(diamondItem).priceState.originPrice }}</del>\n            </div>\n          </div>\n        </template>\n        <template v-else>\n          <div class=\"top\">\n            <div class=\"coin-num\">\n              {{ diamondItem.coin }} <i class=\"diamond-icon\"></i>\n            </div>\n            <transition name=\"bonus\">\n              <div class=\"bonus\" v-if=\"showBonus\">\n                <div class=\"bonus-description\">+{{ vip.diamondBonus * diamondItem.coin }} <br> <i></i></div>\n              </div>\n            </transition>\n            <div :class=\"['image', 'image_' + index]\" ></div>\n            <div class=\"sold-out-mask\" v-if=\"diamondItem.soldOut\"></div>\n          </div>\n          <div class=\"bottom\">\n            <div :class=\"['now',{'is-ar-zone': isArZone}]\">{{ diamondState(diamondItem).priceState.nowPrice }}</div>\n            <div v-if=\"diamondState(diamondItem).priceState.originPrice\" :class=\"['origin',{'is-ar-zone': isArZone}]\">\n              <del>{{ diamondState(diamondItem).priceState.originPrice }}</del>\n            </div>\n          </div>\n        </template>\n\n        <!-- koa 专用未登录 && 首充折扣档位  -  展示首充 20%折扣 -->\n        <div v-if=\"isKOA && !isLogin && firstPayProducts[diamondItem.product_id]\" :class=\"['bonus', 'orange']\">\n          <p class=\"discount\"><over-size-scale>20%</over-size-scale></p>\n          <p class=\"off\">OFF</p>\n        </div>\n        <!-- koa之后，使用的通用的弹窗样式-->\n        <div v-if=\"diamondState(diamondItem).bonusState.type\" class=\"common-bonus\">\n          <template v-if=\"diamondState(diamondItem).bonusState.type === 'rebate'\">\n            <div class=\"send\">\n              <over-size-scale>{{ $t('bonus_tips') }}</over-size-scale>\n            </div>\n            <div class=\"num\">{{ diamondState(diamondItem).bonusState.coin }}<i class=\"diamond-icon\"></i></div>\n          </template>\n          <template v-if=\"diamondState(diamondItem).bonusState.type === 'coupon'\">\n            <div class=\"discount\">{{ diamondState(diamondItem).bonusState.rate }}</div>\n            <div class=\"off\">OFF</div>\n          </template>\n        </div>\n        <!--限购-->\n        <div v-if=\"diamondItem.total_purchase_times || diamondItem.purchased_times\" class=\"forbidden-by-num\">\n          {{ diamondItem.purchased_times }}/{{ diamondItem.total_purchase_times }}\n        </div>\n      </div>\n      <div class=\"empty\" v-if=\"!diamondList.length\">{{ $t('nothingHere') }}</div>\n    </div>\n    <extra-diamond v-if=\"$store.state.country === 'TW' && $gameName === 'ssv'\"></extra-diamond>\n  </common-part>\n</template>\n\n<script>\nimport CommonPart from '@/components/common/CommonPart.vue'\nimport { mapState, mapGetters } from 'vuex'\nimport { priceHelper } from '@/utils/utils'\nimport { getActivityListForToken, getTokenList, getAmeDo } from '@/server'\nimport OverSizeScale from './OverSizeScale.vue'\nimport ExtraDiamond from '@/components/ExtraDiamond.vue'\n\nconst minCustomDiamondNum = window.$gcbk('ids.minCustomDiamondNum', 11)\nexport default {\n  name: 'DiamondChooseKOA',\n  components: { ExtraDiamond, CommonPart, OverSizeScale },\n  data () {\n    return {\n      diamondList: [],\n      canICustom: false,\n      lastDiamondItem: {},\n      chosenNum: minCustomDiamondNum,\n      showPopTips: false\n    }\n  },\n  computed: {\n    ...mapState('formdata', ['chosenDiamond', 'chosenCoupon', 'vip', 'isInit', 'isFirstPayUsed', 'chosenCoupon', 'chosenCouponOther', 'firstPayProducts']),\n    ...mapState('gameinfo', ['defaultDiscount', 'isKOA']),\n    ...mapState('userinfo', ['isLogin']),\n    ...mapState(['isPc', 'isMobile']),\n    ...mapState(['isArZone']),\n    ...mapGetters('formdata', ['takeEffectDefaultDiscount', 'takeEffectDefaultRebate', 'FinalPriceState', 'isDiamondOwn95Off', 'getRebateCoin', 'isDiamondOwnRebate']),\n    ...mapState('functionSwitch', ['smallDiamondDoubleDiscount']),\n    ...mapState('vb', ['isDiscountUsed']),\n    showBonus () {\n      return this.isInit && this.isFirstPayUsed && !this.chosenCoupon.FE_INDEX && this.vip.isInit\n    },\n    lastDiamondCalc () {\n      const { level_currency_price: price, currency, coin, tax_rate: taxRate } = this.lastDiamondItem\n      // 大额档位暂时没有折扣\n      const obj = Object.assign({}, this.lastDiamondItem, {\n        totalDiamond: coin * this.chosenNum,\n        chosenNum: +this.chosenNum,\n        defaultPrice: priceHelper(minCustomDiamondNum * price, currency), // 11倍的原价\n        nowPrice: priceHelper(this.chosenNum * price, currency), // 折扣价\n        nowPriceWidthTax: priceHelper(price * this.chosenNum * (taxRate || 1), currency) // level_currency_price * 倍数 * tax_rate\n      })\n      obj.taxation = priceHelper((obj.nowPriceWidthTax - obj.nowPrice), currency)\n      return obj\n    },\n    /* 计算每个档位的状态 */\n    diamondState () {\n      const chosenDiamond = this.chosenDiamond\n      const chosenCoupon = this.chosenCoupon\n      const pickChosenCouponInfo = (formatState, priceState, couponInfo) => {\n        if (['first_pay', 'discount_coupon', 'fixed_discount_coupon'].includes(chosenCoupon.feType)) {\n          formatState.type = 'coupon'\n          formatState.rate = chosenCoupon.rate\n\n          priceState.originPrice = couponInfo.level_currency_price\n          priceState.nowPrice = couponInfo.no_tax_price\n        } else if (chosenCoupon.feType === 'cash_coupon') {\n          formatState.type = 'coupon'\n          formatState.rate = `${chosenCoupon.deduct_price} ${chosenDiamond.currency_symbol}`\n          priceState.originPrice = couponInfo.level_currency_price\n          priceState.nowPrice = couponInfo.no_tax_price\n        } else if (['rebate_coupon', 'fixed_rebate', 'fixed_dynamic_rebate', 'first_pay_rebate'].includes(chosenCoupon.feType || couponInfo.feType)) { // 两种情况：不是优惠券就是foundation的首冲\n          formatState.type = 'rebate'\n          formatState.coin = couponInfo.rate || chosenCoupon.rate\n\n          priceState.nowPrice = couponInfo.level_currency_price\n        }\n      }\n      return (diamondItem) => {\n        const formatState = {\n          type: '', // coupon、rebate\n          rate: '',\n          coin: 0\n        }\n        const priceState = { nowPrice: 0, originPrice: 0 }\n        // foundation 每个档位都有首冲，优先使用首冲优惠券\n        if (this.firstPayProducts[diamondItem.product_id]) {\n          const firstPayCoupon = this.firstPayProducts[diamondItem.product_id]\n          pickChosenCouponInfo(formatState, priceState, firstPayCoupon)\n        } else if ((diamondItem.product_id === chosenDiamond.product_id && chosenCoupon.productId === diamondItem.product_id)) {\n          pickChosenCouponInfo(formatState, priceState, chosenCoupon)\n        } else if (this.chosenCouponOther[diamondItem.product_id]) {\n          const chosenCouponOther = this.chosenCouponOther[diamondItem.product_id]\n          pickChosenCouponInfo(formatState, priceState, chosenCouponOther)\n        } else if (this.isDiamondOwn95Off(diamondItem)) {\n          // case2：固定折扣\n          formatState.type = 'coupon'\n          formatState.rate = `${this.$store.state.formdata.defaultDiscountInfo.rateWidthOutPercent}%`\n\n          priceState.originPrice = this.isDiamondOwn95Off(diamondItem).level_currency_price\n          priceState.nowPrice = this.isDiamondOwn95Off(diamondItem).no_tax_price\n        } else if (this.isDiamondOwnRebate(diamondItem)) {\n          // case3：固定返钻\n          formatState.type = 'rebate'\n          formatState.coin = this.getRebateCoin(diamondItem)\n\n          if (this.$gameName === 'dc') {\n            const levelCoin = this.$store.state.formdata.defaultRebateDynamicInfoAll[diamondItem.product_id].level_coin\n            const discount = this.getRebateCoin(diamondItem) / levelCoin\n            formatState.coin = `${Math.floor(discount * 100)}%`\n          }\n\n          priceState.nowPrice = this.isDiamondOwnRebate(diamondItem).no_tax_price\n        } else {\n          // case4：默认情况\n          priceState.nowPrice = diamondItem.level_currency_price\n\n          // 如果为大额 直接展示lastDiamondCalc的价格\n          if (diamondItem.type === 2) priceState.nowPrice = this.lastDiamondCalc.nowPrice\n        }\n\n        // 小档位双倍钻石，修正上面的结果\n        const minimumDiamondId = this.$gcbk('ids.minimumDiamondId', '')\n        if (this.smallDiamondDoubleDiscount && diamondItem.product_id === minimumDiamondId) {\n          const showDoubleDiamond = () => {\n            if (this.chosenCoupon.feType === 'first_pay') return false\n            return (this.chosenDiamond.product_id === minimumDiamondId && !this.chosenCoupon.FE_INDEX) || // 如果选中第一个档位需要，需要不选中优惠券\n              this.chosenDiamond.product_id !== minimumDiamondId // 如果选中\n          }\n          if (!this.isDiscountUsed && showDoubleDiamond()) {\n            formatState.coin = diamondItem.coin\n            formatState.type = 'rebate'\n          }\n        }\n\n        if (priceState.nowPrice) priceState.nowPrice += ` ${diamondItem.currency_symbol}`\n        if (priceState.originPrice) priceState.originPrice += ` ${diamondItem.currency_symbol}`\n\n        // console.log({\n        //   bonusState: formatState,\n        //   priceState\n        // })\n\n        if (this.isKOA) formatState.type = undefined // koa登录后不展示优惠券\n        return {\n          bonusState: formatState,\n          priceState\n        }\n      }\n    }\n  },\n  methods: {\n    loadDiamondList () {\n      this.$loading.show()\n      getTokenList({ store_from: 'storeFromWeb' })\n        .then(res => {\n          const { data, code } = res\n          if (code === 0) {\n            this.diamondList = data.sort((a, b) => a.coin - b.coin)\n            this.diamondList = this.diamondList.map((item, index) => {\n              const { level_currency_price: price, currency, tax_rate: taxRate } = item\n              item.nowPriceWidthTax = priceHelper(price * (taxRate || 1), currency)\n\n              item.index = index\n              item.soldOut = (item.total_purchase_times || item.purchased_times) && item.total_purchase_times === item.purchased_times\n              return item\n            })\n            this.initCustomParams(this.diamondList)\n            for (const [key, value] of Object.entries(data)) {\n              if (value.product_id === this.$route.query.pd) return this.$store.commit('formdata/setChosenDiamond', data[key])\n            }\n            if (data && data.length) {\n              const index = this.diamondList.findIndex(item => !item.soldOut)\n              this.$store.commit('formdata/setChosenDiamond', data[index || 0])\n            }\n\n            if (this.$gameName === 'ssv2') this.initFixCouponByDiamond4(this.diamondList[4])\n          } else {\n            this.$toast.err(this.$t('fetchChannelError'))\n          }\n        })\n        .finally(() => this.$loading.hide())\n    },\n    toggleStatus (index) {\n      if (this.canICustom && index === this.diamondList.length - 1) {\n        if (this.lastDiamondCalc.totalDiamond === this.chosenDiamond.totalDiamond) return null\n        this.$root.$emit('showPop', 'CustomDiamond', { diamond: this.lastDiamondCalc, cb: this.toggleCustom.bind(this) })\n      } else {\n        const target = this.diamondList[index]\n        if (target.soldOut) return this.$toast.err(this.$t('product-sold-out'))\n\n        if (this.diamondList[index].product_id === this.chosenDiamond.product_id) return null\n        this.$store.commit('formdata/setChosenDiamond', this.diamondList[index])\n      }\n\n      this.$store.commit('formdata/resetChannel')\n    },\n    initCustomParams (diamondList = []) {\n      let index = -1\n      for (const [key, value] of Object.entries(diamondList)) {\n        if (value.type === 2) {\n          index = key\n          break\n        }\n      }\n\n      if (index !== -1) {\n        const aim = diamondList.splice(index, 1)[0]\n        diamondList.push(aim)\n\n        this.lastDiamondItem = aim\n        this.canICustom = true\n      }\n    },\n    toggleCustom (chosenNum) {\n      if (chosenNum) {\n        this.chosenNum = chosenNum\n      }\n      this.$store.commit('formdata/setChosenDiamond', this.lastDiamondCalc)\n    },\n\n    /* 小档位功能 */\n    initSmallDiamond () {\n      const { p1, p2 } = this.$gcbk('apiParams.smallDiamondDiscount', {})\n      const params = { p0: 'web', p1, p2 }\n      getAmeDo(params)\n        .then(res => {\n          const { code, data } = res\n          if (code === 0) {\n            this.$store.commit('vb/setIsDiscountUsed', data.is_received)\n          }\n        })\n\n      this.judgeFirstDoubleDiscountStatus()\n    },\n    // 通知服务端发小档位资格\n    judgeFirstDoubleDiscountStatus () {\n      const params = { p0: 'web', p1: 10, p2: 1144 }\n\n      params.p2 = 1144\n      getAmeDo(params)\n        .then(res => {\n          const { code } = res\n          if (code === 0) {\n            setTimeout(() => {\n              this.$root.$emit('reloadActivity')\n            }, 400)\n          }\n        })\n    },\n    smallDiamondEvent () {\n      this.$root.$on('couponChosen', () => {\n        if (this.isDiscountUsed) return null\n        if (this.chosenDiamond.coin === 100) this.$toast.err(this.$t('bonus_coupon_mutually_exclusive'))\n      })\n      // 如果是第一个档位有首冲双倍、且不是首冲8折\n      this.$root.$on('activityInitEnd', () => {\n        if (this.isDiscountUsed) return null\n        const chosenDiamond = this.chosenDiamond\n        const chosenCoupon = this.chosenCoupon\n\n        if (chosenDiamond.coin === 100 && ['discount_coupon', 'cash_coupon'].includes(chosenCoupon.feType)) {\n          console.log('reset coupon!')\n          this.$store.commit('formdata/resetCouponInfo')\n        }\n      })\n    },\n\n    // ss 特有，前面几个没有固定折扣，通过第四个来判断折扣\n    initFixCouponByDiamond4 (diamond) {\n      const getRate = value => ((1 - value) * 100).toFixed(0)\n      const params = {}\n      params.price = diamond.price\n      params.product_id = diamond.product_id\n\n      this.$loading.show()\n      getActivityListForToken(params)\n        .then(res => {\n          const { code, data } = res\n          if (code === 0) {\n            let defaultDiscountOrigin = data.fixed_discount || []\n            defaultDiscountOrigin = defaultDiscountOrigin.map((item, index) => ({\n              ...item,\n              feType: 'fixed_discount_coupon',\n              FE_INDEX: `fixed_discount_coupon_${index}`,\n              rateWidthOutPercent: getRate(item.discount)\n            }))\n            if (defaultDiscountOrigin.length) this.$store.commit('formdata/setFixedCouponByProduct4', defaultDiscountOrigin[0])\n\n            let fixedRebateOrigin = data.fixed_rebate || []\n            fixedRebateOrigin = fixedRebateOrigin.map((item, index) => ({\n              ...item,\n              feType: 'fixed_rebate',\n              rateWidthOutPercent: getRate(item.discount),\n              rate: `${getRate(item.discount)}%`,\n              FE_INDEX: `fixed_rebate_${index}`,\n              productId: params.product_id\n            }))\n            if (fixedRebateOrigin.length) this.$store.commit('formdata/setFixedRebateByProduct4', fixedRebateOrigin[0])\n          }\n        })\n        .finally(() => this.$loading.hide())\n    }\n  },\n  created () {\n    this.loadDiamondList()\n    if (this.smallDiamondDoubleDiscount) this.smallDiamondEvent()\n\n    this.$root.$on('loginEnd', state => {\n      if (state === 1) {\n        // 登录成功，重新刷新diamond\n        this.loadDiamondList()\n        if (this.smallDiamondDoubleDiscount) this.initSmallDiamond()\n      }\n    })\n    this.$root.$on('BodyClick', () => {\n      this.showPopTips = false\n    })\n  }\n}\n</script>\n\n<style scoped lang=\"scss\">\n@use \"~@/utils/utils.scss\" as utils;\n\n.is-ar-zone {\n  display: inline-block;\n}\n.label-wrap {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  position: relative;\n\n  .label{\n    text-decoration: underline;\n    cursor: pointer;\n    margin-bottom: 0;\n    i{\n      @include utils.bgCenter('koa/diamond/diamond.png', calc(23px * 1.3), calc(19px * 1.3));\n      display: inline-block;\n      margin-left: 6px;\n      position: relative;\n      top: 2px;\n    }\n\n    @include utils.setPcContent{\n      display: flex;\n      align-items: center;\n      justify-content: flex-end;\n      i{\n        @include utils.bgCenter('koa/diamond/diamond.png', calc(23PX * .8), calc(19PX * .8));\n        display: inline-block;\n        margin-left: 9px;\n        top: 0;\n      }\n    }\n  }\n\n  .charge-construction {\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    color: #FEB522;\n    line-height: 1;\n    cursor: pointer;\n    text-decoration: underline;\n    font-size: 18px;\n\n    i {\n      margin: 0 3px;\n      cursor: pointer;\n      @include utils.bgCenter('koa/charge-construction-flag.png', 18px, 18px);\n      flex-shrink: 0;\n    }\n  }\n\n  //.whats-diamond-pop-btn{\n  //  font-size: 16PX;\n  //  font-family: PingFangSC-Regular, PingFang SC;\n  //  font-weight: 400;\n  //  color: #FEB522;\n  //  line-height: 22PX;\n  //  position: absolute;\n  //  transform: translateY(40PX);\n  //  right: 0;\n  //  word-break: break-all;\n  //  max-width: 100%;\n  //  text-decoration: underline;\n  //\n  //  display: flex;\n  //  align-items: flex-start;\n  //  justify-content: center;\n  //  cursor: pointer;\n  //\n  //  i {\n  //    margin: 2.5PX 3PX 0;\n  //    cursor: pointer;\n  //    @include utils.bgCenter('koa/charge-construction-flag.png', 17PX, 17PX);\n  //    flex-shrink: 0;\n  //  }\n  //}\n}\n.diamond-list-wrapper {\n  flex-wrap: wrap;\n  display: flex;\n\n  .diamond-item {\n    display: flex;\n    align-items: flex-start;\n    justify-content: space-between;\n    cursor: pointer;\n    box-sizing: border-box;\n    flex-direction: column;\n    background-color: white;\n    position: relative;\n    @include utils.bgCenter('koa/diamond/diamond-bg_mobile.png', 216px, 168px);\n\n    .top{\n      height: 118px;\n      width: 100%;\n      flex-grow: 0;\n      position: relative;\n\n      .coin-num {\n        margin-top: 9px;\n        font-size: 21px;\n        font-family: PingFangSC-Semibold, PingFang SC;\n        font-weight: 600;\n        color: #FFFFFF;\n        line-height: 20px;\n        text-shadow: 0px 1px 1px rgba(0,0,0,0.7);\n        text-align: center;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n\n        i{\n          @include utils.bgCenter( 'koa/diamond/diamond.png', calc(23px * 0.8), calc(19px * .8));\n          display: inline-block;\n          margin: 0 1PX;\n        }\n      }\n\n      .bonus{\n        @include utils.bgCenter('koa/diamond/bonus.png', 56px, 56px);\n        position: absolute;\n        left: -10px;\n        z-index: 5;\n        top: -8px;\n\n        .bonus-description{\n          font-size: 13px;\n          transform: translate(-50%, -50%) rotate(-15deg);\n          position: absolute;\n          top: 50%;\n          left: 50%;\n          text-align: center;\n          font-family: FZRUIZHK_DA--GBK1-0, FZRUIZHK_DA--GBK1;\n          color: #372000;\n          text-shadow: 0px 0px 0px #FBF299;\n          font-weight: bold;\n          transform-origin: center center;\n\n          display: flex;\n          flex-direction: column;\n          justify-content: center;\n          align-items: center;\n\n          i{\n            @include utils.bgCenter('koa/diamond/diamond.png', calc(23px * .52), calc(19px * .52));\n            display: inline-block;\n          }\n        }\n      }\n\n      .image{\n        width: 120px;\n        height: 75px;\n        bottom: 8px;\n        position: absolute;\n        left: 50%;\n        transform: translateX(-50%);\n\n        @for $i from 1 to 8 {\n          &.image_#{$i - 1} {\n            background-image: url(~@/assets/koa/diamond/diamond-#{$i}.png);\n            background-size: 100% auto;\n            background-position: center center;\n            background-repeat: no-repeat;\n          }\n        }\n      }\n\n      .diamond-input-wrapper{\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        text-align: center;\n        width: 100%;\n        position: absolute;\n        bottom: 30px;\n\n        font-size: 18px;\n        font-family: PingFangSC-Regular, PingFang SC;\n        font-weight: 400;\n        color: #3D3D3D;\n\n        .basic-num{\n          font-size: 20px;\n          font-family: PingFangSC-Regular, PingFang SC;\n          font-weight: 400;\n          color: #3B2A00;\n        }\n\n        i {\n          margin: 0 3px 1px 0;\n          @include utils.bgCenter( 'koa/diamond/diamond.png', calc(23px * 0.7), calc(19px * .7));\n          flex-shrink: 0;\n          display: inline-block;\n        }\n\n        input{\n          border: none;\n          height: 34px;\n          margin-left: 3px;\n          appearance: none;\n          -webkit-appearance:none;\n          text-indent: 8px;\n          font-size: 18px;\n          font-family: PingFangSC-Semibold, PingFang SC;\n          font-weight: 600;\n          color: white;\n          background: #422E00;\n          display: inline-block;\n          width: 80px;\n          pointer-events: none;\n\n          &:active, &:focus{\n            appearance: none;\n            -webkit-appearance:none;\n            outline: none;\n          }\n        }\n        input::-webkit-outer-spin-button,\n        input::-webkit-inner-spin-button {\n          -webkit-appearance: none !important;\n          margin: 0;\n        }\n        input[type=number]{-moz-appearance:textfield;}\n\n        .tips{\n          display: none;\n        }\n      }\n    }\n\n    .bottom {\n      flex-grow: 1;\n      width: 100%;\n      text-align: center;\n      display: flex;\n      flex-direction: column;\n      align-items: center;\n      justify-content: center;\n\n      .now {\n        font-family: PingFangSC-Semibold, PingFang SC;\n        font-weight: 600;\n        color: #513900;\n        font-size: 20px;\n        line-height: 22px;\n      }\n\n      .origin {\n        font-family: PingFangSC-Regular, PingFang SC;\n        font-weight: 400;\n        color: #897136;\n        font-size: 12px;\n        line-height: 14px;\n      }\n    }\n\n    &.diamond-item__active {\n      position: relative;\n      box-sizing: border-box;\n\n      &:after{\n        display: inline-block;\n        content: ' ';\n        position: absolute;\n        right: 0;\n        top: 0;\n        z-index: 1;\n        @include utils.bgCenter('koa/diamond/choose-active_mobile.png', 100%, 100%);\n      }\n    }\n\n    .bonus{\n      &.orange{\n        @include utils.bgCenter('koa/diamond/bonus_orange.png', 60px, 60px);\n      }\n      position: absolute;\n      top: -8px;\n      left: -8px;\n      z-index: 5;\n      color: white;\n      text-align: center;\n      display: flex;\n      flex-direction: column;\n      justify-content: center;\n      align-items: center;\n      transition: background-image .5s;\n\n      .discount{\n        font-family: PingFangSC-Semibold, PingFang SC;\n        font-weight: 600;\n        color: #FFFFFF;\n        line-height: 1;\n        width: 100%;\n        align-items: center;\n        justify-content: center;\n        display:flex;\n        font-size: 16px;\n\n        :deep(.inner){\n          transform-origin: left bottom;\n          text-shadow: 1px 1px 2px #444444;\n          font-weight: bold;\n          font-size: 16px;\n        }\n      }\n\n      .off{\n        font-size: 10px;\n        font-family: PingFangSC-Medium, PingFang SC;\n        font-weight: 500;\n        color: #FFFFFF;\n        line-height: 1;\n        text-shadow: 1px 1px 2px #444444;\n      }\n    }\n\n    .common-bonus{\n      @include utils.bgCenterForDC('diamond/bonus-bg.png', 85px, 85px);\n      position: absolute;\n      top: 0;\n      left: 0;\n      color: white;\n      z-index: 0;\n\n      .discount{\n        transform: translate(-50%, -50%) rotate(-45deg);\n        position: absolute;\n        top: 24px;\n        left: 24px;\n        text-shadow: 0px 0px 2px #D5101B;\n        font-size: 15px;\n        font-weight: bold;\n        white-space: nowrap;\n      }\n      .off{\n        transform: translate(-50%, -50%) rotate(-45deg);\n        position: absolute;\n        top: 34px;\n        left: 34px;\n        font-size: 13px;\n      }\n\n      .send{\n        transform: translate(-50%, -50%) rotate(-45deg);\n        position: absolute;\n        top: 23px;\n        left: 23px;\n        max-width: 70%;\n        font-size: 14px;\n        font-weight: bold;\n      }\n      .num{\n        i{\n          @include utils.bgCenterForDC('diamond/diamond-icon.png', 15px, 15px);\n          margin-left: 2px;\n        }\n\n        transform: translate(-50%, -50%) rotate(-45deg);\n        position: absolute;\n        top: 35px;\n        left: 35px;\n        display: flex;\n        align-items: center;\n        font-size: 14px;\n        font-weight: bold;\n      }\n    }\n\n    /* 限制购买数量 */\n    .forbidden-by-num{\n      position: absolute;\n      right: 4px;\n      bottom: 45px;\n      height: 18px;\n      background: #8B755B;\n      border-radius: 9px;\n      font-size: 14px;\n      font-family: PingFangSC-Semibold, PingFang SC;\n      font-weight: 600;\n      color: #FFFFFF;\n      line-height: 18px;\n      padding: 0 7px;\n    }\n    .sold-out-mask{\n      @include utils.bgCenterForSSV('diamond/sold-out-mask.png', 98px, 72px);\n      position: absolute;\n      top: 22px;\n      left: 50%;\n      transform: translateX(-50%);\n      z-index: 11;\n      opacity: 1!important;\n    }\n    &.sold-out{\n      cursor: not-allowed;\n      * {\n        opacity: .7;\n      }\n    }\n  }\n\n  .empty {\n    font-size: 18px;\n    color: #8C8C8C;\n    margin-left: 9px;\n\n    @include utils.setPcContent{\n      font-size: 14PX;\n      margin-left: -1PX;\n      line-height: 30PX;\n    }\n  }\n}\n@include utils.setPcContent {\n  .diamond-list-wrapper {\n\n    .diamond-item{\n      width: 134PX;\n      height: 118PX;\n      @include utils.bgCenterForKoa( 'koa/diamond/diamond-bg.png', 134PX, 118PX);\n      transition: all .3s;\n\n      .bonus{\n        &.orange{\n          @include utils.bgCenter('koa/diamond/bonus_orange.png', 44px, 44px);\n        }\n        top: -10px;\n        left: -10px;\n\n        .discount{\n          font-size: 18px;\n        }\n\n        .off{\n          font-size: 10px;\n        }\n      }\n\n      .top{\n        height: 80PX;\n        width: 100%;\n        flex-grow: 0;\n        position: relative;\n\n        .coin-num {\n          margin-top: 11PX;\n          font-size: 14PX;\n          font-family: PingFangSC-Semibold, PingFang SC;\n          font-weight: 600;\n          color: #FFFFFF;\n          line-height: 20px;\n          text-shadow: 0px 1px 1px rgba(0,0,0,0.7);\n          text-align: center;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          position: relative;\n          left: 5PX;\n\n          i{\n            @include utils.bgCenter( 'koa/diamond/diamond.png', calc(23PX * 0.65), calc(19PX * .65));\n            display: inline-block;\n            margin: 0 1PX;\n          }\n        }\n\n        .bonus{\n          @include utils.bgCenter('koa/diamond/bonus.png', 48PX, 49PX);\n          position: absolute;\n          left: -8PX;\n          z-index: 5;\n          top: -6PX;\n\n          .bonus-description{\n            width: auto;\n            font-size: 12PX;\n            transform: translate(-50%, -50%) rotate(-15deg);\n            line-height: 14PX;\n\n            i{\n              @include utils.bgCenter('koa/diamond/diamond.png', calc(23PX * .52), calc(19PX * .52));\n              display: inline-block;\n            }\n          }\n        }\n\n        .image{\n          width: 80PX;\n          height: 50PX;\n          bottom: 0;\n          position: absolute;\n          left: 50%;\n          transform: translateX(-50%);\n\n          @for $i from 1 to 8 {\n            &.image_#{$i - 1} {\n              background-image: url(~@/assets/koa/diamond/diamond-#{$i}.png);\n              background-size: 93% auto;\n              background-position: center center;\n              background-repeat: no-repeat;\n            }\n          }\n        }\n\n        .diamond-input-wrapper{\n          bottom: 20px;\n          font-size: 12px;\n\n          .basic-num{\n            font-size: 14px;\n          }\n\n          i {\n            margin: 0 3px 1px 0;\n            @include utils.bgCenter( 'koa/diamond/diamond.png', calc(23px * 0.6), calc(19px * .6));\n            flex-shrink: 0;\n            display: inline-block;\n          }\n\n          input{\n            width: 54px;\n            height: 22px;\n            margin-left: 1px;\n            text-indent: 3px;\n            font-size: 12px;\n          }\n\n          .tips{\n            display: inline-block;\n            height: 12PX;\n            width: 12PX;\n            margin-left: 1PX;\n            cursor: pointer;\n            position: relative;\n            z-index: 10;\n            @include utils.bgCenter('koa/diamond/custom-diamond-tips-pop-btn.png', 12PX, 12PX);\n\n            .custom-num-range-tips{\n              width: 140PX;\n              min-height: 60PX;\n              background: white;\n              position: absolute;\n              left: 100%;\n              top: -16PX;\n              margin-left: 10PX;\n              border-radius: 5PX;\n\n              font-size: 14PX;\n              font-family: PingFangSC-Regular, PingFang SC;\n              font-weight: 400;\n              color: #383838;\n              line-height: 20PX;\n              text-shadow: 0PX 1PX 3PX rgba(0,0,0,0.5);\n              padding: 10px;\n              display: flex;\n              align-items: center;\n              justify-content: center;\n              box-shadow: 0px 1px 3px 0px rgba(0,0,0,0.5);\n\n              &:after{\n                content: '';\n                height: 0;\n                width: 0;\n                border-right: 5PX solid white;\n                border-bottom: 5PX solid transparent;\n                border-top: 5PX solid transparent;\n                position: absolute;\n                left: -4PX;\n                top: 16PX;\n              }\n            }\n          }\n        }\n      }\n\n      .bottom {\n        flex-grow: 1;\n        width: 100%;\n        text-align: center;\n        display: flex;\n        flex-direction: column;\n        align-items: center;\n        justify-content: center;\n\n        .now {\n          font-family: PingFangSC-Semibold, PingFang SC;\n          font-weight: 600;\n          color: #513900;\n          font-size: 18PX;\n          line-height: 20PX;\n        }\n\n        .origin {\n          font-family: PingFangSC-Regular, PingFang SC;\n          font-weight: 400;\n          color: #897136;\n          font-size: 12PX;\n          line-height: 14PX;\n          transform: scale(.75);\n          transform-origin: center center;\n          margin-top: -2PX;\n        }\n      }\n\n      $size: 8PX;\n      &:nth-of-type(n+6) {\n        margin-top: $size;\n      }\n      &:not(:nth-of-type(5n + 1)) {\n        margin-left: $size;\n      }\n      &.diamond-item__active {\n        position: relative;\n        box-sizing: border-box;\n\n        &:after {\n          display: inline-block;\n          content: ' ';\n          position: absolute;\n          left: 0;\n          top: 0;\n          z-index: 1;\n          width: 100%;\n          height: 100%;\n          box-sizing: border-box;\n          @include utils.bgCenter('koa/diamond/choose-active.png', 134PX, 119PX);\n        }\n      }\n      &:hover{\n        transform: translateY(-5PX);\n      }\n\n      .common-bonus{\n        @include utils.bgCenterForDC('diamond/bonus-bg.png', 53px, 53px);\n\n        .discount{\n          top: 15px;\n          left: 15px;\n          font-size: 10px;\n          ::v-deep{\n            .inner{\n              font-size: 13px;\n            }\n          }\n        }\n        .off{\n          top: 21px;\n          left: 21px;\n          font-size: 8px;\n        }\n\n        .send{\n          font-size: 9px;\n          top: 15px;\n          left: 15px;\n        }\n        .num{\n          font-size: 9px;\n          i{\n            @include utils.bgCenterForDC('diamond/diamond-icon.png', 11px, 11px);\n            margin-left: 1px;\n          }\n          top: 21px;\n          left: 21px;\n        }\n      }\n      .forbidden-by-num{\n        right: 3px;\n        bottom: 40px;\n        height: 14px;\n        border-radius: 7px;\n        font-size: 12px;\n        line-height: 14px;\n        padding: 0 5px;\n      }\n      .sold-out-mask{\n        @include utils.bgCenterForSSV('diamond/sold-out-mask.png', 76.44px, 56.1px);\n        top: 19px;\n      }\n    }\n  }\n}\n@include utils.setMobileContent{\n  .diamond-list-wrapper{\n    width: calc(100% + 12px);\n    transform: translate(-5px, -4px);\n\n    .diamond-item{\n      margin: 4px;\n    }\n  }\n}\n\n/* dc */\n.diamond-part-wrapper.dc{\n  .label-wrap {\n    .label{\n      text-decoration: none;\n      @include utils.flexCenter;\n      i{\n        @include utils.bgCenterForDC('diamond/diamond-icon.png', 35px, 35px);\n        display: inline-block;\n        margin-left: 6px;\n        position: relative;\n        top: -3px;\n      }\n    }\n\n    .charge-construction {\n      color: #FFCC66;\n      line-height: 1;\n      font-size: 18px;\n\n      i {\n        @include utils.bgCenterForDC('diamond/charge-construction-flag-diamond.png', 20px, 20px);\n      }\n    }\n  }\n  .diamond-list-wrapper{\n    .diamond-item{\n      @include utils.bgCenterForDC('diamond/diamond-bg_mobile.png', 216px, 168px);\n      background-color: transparent;\n\n      .top{\n        height: 113px;\n        position: relative;\n        .image{\n          width: 100px;\n          height: 100px;\n          top: 6px;\n\n          @for $i from 1 to 10 {\n            &.image_#{$i - 1} {\n              background-image: url(~@/assets/dc/diamond/diamond-#{$i}.png);\n              background-size: cover;\n            }\n          }\n        }\n        .coin-num {\n          height: 29px;\n          font-family: SourceHanSansCN, SourceHanSansCN;\n          font-weight: bold;\n          font-size: 20px;\n          color: #9399B7;\n          line-height: 29px;\n          text-shadow: none;\n          width: 100%;\n          position: absolute;\n          bottom: 0;\n          @include utils.flexCenter;\n          flex-direction: row-reverse;\n\n          i{\n            @include utils.bgCenterForDC( 'diamond/diamond-icon.png', 23px, 23px);\n            display: inline-block;\n            margin: 0;\n          }\n        }\n      }\n\n      .bottom{\n        .now{\n          color: #393A3E;\n        }\n        .origin {\n          color: #393A3E;\n        }\n      }\n\n      &.diamond-item__active{\n        &:after{\n          top: 50%;\n          left: 50%;\n          transform: translate(-50%, -50%);\n          @include utils.bgCenterForDC('diamond/choose-active_mobile.png', 218px, 171px);\n        }\n      }\n\n      .common-bonus{\n        @include utils.bgCenterForDC('diamond/bonus-bg.png', 122px, 22px);\n        position: absolute;\n        top: 0;\n        left: 0;\n        color: white;\n        z-index: 0;\n\n        @include utils.flexCenter;\n        justify-content: flex-start;\n        align-items: flex-start;\n        padding-left: 8px;\n        padding-top: 5.5px;\n\n        .discount{\n          font-size: 14px;\n          font-weight: bold;\n          white-space: nowrap;\n\n          position: static;\n          transform: rotate(0) translate(0, 0);\n          line-height: 14px;\n          height: 14px;\n          @include utils.flexCenter;\n          @extend .dc-stroke;\n        }\n        .off{\n          font-size: 14px;\n\n          position: static;\n          transform: rotate(0) translate(0, 0);\n          line-height: 14px;\n          height: 14px;\n          @include utils.flexCenter;\n          margin-left: 3px;\n          font-weight: bold;\n          @extend .dc-stroke;\n        }\n\n        .send{\n          font-size: 14px;\n          font-weight: bold;\n\n          position: static;\n          transform: rotate(0) translate(0, 0);\n          line-height: 15px;\n          height: 15px;\n          @include utils.flexCenter;\n          max-width: 45px;\n          @extend .dc-stroke;\n\n          ::v-deep{\n            .inner{\n              padding: 0 3px 0 4px;\n            }\n          }\n        }\n        .num{\n          i{\n            @include utils.bgCenterForDC('diamond/diamond-icon.png', 14px, 14px);\n            margin-left: 2px;\n            position: relative;\n            top: -1.5px;\n          }\n          font-size: 14px;\n          font-weight: bold;\n\n          position: static;\n          transform: rotate(0) translate(0, 0);\n          line-height: 14px;\n          @include utils.flexCenter;\n          margin-left: 3px;\n          @extend .dc-stroke;\n        }\n      }\n    }\n  }\n\n  @include utils.setPcContent{\n    .label-wrap {\n      .label{\n        @include utils.setPcContent{\n          justify-content: flex-end;\n          i{\n            @include utils.bgCenterForDC('diamond/diamond-icon.png', 30px, 30px);\n            margin-left: 0;\n            top: -2px;\n          }\n        }\n      }\n    }\n    .diamond-list-wrapper{\n      .diamond-item{\n        @include utils.bgCenterForDC('diamond/diamond-bg_pc.png', 134px, 118px);\n        background-color: transparent;\n\n        .top{\n          height: 81px;\n          position: relative;\n          .image{\n            width: 70px;\n            height: 70px;\n            top: 5px;\n          }\n          .coin-num {\n            height: 29px;\n            font-size: 14px;\n            color: #9399B7;\n            bottom: -3px;\n\n            i{\n              @include utils.bgCenterForDC( 'diamond/diamond-icon.png', 16px, 16px);\n            }\n          }\n        }\n\n        &.diamond-item__active{\n          &:after{\n            @include utils.bgCenterForDC('diamond/choose-active_pc.png', 140px, 122px);\n          }\n        }\n\n        .common-bonus{\n          @include utils.bgCenterForDC('diamond/bonus-bg-pc.png', 84px, 18px);\n\n          padding-left: 4px;\n          padding-top: 4.5px;\n\n          .discount{\n            font-size: 11px;\n\n            line-height: 11px;\n            height: 11px;\n          }\n          .off{\n            font-size: 11px;\n\n            line-height: 11px;\n            height: 11px;\n            margin-left: 3px;\n          }\n\n          .send{\n            font-size: 11px;\n            font-weight: bold;\n\n            line-height: 11px;\n            height: 11px;\n            max-width: 35px;\n            ::v-deep{\n              .inner{\n                padding: 0 2px;\n              }\n            }\n          }\n          .num{\n            i{\n              @include utils.bgCenterForDC('diamond/diamond-icon.png', 11px, 11px);\n              margin-left: 1px;\n              top: -1px;\n            }\n            font-size: 11px;\n            line-height: 11px;\n            margin-left: 1px;\n          }\n        }\n      }\n    }\n  }\n}\n/* ssv */\n.diamond-part-wrapper.ssv{\n  .label-wrap {\n    .label{\n      text-decoration: none;\n      @include utils.flexCenter;\n      i{\n        @include utils.bgCenterForSSV('diamond/diamond.png', 20px, 20px);\n        display: inline-block;\n        margin-left: 6px;\n      }\n    }\n\n    .charge-construction {\n      color: #FF813C;\n      line-height: 1;\n      font-size: 18px;\n\n      i {\n        @include utils.bgCenterForSSV('pc/charge-construction-flag.png', 22px, 22px);\n      }\n    }\n  }\n  .diamond-list-wrapper{\n    .diamond-item{\n      @include utils.bgCenterForSSV('diamond/diamond-bg.png', 216px, 130px);\n      background-color: transparent;\n\n      .top{\n        height: 87px;\n        position: relative;\n        .image{\n          width: 104px;\n          height: 70px;\n          top: 28px;\n\n          @for $i from 0 to 9 {\n            &.image_#{$i} {\n              background-image: url(~@/assets/ss/diamond/ss-diamond-new-0#{$i}.png);\n            }\n          }\n        }\n        .coin-num {\n          //height: 29px;\n          //font-family: SourceHanSansCN, SourceHanSansCN;\n          //font-weight: bold;\n          //font-size: 20px;\n          //color: #9399B7;\n          //line-height: 1;\n          //text-shadow: none;\n          //width: 100%;\n          //position: absolute;\n          //bottom: 0;\n          //@include utils.flexCenter;\n          //flex-direction: row-reverse;\n          height: 28px;\n          font-size: 20px;\n          margin-top: 4px;\n          color: #FFFFFF;\n\n          i{\n            @include utils.bgCenterForSSV('diamond/diamond.png', 20px, 20px);\n            margin-left: 5px;\n          }\n        }\n        .diamond-input-wrapper{\n          bottom: 10px;\n          color: white;\n          .basic-num{\n            color: white;\n          }\n          i {\n            margin: 2px 8px 1px 2px;\n            @include utils.bgCenterForSSV('diamond/diamond.png', 16px, 16px);\n          }\n          input{\n            border-radius: 2px;\n            border: 1px solid #CFCFCF;\n            height: 28px;\n            color: #3D3D3D;\n            background: rgba(239, 239, 239, 0.3);\n            margin-left: 7px;\n          }\n        }\n      }\n\n      .bottom{\n        .now{\n          color: #000000;\n        }\n        .origin {\n          color: #7D561E;\n        }\n      }\n\n      .common-bonus{\n        @include utils.bgCenterForDC('diamond/bonus-bg.png', 75px, 75px);\n\n        .discount{\n          top: 21px;\n          left: 21px;\n        }\n        .off{\n          top: 31px;\n          left: 31px;\n        }\n\n        .send{\n          top: 20px;\n          left: 20px;\n          max-width: 70%;\n        }\n        .num{\n          i{\n            @include utils.bgCenterForSSV('diamond/diamond.png', 15px, 15px);\n            margin-left: 2px;\n          }\n          top: 30px;\n          left: 30px;\n        }\n      }\n\n      &.diamond-item__active{\n        &:after {\n          @include utils.bgCenterForSSV('diamond/chosen-diamond-active.png', 216px, 130px);\n        }\n      }\n    }\n  }\n\n  @include utils.setPcContent{\n    .label-wrap {\n      .label{\n        @include utils.setPcContent{\n          justify-content: flex-end;\n          i{\n            @include utils.bgCenterForSSV('diamond/diamond.png', 23px, 23px);\n            margin-left: 6px;\n          }\n        }\n      }\n    }\n    .diamond-list-wrapper{\n      .diamond-item{\n        @include utils.bgCenterForSSV('diamond/pc/diamond-bg.png', 134px, 108px);\n\n        .top{\n          height: 69px;\n          position: relative;\n          .image{\n            width: 74px;\n            height: 50px;\n            top: 26px;\n            @for $i from 0 to 9 {\n              &.index-#{$i}{\n                background-image: url(~@/assets/ss/diamond/pc/ss-diamond-new-0#{$i}.png);\n              }\n            }\n          }\n          .coin-num {\n            height: 28px;\n            font-size: 18px;\n            line-height: 28px;\n            margin-top: 2px;\n            left: 0;\n            padding-left: 30px;\n\n            i{\n              @include utils.bgCenterForSSV( 'diamond/diamond.png', 18px, 18px);\n              margin-left: 1px;\n            }\n          }\n          .diamond-input-wrapper{\n            bottom: 2px;\n            //color: white;\n            //.basic-num{\n            //  color: white;\n            //}\n            font-size: 14px;\n            i {\n              margin: 0 3PX 0 0;\n              @include utils.bgCenterForSSV('diamond/diamond.png', 10px, 10px);\n            }\n            input{\n              border-radius: 2PX;\n              border: 1PX solid #CFCFCF;\n              height: 22PX;\n              margin-left: 2PX;\n              text-indent: 4PX;\n              font-size: 14PX;\n              width: 46PX;\n              background: #F2F1F1;\n              position: relative;\n              z-index: 10;\n            }\n\n            .tips{\n              display: inline-block;\n              height: 12PX;\n              width: 12PX;\n              margin-left: 1PX;\n              cursor: pointer;\n              position: relative;\n              z-index: 10;\n              @include utils.bgCenterForSSV('diamond/custom-diamond-tips-pop-btn.png', 12PX, 12PX);\n\n              .custom-num-range-tips{\n                width: 140PX;\n                min-height: 60PX;\n                background: white;\n                position: absolute;\n                left: 100%;\n                top: -16PX;\n                margin-left: 10PX;\n                border-radius: 5PX;\n\n                font-size: 14PX;\n                font-family: PingFangSC-Regular, PingFang SC;\n                font-weight: 400;\n                color: #383838;\n                line-height: 20PX;\n                text-shadow: 0PX 1PX 3PX rgba(0,0,0,0.5);\n                padding: 10px;\n                display: flex;\n                align-items: center;\n                justify-content: center;\n                box-shadow: 0px 1px 3px 0px rgba(0,0,0,0.5);\n\n                &:after{\n                  content: '';\n                  height: 0;\n                  width: 0;\n                  border-right: 5PX solid white;\n                  border-bottom: 5PX solid transparent;\n                  border-top: 5PX solid transparent;\n                  position: absolute;\n                  left: -4PX;\n                  top: 16PX;\n                }\n              }\n            }\n          }\n        }\n\n        .bottom{\n          height: 38px;\n\n          .origin {\n            font-size: 12px;\n            line-height: 1;\n          }\n\n          .now {\n            font-size: 18px;\n            line-height: 1.1;\n          }\n        }\n\n        .common-bonus{\n          @include utils.bgCenterForDC('diamond/bonus-bg.png', 53px, 53px);\n\n          .discount{\n            top: 14px;\n            left: 14px;\n          }\n          .off{\n            top: 21px;\n            left: 21px;\n          }\n\n          .send{\n            top: 14px;\n            left: 14px;\n            max-width: 70%;\n          }\n          .num{\n            i{\n              @include utils.bgCenterForSSV('diamond/diamond.png', 12px, 12px);\n              margin-left: 2px;\n            }\n            top: 20px;\n            left: 20px;\n          }\n        }\n\n        &.diamond-item__active{\n          &:after{\n            @include utils.bgCenterForSSV('diamond/chosen-diamond-active_pc.png', 134px, 108px);\n          }\n        }\n      }\n    }\n  }\n}\n.standard-diamond{\n  .label-wrap{\n    .label{\n      @include utils.flexCenter;\n      i{\n        top: auto;\n      }\n    }\n  }\n}\n/* ssv2 */\n.diamond-part-wrapper.ssv2{\n  @extend .standard-diamond;\n\n  .label-wrap {\n    .label{\n      i{\n        @include utils.bgCenterForSS('diamond/diamond.png', 32px, 32px);\n        display: inline-block;\n        margin-left: 6px;\n      }\n    }\n\n    .charge-construction {\n      color: #FF813C;\n      line-height: 1;\n      font-size: 18px;\n\n      i {\n        @include utils.bgCenterForSS('pc/charge-construction-flag.png', 22px, 22px);\n      }\n    }\n  }\n  .diamond-list-wrapper{\n    .diamond-item{\n      @include utils.bgCenterForSS('diamond/diamond-bg.png', 216px, 168px);\n      background-color: transparent;\n\n      .top{\n        height: 116px;\n        position: relative;\n        .image{\n          width: 104px;\n          height: 70px;\n          top: 45px;\n\n          @for $i from 0 to 9 {\n            &.image_#{$i} {\n              background-image: url(~@/assets/ss/diamond/ss-diamond-new-0#{$i}.png);\n              background-position: center center;\n              background-size: 100%;\n            }\n          }\n        }\n        .coin-num {\n          height: 28px;\n          font-size: 20px;\n          margin-top: 10px;\n          color: #FFFFFF;\n\n          i{\n            @include utils.bgCenterForSSV('diamond/diamond.png', 20px, 20px);\n            margin-left: 5px;\n          }\n        }\n        .diamond-input-wrapper{\n          bottom: 10px;\n          color: white;\n          .basic-num{\n            color: white;\n          }\n          i {\n            margin: 2px 8px 1px 2px;\n            @include utils.bgCenterForSSV('diamond/diamond.png', 16px, 16px);\n          }\n          input{\n            border-radius: 2px;\n            border: 1px solid #CFCFCF;\n            height: 28px;\n            color: #3D3D3D;\n            background: rgba(239, 239, 239, 0.3);\n            margin-left: 7px;\n          }\n        }\n      }\n\n      .bottom{\n        @include utils.flexCenter;\n        .now{\n          color: #000000;\n        }\n        .origin {\n          color: #7D561E;\n        }\n      }\n\n      .common-bonus{\n        @include utils.bgCenterForSS('diamond/bonus_rebate.png', 80px, 80px);\n        top: -8px;\n        left: -14px;\n        z-index: 2;\n\n        .discount{\n          transform: translate(-50%, -50%) rotate(-18deg);\n          top: 40px;\n          left: 46%;\n          text-shadow: 0px 0px 1px rgba(50,27,0,0.73);;\n          font-size: 23px;\n          font-weight: bold;\n          letter-spacing: -1px;\n        }\n        .off{\n          transform: translate(-50%, -50%) rotate(-18deg);\n          top: 57px;\n          left: 54%;\n          font-size: 14px;\n          font-weight: bold;\n        }\n\n        .send{\n          transform: translate(-50%, -50%) rotate(-20deg);\n          top: 37px;\n          left: 43%;\n          max-width: 70%;\n          font-size: 14px;\n          text-shadow: none;\n        }\n        .num{\n          i{\n            @include utils.bgCenterForSS('diamond/diamond.png', 16px, 16px);\n            margin-left: 2px;\n          }\n\n          transform: translate(-50%, -50%) rotate(-20deg);\n          top: 51px;\n          left: 52%;\n          font-size: 18px;\n        }\n      }\n\n      &.diamond-item__active{\n        &:after {\n          @include utils.bgCenterForSS('diamond/chosen-diamond-active.png', 216px, 168px);\n        }\n      }\n    }\n  }\n\n  @include utils.setPcContent{\n    .label-wrap {\n      .label{\n        @include utils.setPcContent{\n          justify-content: flex-end;\n          i{\n            @include utils.bgCenterForSS('diamond/diamond.png', 23px, 23px);\n            margin-left: 6px;\n          }\n        }\n      }\n    }\n    .diamond-list-wrapper{\n      .diamond-item{\n        @include utils.bgCenterForSSV('diamond/pc/diamond-bg.png', 134px, 108px);\n\n        .top{\n          height: 69px;\n          position: relative;\n          .image{\n            width: 74px;\n            height: 50px;\n            top: 26px;\n            @for $i from 0 to 9 {\n              &.index-#{$i}{\n                background-image: url(~@/assets/ss/diamond/pc/ss-diamond-new-0#{$i}.png);\n              }\n            }\n          }\n          .coin-num {\n            height: 28px;\n            font-size: 18px;\n            line-height: 28px;\n            margin-top: 2px;\n            left: 0;\n            padding-left: 30px;\n\n            i{\n              @include utils.bgCenterForSSV( 'diamond/diamond.png', 18px, 18px);\n              margin-left: 1px;\n            }\n          }\n          .diamond-input-wrapper{\n            bottom: 2px;\n            //color: white;\n            //.basic-num{\n            //  color: white;\n            //}\n            font-size: 14px;\n            i {\n              margin: 0 3PX 0 0;\n              @include utils.bgCenterForSSV('diamond/diamond.png', 10px, 10px);\n            }\n            input{\n              border-radius: 2PX;\n              border: 1PX solid #CFCFCF;\n              height: 22PX;\n              margin-left: 2PX;\n              text-indent: 4PX;\n              font-size: 14PX;\n              width: 46PX;\n              background: #F2F1F1;\n              position: relative;\n              z-index: 10;\n            }\n\n            .tips{\n              display: inline-block;\n              height: 12PX;\n              width: 12PX;\n              margin-left: 1PX;\n              cursor: pointer;\n              position: relative;\n              z-index: 10;\n              @include utils.bgCenterForSSV('diamond/custom-diamond-tips-pop-btn.png', 12PX, 12PX);\n\n              .custom-num-range-tips{\n                width: 140PX;\n                min-height: 60PX;\n                background: white;\n                position: absolute;\n                left: 100%;\n                top: -16PX;\n                margin-left: 10PX;\n                border-radius: 5PX;\n\n                font-size: 14PX;\n                font-family: PingFangSC-Regular, PingFang SC;\n                font-weight: 400;\n                color: #383838;\n                line-height: 20PX;\n                text-shadow: 0PX 1PX 3PX rgba(0,0,0,0.5);\n                padding: 10px;\n                display: flex;\n                align-items: center;\n                justify-content: center;\n                box-shadow: 0px 1px 3px 0px rgba(0,0,0,0.5);\n\n                &:after{\n                  content: '';\n                  height: 0;\n                  width: 0;\n                  border-right: 5PX solid white;\n                  border-bottom: 5PX solid transparent;\n                  border-top: 5PX solid transparent;\n                  position: absolute;\n                  left: -4PX;\n                  top: 16PX;\n                }\n              }\n            }\n          }\n        }\n\n        .bottom{\n          height: 38px;\n\n          .origin {\n            font-size: 12px;\n            line-height: 1;\n          }\n\n          .now {\n            font-size: 18px;\n            line-height: 1.1;\n          }\n        }\n\n        .common-bonus{\n          transform: scale(.7);\n          top: -8px;\n          left: -14px;\n          transform-origin: left top;\n        }\n\n        &.diamond-item__active{\n          &:after{\n            @include utils.bgCenterForSSV('diamond/chosen-diamond-active_pc.png', 134px, 108px);\n          }\n        }\n      }\n    }\n  }\n}\n</style>\n", "import mod from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./DiamondChooseKOA.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./DiamondChooseKOA.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./DiamondChooseKOA.vue?vue&type=template&id=83f0c5b0&scoped=true\"\nimport script from \"./DiamondChooseKOA.vue?vue&type=script&lang=js\"\nexport * from \"./DiamondChooseKOA.vue?vue&type=script&lang=js\"\nimport style0 from \"./DiamondChooseKOA.vue?vue&type=style&index=0&id=83f0c5b0&prod&scoped=true&lang=scss\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"83f0c5b0\",\n  null\n  \n)\n\nexport default component.exports", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('common-part',{staticClass:\"entrance-part\"},[_c('div',{staticClass:\"entrance-wrapper\",on:{\"click\":_vm.go}},[(_vm.$store.state.formdata.showDoubleExperience)?_c('div',{staticClass:\"toggle-coupon-act-level-up\"}):_c('div',{staticClass:\"award\",class:{rotate: _vm.rotate}}),_vm._v(\" \"+_vm._s(_vm.$t('boon-page-title'))+\" \"),_c('i')])])\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\n  <common-part class=\"entrance-part\">\n    <div class=\"entrance-wrapper\" @click=\"go\">\n      <div v-if=\"$store.state.formdata.showDoubleExperience\" class=\"toggle-coupon-act-level-up\"></div>\n      <div v-else class=\"award\" :class=\"{rotate: rotate}\"></div>\n      {{ $t('boon-page-title') }}\n      <i></i>\n    </div>\n  </common-part>\n</template>\n\n<script>\nimport CommonPart from '@/components/common/CommonPart.vue'\nimport { mapState } from 'vuex'\nimport { ameDoByGet, ameHoldByGet, getAmeDo } from '@/server'\nimport { getPWADisplayMode } from '@/utils/utils'\nconst { projectId, pwaOpenAction, loginAction, getLoginReward, getPwaReward } = window.$gcbk('apiParams.boonAme', {})\n\nexport default {\n  name: 'EntranceOfBoon',\n  components: { CommonPart },\n  data () {\n    return {\n      clicked: false,\n      gain: false // 获取两个\n    }\n  },\n  computed: {\n    ...mapState('userinfo', ['isLogin']),\n    rotate () {\n      return !this.isLogin || (!this.clicked && !this.gain)\n    }\n  },\n  methods: {\n    getStatus () {\n      const params = {\n        p0: 'web',\n        p1: projectId,\n        p2: `${getLoginReward},${getPwaReward}`\n      }\n      this.$loading.show()\n      ameHoldByGet(params)\n        .then(res => {\n          const { data = [], code } = res\n          if (code === 0) {\n            if (data.length === 2) this.gain = true\n\n            // gotDailyReward\n            if (this.$store.state.gameinfo.gameCode === 'KOA') {\n              this.gain = this.gain && this.$store.state.formdata.gotDailyReward\n            }\n          }\n        })\n        .finally(() => this.$loading.hide())\n      // this.topupApi(true)\n    },\n    go () {\n      this.clicked = true\n      this.$root.$emit('showPop', 'BoonPop')\n    },\n\n    // 登录成功 初始化福利中心\n    informServer (taskId) {\n      const ameParams = { p0: 'web', p1: projectId }\n      ameDoByGet({ p2: taskId, ...ameParams })\n        .then(res => {\n          const { code } = res\n          if (code === 0) this.getReward(taskId)\n        })\n    },\n    getReward (taskId) {\n      const params = { p2: +taskId + 1 }\n      const ameParams = { p0: 'web', p1: projectId }\n      this.$loading.show()\n      getAmeDo({ ...params, ...ameParams })\n        .finally(() => this.$loading.hide())\n    },\n\n    checkAddBtn(){\n      // pwa安装按钮样式调整\n      this.setInterval = setInterval(() => {\n        const btn =document.querySelector('.add-to-main-screen__mobile')\n        const body = document.querySelector('.mobile-body-wrapper')\n        if (btn) {\n          btn.style.zoom = 0.75\n          body.style.paddingTop = '1.3rem'\n        } else {\n          body.style.paddingTop = '0.53333rem'\n        }\n      }, 1000)\n    }\n  },\n  created () {\n    this.$root.$on('loginEnd', (state) => {\n      // 福利中心\n      if (getPWADisplayMode === 'standalone') this.informServer(pwaOpenAction)\n      else {\n        window.$event = this.$root\n        this.$root.$on('installSuccessful', () => this.informServer(pwaOpenAction))\n      }\n      this.informServer(loginAction)\n\n      this.getStatus()\n    })\n\n    if (this.$store.state.isMobile) this.checkAddBtn()\n  },\n  beforeDestroy() {\n    if (this.setInterval) clearInterval(this.setInterval)\n  }\n}\n</script>\n\n<style lang=\"scss\">\n@use \"~@/utils/utils.scss\" as utils;\n\n@keyframes shake {\n  70%, 80% {\n    transform: rotate(12deg);\n  }\n\n  75% {\n    transform: rotate(-12deg);\n  }\n\n  65%, 85% {\n    transform: rotate(0);\n  }\n}\n\n.entrance-part {\n  margin-top: 20px !important;\n\n  & + .common-part-wrapper {\n    margin-top: 12px;\n  }\n\n  .entrance-wrapper {\n    font-size: 20px;\n    font-family: PingFangSC-Semibold, PingFang SC;\n    font-weight: 600;\n    color: #FFEA99;\n    line-height: 34px;\n    text-indent: 47px;\n    background: linear-gradient(to bottom, #F6812F, #EE2601);\n    border-top-right-radius: 17px;\n    border-bottom-right-radius: 17px;\n    padding-right: 34px;\n    display: inline-block;\n\n    position: relative;\n    margin-left: 20px;\n    cursor: pointer;\n    // display: flex;\n    // align-items: center;\n    // @include utils.bgCenter('koa/koa-boon.png', auto, 63px);\n\n    .award {\n      // @include utils.bgCenter('koa/boon-entrance-award.png', 159px, 89px);\n      @include utils.bgCenter('koa/boon/boon-entrance-award.png', 91px, 50px);\n      position: absolute;\n      left: -44px;\n      bottom: -4px;\n    }\n\n    //.award-turntable {\n    //  @include utils.bgCenter('koa/turntable/back.png', 47px, 54px);\n    //  position: absolute;\n    //  display: flex;\n    //  justify-content: center;\n    //  left: -25px;\n    //  bottom: 0px;\n    //\n    //  .award-turntable-turn {\n    //    @include utils.bgCenter('koa/turntable/turn.png', 46px, 46px);\n    //    position: absolute;\n    //    animation: turntable-rotate 4s linear infinite;\n    //  }\n    //\n    //  .award-turntable-pointer {\n    //    @include utils.bgCenter('koa/turntable/pointer.png', 46px, 46px);\n    //    position: absolute;\n    //  }\n    //\n    //  @keyframes turntable-rotate {\n    //    from {\n    //      transform: rotate(0deg);\n    //    }\n    //    to {\n    //      transform: rotate(360deg);\n    //    }\n    //  }\n    //}\n\n    i {\n      @include utils.bgCenter('koa/boon/boon-entrance-arrow.png', 8px, 8px);\n      display: inline-block;\n      position: absolute;\n      right: 12px;\n      top: 50%;\n      transform: translateY(-50%);\n    }\n\n    .rotate {\n      animation: shake 2s linear infinite alternate-reverse;\n    }\n\n    .toggle-coupon-act-level-up{\n      @include utils.bgCenter('koa/activity/act-toggle-coupon-level.png', 66px, 82px);\n      position: absolute;\n      bottom: -6px;\n      left: -17px;\n    }\n  }\n\n  // pc\n  @include utils.setPcContent{\n    margin-top: 20px!important;\n\n    & + .common-part-wrapper {\n      margin-top: 18px;\n    }\n  }\n  @include utils.setMobileContent{\n    .label{\n      display: none;\n    }\n  }\n}\n</style>\n", "import mod from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./EntranceOfBoon.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./EntranceOfBoon.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./EntranceOfBoon.vue?vue&type=template&id=5f9ee2ec\"\nimport script from \"./EntranceOfBoon.vue?vue&type=script&lang=js\"\nexport * from \"./EntranceOfBoon.vue?vue&type=script&lang=js\"\nimport style0 from \"./EntranceOfBoon.vue?vue&type=style&index=0&id=5f9ee2ec&prod&lang=scss\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\nexport default component.exports", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('common-part',{staticClass:\"checkout-counter-sdk-b\",class:[_vm.$gameName],attrs:{\"labelFont\":_vm.$t('totalPrice'),\"id\":\"checkout-counter-sdk-b\"}},[(_vm.expandMode)?_c('div',{staticClass:\"expand\",class:[_vm.$gameName]},[_c('div',{staticClass:\"price-wrapper\"},[_c('span',{staticClass:\"sub-total\"},[_vm._v(_vm._s(_vm.$t('tax-sub-total'))+\":  \")]),_c('span',{staticClass:\"now-price\",class:{'is-ar-zone': _vm.isArZone}},[_vm._v(_vm._s(_vm.FinalPriceState.rawNowPrice))]),(_vm.FinalPriceState.rawOriginPrice)?_c('span',{class:['origin-price', {'is-ar-zone': _vm.isArZone}]},[_vm._v(_vm._s(_vm.FinalPriceState.rawOriginPrice))]):_vm._e(),(_vm.FinalPriceState.offCountTips)?_c('div',{staticClass:\"off-count-tips\",domProps:{\"innerHTML\":_vm._s(_vm.FinalPriceState.offCountTips)}}):_vm._e()]),(_vm.taxCost)?_c('div',{staticClass:\"tax-wrapper\"},[_vm._v(_vm._s(_vm.$t('tax-txt'))+\":  \"+_vm._s(_vm.taxCost)+\" \"+_vm._s(_vm.currencyUnit))]):_vm._e(),(_vm.extraCost)?_c('div',{staticClass:\"tax-wrapper\"},[_vm._v(_vm._s(_vm.$t('extra-txt'))+\":  \"+_vm._s(_vm.extraCost)+\" \"+_vm._s(_vm.currencyUnit))]):_vm._e(),_c('div',{staticClass:\"final-price\"},[_vm._v(_vm._s(_vm.FinalPriceState.finalNowPrice))]),_c('span',{staticClass:\"rate\",class:{active: _vm.expandMode},on:{\"click\":function($event){_vm.expandMode = !_vm.expandMode}}},[_vm._v(\"+ \"+_vm._s(_vm.$t('tax-txt'))),_c('i')])]):_c('div',{staticClass:\"normal\",class:[_vm.$gameName]},[_c('div',{staticClass:\"price-wrapper\"},[_c('span',{staticClass:\"now-price\",class:{'is-ar-zone': _vm.isArZone}},[_vm._v(_vm._s(_vm.FinalPriceState.finalNowPrice || '-'))]),(_vm.FinalPriceState.finalOriginPrice)?_c('span',{staticClass:\"origin-price\",class:{'is-ar-zone': _vm.isArZone}},[_vm._v(_vm._s(_vm.FinalPriceState.finalOriginPrice))]):_vm._e(),(_vm.FinalPriceState.offCountTips)?_c('div',{staticClass:\"off-count-tips\",domProps:{\"innerHTML\":_vm._s(_vm.FinalPriceState.offCountTips)}}):_vm._e()]),(_vm.showTaxBtn)?_c('span',{staticClass:\"rate\",class:{active: _vm.expandMode},on:{\"click\":function($event){_vm.expandMode = !_vm.expandMode}}},[_vm._v(\"+ \"+_vm._s(_vm.$t('tax-txt'))),_c('i')]):_vm._e()]),_c('div',{staticClass:\"click-btn\",class:[{disable: _vm.requestLoading || _vm.$store.getters['riskPolicy/forbiddenAccess']}],on:{\"click\":function($event){return _vm.$emit('purchaseGoods')}}},[_vm._v(\" \"+_vm._s(_vm.$t('shop_now'))+\" \"),(_vm.vip.isNewUser)?_c('i'):_vm._e()])])\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<script>\nimport checkoutCounter from '@/components/CheckoutCounter.js'\n\nexport default {\n  name: 'CheckoutCounterSDK',\n  props: ['requestLoading'],\n  mixins: [checkoutCounter]\n}\n</script>\n\n<template>\n  <common-part class=\"checkout-counter-sdk-b\" :labelFont=\"$t('totalPrice')\" :class=\"[$gameName]\" id=\"checkout-counter-sdk-b\">\n    <div v-if=\"expandMode\" class=\"expand\" :class=\"[$gameName]\">\n      <!--sub total-->\n      <div class=\"price-wrapper\">\n        <span class=\"sub-total\">{{ $t('tax-sub-total') }}:&nbsp;&nbsp;</span>\n        <span class=\"now-price\" :class=\"{'is-ar-zone': isArZone}\">{{ FinalPriceState.rawNowPrice }}</span>\n        <span v-if=\"FinalPriceState.rawOriginPrice\" :class=\"['origin-price', {'is-ar-zone': isArZone}]\">{{ FinalPriceState.rawOriginPrice }}</span>\n        <div v-if=\"FinalPriceState.offCountTips\" class=\"off-count-tips\" v-html=\"FinalPriceState.offCountTips\"></div>\n      </div>\n      <!--Tax-->\n      <div class=\"tax-wrapper\" v-if=\"taxCost\">{{ $t('tax-txt') }}:&nbsp;&nbsp;{{ taxCost }} {{ currencyUnit }}</div>\n      <!--额外费用-->\n      <div class=\"tax-wrapper\" v-if=\"extraCost\">{{ $t('extra-txt') }}:&nbsp;&nbsp;{{ extraCost }} {{ currencyUnit }}</div>\n      <!--最终价格-->\n      <div class=\"final-price\">{{ FinalPriceState.finalNowPrice }}</div>\n      <span class=\"rate\" @click=\"expandMode = !expandMode\" :class=\"{active: expandMode}\">+ {{ $t('tax-txt') }}<i></i></span>\n    </div>\n    <div v-else class=\"normal\" :class=\"[$gameName]\">\n      <div class=\"price-wrapper\">\n        <span class=\"now-price\" :class=\"{'is-ar-zone': isArZone}\">{{ FinalPriceState.finalNowPrice || '-' }}</span>\n        <span v-if=\"FinalPriceState.finalOriginPrice\" class=\"origin-price\" :class=\"{'is-ar-zone': isArZone}\">{{ FinalPriceState.finalOriginPrice }}</span>\n        <div v-if=\"FinalPriceState.offCountTips\" class=\"off-count-tips\" v-html=\"FinalPriceState.offCountTips\"></div>\n      </div>\n      <span class=\"rate\" v-if=\"showTaxBtn\" @click=\"expandMode = !expandMode\" :class=\"{active: expandMode}\">+ {{ $t('tax-txt') }}<i></i></span>\n    </div>\n    <div class=\"click-btn\" :class=\"[{disable: requestLoading || $store.getters['riskPolicy/forbiddenAccess']}]\" @click=\"$emit('purchaseGoods')\">\n      {{ $t('shop_now') }}\n      <i v-if=\"vip.isNewUser\"></i>\n    </div>\n  </common-part>\n</template>\n\n<style scoped lang=\"scss\">\n@use \"~@/utils/utils.scss\" as utils;\n.normal{\n  .price-wrapper {\n    font-weight: bold;\n    color: #FFFFFF;\n    line-height: 38px;\n    @include utils.flexCenter;\n    .is-ar-zone {\n      display: inline-block;\n    }\n    .now-price{\n      font-size: 28PX;\n    }\n    .origin-price {\n      text-decoration: line-through;\n      font-weight: bold;\n      color: #989DA6;\n      font-size: 18PX;\n      margin-left: 10PX;\n    }\n    .off-count-tips {\n      display: inline-block;\n      background: #FEB522;\n      position: relative;\n      font-weight: bold;\n      color: #735100;\n      font-size: 10PX;\n      top: -19PX;\n      left: 5PX;\n      padding: 0 6PX;\n      border-radius: 4PX;\n      height: 18PX;\n      line-height: 18PX;\n\n      ::v-deep{\n        .diamond-icon {\n          @include utils.bgCenterForKoaIcon('koa/diamond/diamond.png', 11px, 9px);\n          display: inline-block;\n          margin-left: 1px;\n          position: relative;\n        }\n      }\n    }\n  }\n  .rate{\n    font-size: 18PX;\n    font-weight: 400;\n    color: #FEB522;\n    line-height: 1;\n    cursor: pointer;\n    margin-top: 8px;\n    i{\n      @include utils.bgCenter('common/icon/tax-arrow.png', 20PX, 20PX);\n      display: inline-block;\n      margin-left: 4PX;\n      transition: all .3s;\n    }\n\n    &.active i{\n      transform: rotate(180deg);\n    }\n  }\n  @include utils.flexCenter;\n  flex-direction: column;\n  align-items: flex-start;\n}\n.expand{\n  @extend .normal;\n  .price-wrapper{\n    line-height: 30PX;\n\n    .sub-total{\n      font-size: 18PX;\n      //font-family: PingFangSC-Regular, PingFang SC;\n      font-weight: 400;\n      color: #FFFFFF;\n      line-height: 25PX;\n      white-space: nowrap;\n      overflow: hidden;\n      text-overflow: ellipsis;\n    }\n    .now-price{\n      font-weight: normal;\n      font-size: 18px;\n    }\n    .origin-price{\n      font-size: 15px;\n    }\n    .off-count-tips {\n      top: -10px;\n    }\n  }\n  .tax-wrapper{\n    margin-top: 2PX;\n    font-size: 18PX;\n    font-weight: 400;\n    color: #FFFFFF;\n    line-height: 30PX;\n  }\n  .final-price{\n    font-size: 28PX;\n    font-weight: 600;\n    color: #FFFFFF;\n    line-height: 38px;\n    @include utils.flexCenter;\n  }\n}\n.click-btn {\n  margin-top: 24px;\n  width: 426px;\n  text-align: center;\n  font-size: 24PX;\n  height: 63PX;\n  line-height: 63PX;\n  //font-family: PingFangSC-Semibold, PingFang SC;\n  font-weight: 600;\n  color: #FFFFFF;\n  //background: #FF5E0F;\n  display: inline-block;\n  cursor: pointer;\n\n  &.disable {\n    opacity: .4;\n    cursor: not-allowed;\n  }\n}\n\n.dc{\n  .click-btn{\n    color: #393A3E;\n    background: rgba(255, 210, 53);\n    @extend .dc-btn-decoration;\n  }\n  .price-wrapper{\n    .off-count-tips {\n      background: #FFE14D;\n      color: #393A3E;\n\n      ::v-deep{\n        .diamond-icon{\n          @include utils.bgCenterForDC('diamond/diamond-icon.png', 12PX, 12PX);\n        }\n      }\n    }\n  }\n}\n\n/* 移动端不展示 */\n@include utils.setMobileContent{\n  .checkout-counter-sdk-b{\n    display: none;\n  }\n}\n</style>\n", "import mod from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./CheckoutCounterSDK.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./CheckoutCounterSDK.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./CheckoutCounterSDK.vue?vue&type=template&id=40652ba8&scoped=true\"\nimport script from \"./CheckoutCounterSDK.vue?vue&type=script&lang=js\"\nexport * from \"./CheckoutCounterSDK.vue?vue&type=script&lang=js\"\nimport style0 from \"./CheckoutCounterSDK.vue?vue&type=style&index=0&id=40652ba8&prod&scoped=true&lang=scss\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"40652ba8\",\n  null\n  \n)\n\nexport default component.exports", "<template>\n  <div :class=\"['shopping-wrapper', $gameName, { sdk: IS_CHECKOUT_SDK }]\" id=\"page-pay-wrapper\">\n    <template v-if=\"isPc && !IS_CHECKOUT_SDK\">\n      <section class=\"pc-content-wrapper\">\n        <add-screen-btn></add-screen-btn>\n        <!-- iOS safari... -->\n        <add-ios-safari-btn></add-ios-safari-btn>\n        <div class=\"page-title\">{{ $vt('pageTitle') }} <span>{{ $t('mobile_available') }} <i></i></span></div>\n        <div class=\"content-center content-center__main\">\n          <div class=\"left-part\">\n            <div class=\"logo\"></div>\n            <div class=\"name\"></div>\n            <p class=\"description\">\n              <span>{{ $vt('whatIsDiamondTitle') }}</span>\n              <span v-if=\"$store.state.functionSwitch.showPcDiscountTips\">{{ $vt('discount95Tips') }}</span>\n            </p>\n            <div class=\"charge-construction\" @click=\"$root.$emit('showPop', 'ChargeConstruction')\">\n              <i></i>{{ $t('construction_title') }}\n            </div>\n          </div>\n          <div class=\"right-part\">\n            <login-module></login-module>\n            <entrance-of-boon v-if=\"boon\"></entrance-of-boon>\n            <coupon-choose></coupon-choose>\n            <diamond-choose-k-o-a></diamond-choose-k-o-a>\n            <channel-choose></channel-choose>\n            <checkout-counter-cn v-if=\"$store.state.gameinfo.isCn\"></checkout-counter-cn>\n            <checkout-counter v-else></checkout-counter>\n            <div class=\"shop-btn\">\n              <span class=\"click-btn\" :class=\"[{disable: requestLoading || $store.getters['riskPolicy/forbiddenAccess']}]\" @click=\"judgeRisk()\">{{ $t('shop_now') }} <i v-if=\"vip.isNewUser || !isLogin\"></i></span>\n            </div>\n\n            <common-part v-if=\"$store.state.country === 'DE' && isLogin\">\n              <private-permission></private-permission>\n            </common-part>\n          </div>\n        </div>\n      </section>\n\n      <common-footer-cn v-if=\"$store.state.gameinfo.isCn\"></common-footer-cn>\n      <CommonFooterPuzala v-if=\"$store.state.gameinfo.mainBody\"></CommonFooterPuzala>\n      <common-footer v-else></common-footer>\n    </template>\n    <template v-if=\"isMobile && !IS_CHECKOUT_SDK\">\n      <div class=\"mobile-body-wrapper\">\n        <add-screen-btn></add-screen-btn>\n        <!-- iOS safari... -->\n        <add-ios-safari-btn></add-ios-safari-btn>\n        <login-module></login-module>\n        <entrance-of-boon v-if=\"boon\"></entrance-of-boon>\n        <coupon-choose></coupon-choose>\n        <diamond-choose-k-o-a></diamond-choose-k-o-a>\n        <channel-choose></channel-choose>\n        <common-part v-if=\"$store.state.country === 'DE' && isLogin\">\n          <private-permission></private-permission>\n        </common-part>\n        <refund-policy v-else-if=\"showMobilePolicy\"></refund-policy>\n        <common-footer-cn v-if=\"$store.state.gameinfo.isCn\"></common-footer-cn>\n      </div>\n      <checkout-footer @purchaseGoods=\"judgeRisk()\" :request-loading=\"requestLoading\"></checkout-footer>\n    </template>\n\n    <!--  通用SDK接入到商城，共用一套代码  -->\n    <template v-if=\"IS_CHECKOUT_SDK\">\n      <div class=\"sdk-body-wrapper\">\n        <direct-gift-package></direct-gift-package>\n        <coupon-choose></coupon-choose>\n        <channel-choose></channel-choose>\n        <common-part v-if=\"$store.state.country === 'DE' && isLogin\">\n          <private-permission></private-permission>\n        </common-part>\n        <checkout-counter-s-d-k @purchaseGoods=\"judgeRisk()\" :request-loading=\"requestLoading\"></checkout-counter-s-d-k>\n        <!-- login 放最下面，等上面组件初始化完成 -->\n        <login-module v-show=\"false\"></login-module>\n      </div>\n      <checkout-footer @purchaseGoods=\"judgeRisk()\" :request-loading=\"requestLoading\"></checkout-footer>\n      <common-footer></common-footer>\n    </template>\n  </div>\n</template>\n\n<script>\nimport CommonFooter from '@/components/CommonFooter'\nimport LoginModule from '@/components/LoginModule'\nimport ChannelChoose from '@/components/ChannelChoose'\nimport CouponChoose from '@/components/coupon/CouponChoose'\nimport CheckoutCounter from '@/components/CheckoutCounterTax'\nimport CheckoutCounterCn from '@/components/CheckoutCounterCN'\nimport { ameDoByGet } from '@/server'\nimport CheckoutFooter from '@/components/mobile/CheckoutFooterTax'\nimport AddScreenBtn from '@/components/AddScreenBtn'\nimport AddIosSafariBtn from '@/components/AddIosSafariBtn'\nimport DiamondChooseKOA from '@/components/DiamondChooseKOA'\nimport EntranceOfBoon from '@/components/EntranceOfBoon'\nimport CommonPart from '@/components/common/CommonPart.vue'\nimport DirectGiftPackage from '@/components/product/DirectGiftPackage'\nimport CheckoutCounterSDK from '@/components/CheckoutCounterSDK.vue'\n\nimport PayMixin from '@/views/PayMixin'\n\nexport default {\n  name: 'Pay',\n  components: {\n    CheckoutCounterSDK,\n    CommonPart,\n    RefundPolicy: () => import(/* webpackChunkName: 'chunk-functions' */'@/components/mobile/RefundPolicy'),\n    EntranceOfBoon,\n    DiamondChooseKOA,\n    AddScreenBtn,\n    AddIosSafariBtn,\n    CheckoutFooter,\n    CheckoutCounter,\n    CouponChoose,\n    ChannelChoose,\n    LoginModule,\n    CommonFooter,\n    CheckoutCounterCn,\n    CommonFooterCn: () => import(/* webpackChunkName: 'chunk-functions' */'@/components/game/koa/CommonFooter.vue'),\n    CommonFooterPuzala: () => import(/* webpackChunkName: 'chunk-functions' */'@/components/common/CommonFooterPuzala.vue'),\n    PrivatePermission: () => import(/* webpackChunkName: 'chunk-functions' */'@/components/privatePermission.vue'),\n    DirectGiftPackage\n  },\n  mixins: [PayMixin],\n  methods: {\n    showDiamondPop () {\n      this.$root.$on('showWhatIsDiamondPop', () => {\n        const gallery = this.$imageLoader('whatsDiamond', [])\n\n        // 创建img标签加载图片\n        const img = new Image()\n        img.src = gallery[0].imageUrl\n        this.$loading.show()\n        // 监听图片加载完成事件\n        img.onload = () => {\n          this.$root.$emit('showPop', 'WhatIsDiamond')\n          this.$loading.hide()\n        }\n        img.onerror = () => {\n          this.$loading.hide()\n        }\n      })\n    },\n    initShowDoubleExperience () {\n      if (this.IS_CHECKOUT_SDK) return null\n      ameDoByGet({\n        p0: 'web',\n        p1: 11,\n        p2: 2195,\n        p3: 'api'\n      })\n        .then(res => {\n          const { code, data } = res\n          if (code === 0) {\n            this.$store.commit('formdata/switchDoubleExperience', data.double_flage || false)\n          }\n        })\n    }\n  },\n  created () {\n    this.isKOA && this.initShowDoubleExperience()\n    this.showDiamondPop()\n  }\n}\n</script>\n\n<style scoped lang=\"scss\">\n@use \"~@/utils/utils.scss\" as utils;\n.shopping-wrapper {\n  width: 100%;\n  box-sizing: border-box;\n  margin: 0 auto;\n  text-align: left;\n\n  .content-center {\n    margin: 0 auto;\n    @media screen and (min-width: 940px) {\n      width: 940PX;\n\n      .left-part {\n        display: none;\n      }\n    }\n\n    @media screen and (min-width: 1200px) {\n      width: 1200PX;\n      .left-part {\n        display: block;\n      }\n    }\n  }\n\n  .page-title {\n    font-family: PingFangSC-Semibold, PingFang SC;\n    font-weight: 600;\n    color: #FFFFFF;\n    @include utils.setPropByBp(\n      $p: (font-size: 30px, line-height: 30px, height: 55px, padding-left: 33px, margin-top: 18px),\n    );\n\n    position: relative;\n    span{\n      vertical-align: top;\n      height: 24PX;\n      line-height: 24PX;\n      background: #FDDB70;\n      font-size: 14PX;\n      font-family: PingFangSC-Regular, PingFang SC;\n      font-weight: 400;\n      color: #633B00;\n      padding-right: 8PX;\n      padding-left: 24PX;\n      display: inline-block;\n      margin: 15PX 10PX 0;\n      transform: scale(.95) translateY(-50%);\n      transform-origin: left top;\n      position: relative;\n\n      i{\n        @include utils.bgCenter('koa/mobile_yingdao.png', 18PX, 18PX);\n        display: inline-block;\n        position: absolute;\n        left: 6PX;\n        top: 50%;\n        transform: translateY(-50%);\n      }\n    }\n  }\n\n  .content-center__main {\n    margin-top: 7px;\n    display: flex;\n    justify-content: center;\n\n    /* left-part所有的都用PX */\n    .left-part {\n      width: 260PX;\n      background-color: rgba(255, 255, 255, 0.07);\n      overflow: hidden;\n      position: relative;\n      flex-shrink: 0;\n      max-height: 80vh;\n\n      .logo {\n        //@include utils.bgCenterForSS('ss-logo.png', 142PX, 142PX);\n        margin: 39PX auto 0;\n      }\n\n      .name {\n        //@include utils.bgCenterForSS('ss-name.png', 190PX, 87PX);\n        margin: 36PX auto 0;\n      }\n\n      .description {\n        font-size: 16PX;\n        font-family: PingFangSC-Regular, PingFang SC;\n        font-weight: 400;\n        color: #919090;\n        line-height: 22PX;\n        width: 206PX;\n        margin: 30PX auto 0;\n\n        span{\n          display: block;\n          //text-indent: 30PX;\n          &:nth-of-type(n+2){\n            margin-top: 6PX;\n          }\n        }\n      }\n\n      .charge-construction {\n        font-size: 18PX;\n        font-family: PingFangSC-Regular, PingFang SC;\n        font-weight: 400;\n        color: #FF813C;\n        line-height: 25PX;\n        letter-spacing: 1PX;\n        position: absolute;\n        bottom: 50PX;\n        left: 50%;\n        transform: translateX(-50%);\n        margin: 0 auto;\n        cursor: pointer;\n        text-decoration: underline;\n        width: 90%;\n        text-align: center;\n\n        i {\n          display: inline-block;\n          margin: 0 3PX;\n          cursor: pointer;\n          //@include utils.bgCenter('charge-construction-flag.png', 19PX, 19PX);\n          flex-shrink: 0;\n          position: relative;\n          top: 3PX;\n        }\n      }\n    }\n\n    .right-part {\n      width: 0;\n      flex-grow: 1;\n      padding-bottom: 30PX;\n      .shop-btn {\n        width: 100%;\n        text-align: center;\n        margin-top: 15PX;\n        overflow: hidden;\n\n        span {\n          font-size: 30PX;\n          height: 63PX;\n          line-height: 63PX;\n          font-family: PingFangSC-Semibold, PingFang SC;\n          font-weight: 600;\n          color: #FFFFFF;\n          background: #FF5E0F;\n          border-radius: 8PX;\n          display: inline-block;\n          margin: 0 auto;\n          padding: 0 61PX;\n          cursor: pointer;\n\n          &.disable {\n            opacity: .4;\n            cursor: not-allowed;\n          }\n        }\n      }\n    }\n  }\n}\n@include utils.setPcContent{\n  .shopping-wrapper{\n    .pc-content-wrapper{\n      padding-top: 18PX;\n      min-height: calc(100vh - 40PX);\n    }\n\n    .content-center{\n      min-height: 500PX;\n    }\n  }\n}\n@include utils.setMobileContent{\n  .shopping-wrapper{\n    display: flex;\n    flex-direction: column;\n    height: 100%;\n\n    .mobile-body-wrapper{\n      overflow-y: auto;\n      padding-top: 20px;\n      flex-grow: 1;\n      padding-bottom: 100px;\n    }\n  }\n}\n\n/* 各游戏 */\n.shopping-wrapper.koa{\n  .content-center__main{\n    .left-part{\n      .logo {\n        @include utils.bgCenter('koa/login/koa-logo.png', 140PX, 140PX);\n        margin: 39PX auto 0;\n      }\n\n      .name {\n        @include utils.bgCenter('koa/login/koa-name.png', 190PX, 18PX);\n        margin: 29PX auto 0;\n      }\n\n      .charge-construction{\n        color: #FEB522;\n        i {\n          @include utils.bgCenter('koa/charge-construction-flag.png', 19PX, 19PX);\n        }\n      }\n    }\n    .right-part{\n      .shop-btn{\n        overflow: visible;\n        span{\n          background: linear-gradient(180deg, #F6E190 0%, #CBA455 100%);\n          font-size: 24PX;\n          font-family: PingFangSC-Semibold, PingFang SC;\n          font-weight: 600;\n          color: #633B00;\n          border-radius: 0;\n          position: relative;\n\n          i{\n            position: absolute;\n            right: 0;\n            top: 0;\n            @include utils.bgCenter('koa/pay-btn-gift.png', 44PX, 46PX);\n            display: inline-block;\n            transform: translate(50%, -50%);\n          }\n        }\n      }\n    }\n  }\n}\n.shopping-wrapper.aof{\n  .content-center__main {\n    .left-part {\n      min-height: 60vh;\n      .logo {\n        @include utils.bgCenter('koa/aof/aof-logo.png', 140px, 140px);\n        margin: 39px auto 0;\n      }\n\n      .name {\n        @include utils.bgCenter('koa/aof/aof-name.png', 216PX, 20px);\n        background-size: contain;\n        margin: 21PX auto 0;\n      }\n\n      .charge-construction{\n        color: #FEB522;\n        i {\n          @include utils.bgCenter('koa/charge-construction-flag.png', 19PX, 19PX);\n        }\n      }\n    }\n\n    .right-part{\n      .shop-btn{\n        overflow: visible;\n        span{\n          background: linear-gradient(180deg, #F6E190 0%, #CBA455 100%);\n          font-size: 24PX;\n          font-family: PingFangSC-Semibold, PingFang SC;\n          font-weight: 600;\n          color: #633B00;\n          border-radius: 0;\n          position: relative;\n\n          i{\n            position: absolute;\n            right: 0;\n            top: 0;\n            @include utils.bgCenter('koa/pay-btn-gift.png', 44PX, 46PX);\n            display: inline-block;\n            transform: translate(50%, -50%);\n          }\n        }\n      }\n    }\n  }\n}\n.shopping-wrapper.rom{\n  .content-center__main {\n    .left-part {\n      .logo {\n        @include utils.bgCenter('koa/rom/logo.png', 140px, 140px);\n      }\n\n      .name {\n        @include utils.bgCenter('koa/rom/name.png', 231PX, 30PX);\n      }\n\n      .charge-construction{\n        color: #FEB522;\n        i {\n          @include utils.bgCenter('koa/charge-construction-flag.png', 19PX, 19PX);\n        }\n      }\n    }\n\n    .right-part{\n      .shop-btn{\n        overflow: visible;\n        span{\n          background: linear-gradient(180deg, #F6E190 0%, #CBA455 100%);\n          font-size: 24PX;\n          font-family: PingFangSC-Semibold, PingFang SC;\n          font-weight: 600;\n          color: #633B00;\n          border-radius: 0;\n          position: relative;\n\n          i{\n            position: absolute;\n            right: 0;\n            top: 0;\n            @include utils.bgCenter('koa/pay-btn-gift.png', 44PX, 46PX);\n            display: inline-block;\n            transform: translate(50%, -50%);\n          }\n        }\n      }\n    }\n  }\n}\n.shopping-wrapper.dc{\n  .page-title {\n    span{\n      background: rgb(53, 121, 209);\n      color: #F4FBFF;\n      @extend .dc-stroke;\n      @extend .dc-btn-decoration;\n      line-height: 1.5;\n\n      i{\n        @include utils.bgCenterForDC('pwa/pwa-install-guide.png', 18PX, 18PX);\n      }\n    }\n  }\n  .content-center__main{\n    .left-part{\n      background: linear-gradient(to bottom, black, rgba(0,0,0,0));\n      min-height: 80vh;\n      .logo {\n        @include utils.bgCenterForDC('pc/logo.png', 140PX, 140PX);\n        margin: 39PX auto 0;\n      }\n\n      .name {\n        @include utils.bgCenterForDC('pc/name.png', 128px, 64px);\n        margin: 20px auto 0;\n      }\n\n      .description{\n        color: #F4FBFF;\n      }\n\n      .charge-construction{\n        color: #FFCC66;\n        i {\n          @include utils.bgCenterForDC('pc/charge-construction-flag.png', 19PX, 19PX);\n          margin-right: 4px;\n        }\n      }\n    }\n    .right-part{\n      .shop-btn{\n        overflow: visible;\n        text-align: left;\n        padding-left: 230px;\n        span{\n          background: rgba(255, 210, 53);\n          font-size: 24PX;\n          font-family: PingFangSC-Semibold, PingFang SC;\n          font-weight: 600;\n          color: #393A3E;\n          border-radius: 0;\n          position: relative;\n          width: 426px;\n          text-align: center;\n\n          i{\n           display: none;\n          }\n\n          @extend .dc-btn-decoration;\n        }\n      }\n    }\n  }\n}\n.shopping-wrapper.ssv{\n  .page-title {\n    span{\n      height: 26PX;\n      background: #FF5E0F;\n      border-radius: 5PX;\n      line-height: 26PX;\n      position: absolute;\n      top: 0;\n      margin: 0 16PX;\n      transform: scale(.95);\n      transform-origin: left top;\n      color: #FFFFFF;\n\n      i{\n        @include utils.bgCenterForSSV('pc/mobile_yingdao.png', 14PX, 19PX)\n      }\n    }\n  }\n  .content-center__main{\n    .left-part{\n      background-color: rgba(255, 255, 255, 0.07);\n      min-height: 80vh;\n      .logo {\n        @include utils.bgCenterForSSV('pc/ss-logo.png', 140PX, 140PX);\n        margin: 39PX auto 0;\n      }\n\n      .name {\n        @include utils.bgCenterForSSV('pc/ss-name.png', 190PX, 87PX);\n        margin: 20px auto 0;\n      }\n\n      .description{\n        color: #919090;\n      }\n\n      .charge-construction{\n        color: #FF813C;\n        i {\n          @include utils.bgCenterForSSV('pc/charge-construction-flag.png', 19PX, 19PX);\n          margin-right: 4px;\n        }\n      }\n    }\n    .right-part{\n      .shop-btn{\n        overflow: visible;\n        text-align: left;\n        padding-left: 230px;\n        span{\n          color: #FFFFFF;\n          background: #FF5E0F;\n          font-size: 24PX;\n          font-family: PingFangSC-Semibold, PingFang SC;\n          font-weight: 600;\n          position: relative;\n          width: 426PX;\n          text-align: center;\n\n          i{\n            display: none;\n          }\n        }\n      }\n    }\n  }\n}\n\n.shopping-wrapper.sdk{\n  @include utils.flexCenter;\n  flex-direction: column;\n  height: 100%;\n  .sdk-body-wrapper{\n    width: 100%;\n    height: 0;\n    flex-grow: 1;\n  }\n  .checkout-footer-wrapper{\n    width: 100%;\n    position: relative;\n  }\n  .copyright{\n    display: none;\n  }\n\n  @include utils.setPcContent{\n    .copyright{\n      display: flex;\n      width: 100%;\n    }\n  }\n}\n.shopping-wrapper.ssv2{\n  .page-title {\n    span{\n      height: 26PX;\n      background: #FF5E0F;\n      border-radius: 5PX;\n      line-height: 26PX;\n      position: absolute;\n      top: 0;\n      margin: 0 16PX;\n      transform: scale(.95);\n      transform-origin: left top;\n      color: #FFFFFF;\n\n      i{\n        @include utils.bgCenterForSS('pc/mobile_yingdao.png', 14PX, 19PX)\n      }\n    }\n  }\n  .content-center__main{\n    .left-part{\n      background-color: rgba(255, 255, 255, 0.07);\n      min-height: 80vh;\n      .logo {\n        @include utils.bgCenterForSSV2('ss-logo.png', 140PX, 140PX);\n        margin: 39PX auto 0;\n      }\n\n      .name {\n        @include utils.bgCenterForSSV2('ss-name.png', 142PX, 103PX);\n        margin: 20px auto 0;\n      }\n\n      .description{\n        color: #919090;\n      }\n\n      .charge-construction{\n        color: #FF813C;\n        i {\n          @include utils.bgCenterForSS('pc/charge-construction-flag.png', 19PX, 19PX);\n          margin-right: 4px;\n        }\n      }\n    }\n    .right-part{\n      .shop-btn{\n        overflow: visible;\n        text-align: left;\n        padding-left: 230px;\n        span{\n          color: #FFFFFF;\n          background: #FF5E0F;\n          font-size: 24PX;\n          font-family: PingFangSC-Semibold, PingFang SC;\n          font-weight: 600;\n          position: relative;\n          width: 426PX;\n          text-align: center;\n\n          i{\n            display: none;\n          }\n        }\n      }\n    }\n  }\n}\n</style>\n", "import mod from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./Pay.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./Pay.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./Pay.vue?vue&type=template&id=093b7ec1&scoped=true\"\nimport script from \"./Pay.vue?vue&type=script&lang=js\"\nexport * from \"./Pay.vue?vue&type=script&lang=js\"\nimport style0 from \"./Pay.vue?vue&type=style&index=0&id=093b7ec1&prod&scoped=true&lang=scss\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"093b7ec1\",\n  null\n  \n)\n\nexport default component.exports", "export * from \"-!../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--9-oneOf-1-0!../../node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--9-oneOf-1-2!../../node_modules/sass-loader/dist/cjs.js??ref--9-oneOf-1-3!../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./DiamondChooseKOA.vue?vue&type=style&index=0&id=83f0c5b0&prod&scoped=true&lang=scss\"", "module.exports = __webpack_public_path__ + \"static/1751299200/img/mycard-logo.18a331bb.png\";", "export * from \"-!../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--9-oneOf-1-0!../../node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--9-oneOf-1-2!../../node_modules/sass-loader/dist/cjs.js??ref--9-oneOf-1-3!../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./CommonFooter.vue?vue&type=style&index=0&id=ae65575c&prod&scoped=true&lang=scss\"", "export * from \"-!../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--9-oneOf-1-0!../../node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--9-oneOf-1-2!../../node_modules/sass-loader/dist/cjs.js??ref--9-oneOf-1-3!../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./CheckoutCounterSDK.vue?vue&type=style&index=0&id=40652ba8&prod&scoped=true&lang=scss\"", "export * from \"-!../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--9-oneOf-1-0!../../node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--9-oneOf-1-2!../../node_modules/sass-loader/dist/cjs.js??ref--9-oneOf-1-3!../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./ExtraDiamond.vue?vue&type=style&index=0&id=5c91cc60&prod&scoped=true&lang=scss\"", "export * from \"-!../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--9-oneOf-1-0!../../node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--9-oneOf-1-2!../../node_modules/sass-loader/dist/cjs.js??ref--9-oneOf-1-3!../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./AddIosSafariBtn.vue?vue&type=style&index=0&id=1df0d6bd&prod&scoped=true&lang=scss\"", "export * from \"-!../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--9-oneOf-1-0!../../node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--9-oneOf-1-2!../../node_modules/sass-loader/dist/cjs.js??ref--9-oneOf-1-3!../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./Pay.vue?vue&type=style&index=0&id=093b7ec1&prod&scoped=true&lang=scss\"", "export * from \"-!../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--9-oneOf-1-0!../../node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--9-oneOf-1-2!../../node_modules/sass-loader/dist/cjs.js??ref--9-oneOf-1-3!../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./CheckoutCounterTax.vue?vue&type=style&index=0&id=d3472866&prod&scoped=true&lang=scss\""], "sourceRoot": ""}