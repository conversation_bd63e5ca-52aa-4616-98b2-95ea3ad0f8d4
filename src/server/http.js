import axios from 'axios'
import store from '@/store'
import i18n from '@/utils/i18n'

function makeUpCommonParams (params) {
  const isOrderPage = location.pathname.includes('/order')
  const state = store.state
  const userinfo = isOrderPage ? state.orderPage.userinfo : state.userinfo
  const gameinfo = state.gameinfo

  if (!params) params = {}
  params.game_id = +gameinfo.gameId
  params.game_project = gameinfo.gameProject
  params.source = 'web'

  if (state.country) params.country = state.country
  if (state.currency) params.currency = state.currency

  if (!params.openid && userinfo.openid) params.openid = userinfo.openid
  if (!params.uid && userinfo.uid) params.uid = userinfo.uid

  if (!isOrderPage) {
    const localOpenid = localStorage.getItem('openid') || ''
    if (!params.openid && localOpenid) {
      params.openid = localOpenid
    }
  }

  if (state.isPCSDK || state.IS_CHECKOUT_SDK) params.source = store.state.urlParams.s
  return params
}
function transformUrl (url = '') {
  const isCn = store.state.gameinfo.isCn
  const Game = isCn ? `${store.state.gameinfo.gameCode}CN` : store.state.gameinfo.gameCode

  if (url.startsWith('/ameCommon')) return url.replace('/ameCommon', process.env.VUE_APP_PREFIX_AME)
  if (url.startsWith('/ame')) return url.replace('/ame', (process.env['VUE_APP_PREFIX_AME_' + Game] || process.env.VUE_APP_PREFIX_AME))
  if (url.startsWith('/account')) return url.replace('/account', (process.env['VUE_APP_PREFIX_ACCOUNT_' + Game] || process.env.VUE_APP_PREFIX_ACCOUNT))
  if (url.startsWith('/api')) return url.replace('/api', process.env['VUE_APP_PREFIX_API_' + Game])
  if (url.startsWith('/token')) return url.replace('/token', process.env['VUE_APP_PREFIX_TOKEN_' + Game])
  return url
}

const service = axios.create({ timeout: 10000 })
service.interceptors.request.use(
  config => {
    if (config.method === 'post') config.data = makeUpCommonParams(config.data)
    else config.params = makeUpCommonParams(config.params)

    // if (process.env.NODE_ENV !== 'development') {
    // 本地开发走转发
    config.url = transformUrl(config.url)
    // }

    const localCountry = sessionStorage.getItem('localCountry') || store.state.urlParams.localCountry
    if (localCountry) {
      const contact = config.url.includes('?') ? '&' : '?'
      config.url += `${contact}country=${localCountry}`
    }

    const localAccountEnv = sessionStorage.getItem('localAccountEnv') || store.state.urlParams.localAccountEnv
    if (localAccountEnv) {
      const prefix = config.url.includes('?') ? '&' : '?'
      config.url += (prefix + `gameServerEnv=${localAccountEnv}`)
    }

    if (config.url.includes('/account/store/user')) {
      const { uid, openid } = config.data
      if (openid && uid) delete config.data.openid
    }

    return config
  },
  error => {
    window.$toast.err(i18n.t('network_err'))
    return Promise.reject(error)
  }
)
service.interceptors.response.use(
  response => {
    //     try {
    //       if (response.status !== 200 || ![0, 2].includes(response.data.code)) {
    //         console.error(
    //           `
    // ${response.config.url}
    // ${response.config.data}
    // ${JSON.stringify(response.data)}
    //           `
    //         )
    //       }
    //     } catch (e) {
    //       console.error(e.message)
    //     }
    return response.data
  },
  error => {
    const isLastRequest = !(error.config.url || '').includes('try=1')
    if (isLastRequest) window.$toast.err(i18n.t('network_err'))
    return Promise.reject(error)
  }
)

async function requestController (method, url, payload) {
  const needRetry = url.startsWith('/token/') || url.startsWith('/account/')

  const requestConfig = { method, url }
  if (method === 'get') requestConfig.params = payload
  if (method === 'post') requestConfig.data = payload

  if (needRetry) {
    requestConfig.url = `${url}?try=1`
    return new Promise((resolve, reject) => {
      console.log(`第1次请求:${requestConfig.url}`)
      service(requestConfig)
        .then(res => resolve(res))
        .catch(() => new Promise((resolve, reject) => setTimeout(reject, 3000)))
        // 第二次请求
        .catch(() => {
          console.log(`第2次请求:${url}`)
          return service(requestConfig)
        })
        .then(res => resolve(res))
        // 第三次请求
        .catch(() => {
          console.log(`第3次请求:${url}`)
          // requestConfig.url = url.replace('/api', '/apiBack')
          return service(requestConfig)
        })
        .then(res => resolve(res))
        .catch(err => reject(err))
    })
  }

  return service(requestConfig)
}

const get = async (url, params) => requestController('get', url, params)
const post = async (url, data) => requestController('post', url, data)

export { get, post, makeUpCommonParams, service }
