@use "~@/utils/utils.scss" as utils;

/* 登录 */
.standard-login{
  @include utils.setMobileContent{
    ::v-deep{
      .label{
        display: none;
      }
    }
  }
  .login-status__not-login{
    .login-input-wrapper{
      width: 444px;
      height: 60px;
      flex-grow: 0;
      margin-right: 12px;

      input{
        padding-left: 20px;
        text-indent: 0;
        line-height: 58px;
        height: 58px;
      }
    }
    .btn-login{
      width: 216px;
      height: 60px;
      text-align: center;
    }
  }
  .login-status__login{
    .avatar{
      width: 100px;
      height: 100px;
      margin-right: 20px;
    }
    .user-info{
      .row-1{
        .name{
          font-size: 28px;
          line-height: 40px;
        }
        .toggle{

        }
      }
      .row-2{
        font-size: 20px;
        line-height: 28px;
        margin-top: 12px;
        .server {
          margin-left: 30px
        }
      }
    }
  }
  .tip-uid-get,
  .tip-please-input{
    font-size: 18px;
    margin-top: 8px;
  }

  @include utils.setPcContent{
    .login-status__not-login{
      .login-input-wrapper{
        margin-right: 0;
        height: 50px;
        width: 428px;
        input{
          height: 35px;
          padding-left: 12px;
        }
      }
      .btn-login{
        height: 50px;
        width: auto;
        margin-left: 12px;
        padding: 0 10px;
        font-size: 21px;
        min-width: 150px;
        line-height: 50px;
      }
    }
    .login-status__login{
      .avatar{
        height: 50px;
        width: 50px;
        margin-right: 0;
      }
      .user-info{
        margin-left: 12px;
        .row-1{
          .name{
            font-size: 20px;
            line-height: 28px;
          }
          .toggle{
            font-size: 16px;
            margin-left: 22px
          }
        }
        .row-2{
          font-size: 16px;
          margin-top: 1px;

          .server{
            margin-left: 20px
          }
        }
      }
    }
    .tip-uid-get,
    .tip-please-input{
      font-size: 13px;
      margin-top: 4px;
    }
  }
}

/* 档位 */
.round-common-bonus{
  /* 有些游戏使用大图标角标：比如foundation */
  @include utils.bgCenterForFoundation('diamond/bonus.png', 80px, 80px);
  top: -9px;
  left: -8px;
  z-index: 10;

  .discount{
    transform: translate(-50%, -50%) rotate(-18deg);
    top: 34px;
    left: 46%;
    text-shadow: 0px 0px 1px rgba(50,27,0,0.73);;
    font-size: 23px;
    font-weight: bold;
    letter-spacing: -1px;
  }
  .off{
    transform: translate(-50%, -50%) rotate(-18deg);
    top: 52px;
    left: 54%;
    font-size: 14px;
    font-weight: bold;
  }

  .send{
    transform: translate(-50%, -50%) rotate(-18deg);
    top: 30px;
    left: 43%;
    max-width: 70%;
    font-size: 14px;
    font-weight: bold;
    text-shadow: none;
  }
  .num{
    i{
      @include utils.bgCenterForFoundation('diamond/diamond-icon.png', 17px, 17px);
      transform: rotate(-30deg);
      margin-left: 0;
    }

    transform: translate(-50%, -50%) rotate(-18deg);
    top: 44px;
    left: 52%;
    display: flex;
    align-items: center;
    font-size: 20px;
    font-weight: bold;
    text-shadow: 0px 0px 1px rgba(50,27,0,0.73);
    letter-spacing: -1px;
  }

  @include utils.setPcContent{
    transform: scale(.7);
    transform-origin: left top;
  }
}
