@use "~@/utils/utils.scss" as utils;

#app {
  .shopping-wrapper {
    .sdk-body-wrapper {
      @include utils.setPcContent {
        padding-bottom: 40PX;
        overflow-y: auto;
        height: calc(100% - 40PX);
      }
      .common-part-wrapper {
        .label {
          color: #444444;
          font-size: 25px;

          @include utils.setPcContent {
            font-size: 20px;
            padding-right: 4px;
          }
        }

        .body {
          #direct-package-name {
            height: 66px;
            border: 2px solid #ff5e0f;
            border-radius: 14px;
            padding: 10px 15px;
            color: #2c3e50;
            background: #f8f8f8;

            @include utils.setPcContent {
              height: 48PX;
              border-radius: 10PX;
              border: 2PX solid #ff5e0f;
            }
          }

          .coupons-wrapper {
            height: 68px;
            border: 1px solid #eee;
            background-color: #f7f7f7;
            border-radius: 12px;
            background-image: none;
            display: flex;
            align-items: center;

            font-family: PingFangSC-Regular;
            font-weight: normal;

            .right {
              padding-right: 10px;
              justify-content: flex-end;
            }

            span {
              display: flex;
              align-items: center;
              font-size: 22px;

              .diamond-icon {
                width: 24px;
                height: 20px;
                margin-left: 6px;
              }
            }

            @include utils.setPcContent {
              width: 460px;
              height: 48px;
              border-radius: 10px;
              padding: 0 20px;

              .right {
                padding-right: 4px;
              }

              span {
                font-size: 16px;

                .diamond-icon {
                  width: 22px;
                  height: 18px;
                  margin-left: 6px;
                }
              }
            }
          }

          .coupons-wrapper__not-login {
            @include utils.setPcContent {
              padding-left: 40px;
            }
          }

          .coupons-wrapper__chosen {
            border: none;
            background: #ff5f0f57;
          }

          .channel-list {
            .channel-btn {
              height: 94px;
              margin: 0 10px 10px 0;
              border-radius: 12px;
              border: 1px solid #eee;
              background-color: #f7f7f7;
              overflow: hidden;

              .recommendation-REC {
                left: 0;
                top: 0;
                background-image: none;
                height: auto;
                width: auto;
                background: linear-gradient(80deg, #F4912C 7.36%, #F06B29 93.38%);
                border-radius: 0 0 12px 0;
                padding: 0 20px;

                .blank {
                  display: none;
                }
              }

              @include utils.setPcContent {
                height: 62px;
                border-radius: 8px;
                margin: 0 7PX 7PX 0;
              }
            }

            .channel-chosen__active {
              border: 2px solid #ff5e0f;
            }
          }

          .checkbox {
            margin-top: -32px;
            

            input {
              cursor: pointer;
              border: 1px solid #ff5500;
              margin-right: 15px;
            }

            label {
              color: #444444;
            }

            a {
              color: #ff5500;
              margin: 0 8px;
            }

            @include utils.setPcContent {
              margin-top: -20px;

              input {
                margin-right: 10px;
              }
            }
          }

          .normal {
            margin-left: 2px;

            .price-wrapper {
              .now-price {
                color: #ff5500;
              }

              .origin-price {
                color: #7a7a7a;
              }

              .off-count-tips {
                padding: 0 10px;
                color: white;
                background: #ff5c0a;
                border-radius: 5px;
                display: flex;
                align-items: center;
                justify-content: center;

                .diamond-icon {
                  margin-left: 4px;
                }
              }
            }

            .rate {
              margin: 14px 0;
              font-size: 16px;
              color: #ff5500;
              display: flex;
              align-items: center;

              i {
                margin-top: -2px;
                width: 18px;
                height: 18px;
                @include utils.bgCenter(
                  "ss/checkout/tax-arrow.png",
                  18px,
                  18px
                );
              }
            }
          }

          .expand {
            .sub-total {
              line-height: 30px;
              font-size: 20px;
              font-weight: bold;
              color: #555555;
            }

            .now-price {
              line-height: 30px;
              font-size: 20px;
              font-weight: bold;
              color: #555555;
            }

            .origin-price {
              line-height: 30px;
              color: #888888;
            }

            .off-count-tips {
              padding: 0 10px;
              color: white;
              background: #ff5c0a;
              border-radius: 5px;
              display: flex;
              align-items: center;
              justify-content: center;
            }

            .tax-wrapper {
              line-height: 26px;
              font-size: 15px;
              color: #606060;
            }

            .final-price {
              margin-top: 6px;
              color: #ff5500;
            }

            .rate {
              font-size: 16px;
              color: #ff5500;
              display: flex;
              align-items: center;

              i {
                width: 18px;
                height: 18px;
                @include utils.bgCenter(
                  "ss/checkout/tax-arrow.png",
                  18px,
                  18px
                );
              }
            }
          }

          .click-btn {
            height: 55px;
            line-height: 55px;
            font-size: 22px;
            width: 350px;
            border-radius: 10px;
            background: #fd6a20;
          }
        }
      }
    }

    .checkout-footer-wrapper {
      box-shadow: 0 -2px 15px #e2e2e2;

      .common-part {
        border-top: 1px solid #ecececb5;
        background-color: #fff;

        .total-price {
          .row-1 {
            .now-price {
              color: #ff5500;
            }

            .rate {
              color: #ff5500;
              display: flex;
              align-items: center;

              i {
                @include utils.bgCenter(
                  "ss/checkout/tax-arrow.png",
                  18px,
                  18px
                );
              }
            }
          }

          .row-2 {
            .origin-price {
              color: #a2a2a2;
              margin-right: 10px;
            }

            .off-count-tips {
              min-width: 90px;
              height: 26px;
              line-height: 26px;
              margin-left: 0;
              padding: 0 10px;
              color: white;
              background: #ff5500;
              border-radius: 8px;
              display: flex;
              align-items: center;
              justify-content: center;

              .diamond-icon {
                margin-left: 4px;
              }
            }
          }
        }

        .click-btn {
          background: #fd6a20;
          color: white;
          border-radius: 16px;
          font-size: 28px;

          i {
            opacity: 0;
          }
        }
      }

      .expand-part {
        z-index: 0;
        background: white;
        border-top: 1px solid #ececec;
        box-shadow: 0 -2px 10px #ededed;
        border-radius: 36px 36px 0 0;

        .pop-title {
          color: #424242;
          font-family: PingFangSC;
          font-weight: normal;
        }

        .value-wrapper {
          div {
            height: 44px;

            span:first-child {
              font-family: PingFangSC-Regular;
              font-weight: normal;
              color: #505050;
              font-size: 22px;
            }

            span:last-child {
              color: #ff5500;
              font-size: 24px;
            }
          }
        }

        .divider {
          border: none;
          border-top: 1px solid #000000;
          opacity: 0.08;
        }

        .divider:last-child {
          border: none;
        }
      }
    }

    .copyright {
      border-top: 1px solid #ececec;
      color: #2c3e50;
      background: white;

      a {
        color: #ff5500 !important;
      }
    }
  }

  .payment-callback-page {
    .info-wrapper {
      .image {
        width: 85px;
        height: 85px;
        margin-top: 4px;
      }

      .image-status__pending {
        margin-bottom: 20px;
      }

      .title-desc {
        font-size: 34px;
        color: #ff5500;
      }

      .title-desc__pending {
        text-align: center;
        margin-left: -10px;
      }

      .command {
        margin-top: 4px;
      }

      .btn-wrapper {
        .btn-status {
            margin: 0 20px;
            border-radius: 14px;
        }
      }
    }

    @include utils.setPcContent { 
      .info-wrapper {
        margin-top: 150PX;

        .image {
            width: 55PX;
            height: 55PX;
            margin-top: 0;
        }

        .image-status__pending {
          margin-bottom: 20PX;
        }
    
        .title-desc__pending {
          margin-left: -10PX;
        }

        .title-desc {
          font-size: 24px;
        }
    
        .command {
          margin-top: 5PX;
        }
    
        .btn-wrapper {
          margin-top: 50PX;

          .btn-status {
            margin: 0 20PX;
            border-radius: 12PX;
          }
        }
      }
    }
  }

  .pop-container {
    padding: 22px 0 38px 0;
    border-radius: 22px;
    background: white;

    .title {
      color: rgb(60, 60, 60);
    }

    .header {
      span {
        color: rgb(60, 60, 60);
      }
    }

    .content {
      min-height: 60px;
      margin-top: 10px;
      padding: 0 40px;
      color: rgb(80, 80, 80);

      .desc {
        line-height: 26px;
        font-size: 18px;
        color: rgb(80, 80, 80);
      }

      .strong {
        color: #F06B29;

        @include utils.setPcContent { 
          font-size: 20px;
        }
      }

      a {
        color: #ff5500;
      }
    }

    .footer {
      .btn {
        border-radius: 12px;
        height: 68px;
        display: flex;
        align-items: center;
        margin: 0 22px;
      }

      .btn-cancel {
        background-image: none;
        text-shadow: none;
        color: #ff5500;
        border: 2px solid #ff5500;
      }

      .btn-confirm {
        background-image: none;
        text-shadow: none;
        background: #ff5500;
        line-height: 28px;
      }

      @include utils.setPcContent { 
        .btn {
          border-radius: 10px;
          height: 50px;
        }
  
        .btn-cancel {
          font-size: 18px;
          border: 2PX solid #ff5500;
        }
  
        .btn-confirm {
          line-height: 20px;
        }
      }
    }

    .footer-wrapper {
      margin-top: 26px;

      .custom-btn {
        margin: 0 15px;

        &.btn-ok {
          height: 54px;
          color: white;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }
    }

    @include utils.setPcContent {
      padding: 22px 0 32px 0;
      border-radius: 20px;

      .title {
        font-size: 24px;
      }

      .content {
        margin-top: 14px;
        padding: 0 50px;

        .desc {
          line-height: 26px;
          font-size: 16px;
        }
      }

      .footer-wrapper {
        margin-top: 22px;

        .custom-btn {
          margin: 0 15px;

          &.btn-ok {
            min-width: 180PX;
            height: 48px;
          }
        }
      }
    }
  }
}

#ticket-chosen-wrapper {
  .pop-main {
    max-width: 686px;
    border-radius: 22px;
    padding-top: 10px;
    background: white;

    .pop-close {
      width: 30px;
      height: 30px;
    }

    .pop-title {
      h3 {
        color: rgb(60, 60, 60);
      }

      span {
        margin-top: 2px;
      }
    }

    .divider {
      opacity: 0.15;
      margin: 10px auto 4px auto;
    }

    .nav-btn-wrapper {
      div {
        font-family: PingFangSC-Regular;
        font-weight: normal;
        font-size: 22px;
      }

      .nav-active {
        font-weight: bold;
        color: rgb(60, 60, 60);
        
        &::after {
          background: #ff5500;
        }
      }
    }

    .main-container {
      .ticket-wrapper {
        .ticket-list {
          .item {
            display: flex;
            align-items: center;
            @include utils.bgCenter("common/coupon/coupon-item.png", 615px, 76px);

            .left {
              width: 23%;
            }

            .right {
              flex: 1;
            }
          }

          .item__active {
            @include utils.bgCenter("common/coupon/coupon-item-chosen.png", 615px, 76px);

            div {
              color: white;
            }
          }
        }
      }

      .btn-confirm {
        width: auto !important;
        height: 66px;
        line-height: 66px;
        font-size: 26px;
        color: white;
        background: #fd6a20;
        border-radius: 16px;
      }
    }
  }

  @include utils.setPcContent {
    .pop-main {
      width: 665PX;
      border-radius: 20px;
      padding-top: 8px;

      .pop-close {
        width: 25PX;
        height: 25PX;
      }

      .pop-title {
        span {
          margin-top: 2px;
        }
      }

      .divider {
        margin: 10px auto 4px auto;
      }

      .nav-btn-wrapper {
        div {
          font-size: 20px;
        }
      }

      .main-container {
        .ticket-wrapper {
          .ticket-list {
            .item {
              height: 76px;
              width: 615px;
            }
          }
        }
        
        .btn-confirm {
          width: auto !important;
          min-width: 220px;
          height: 56px;
          line-height: 56px;
          font-size: 22px;
          border-radius: 10px;
        }
      }
    }
  }
}
