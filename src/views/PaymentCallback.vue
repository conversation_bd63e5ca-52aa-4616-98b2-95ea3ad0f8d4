<template>
  <div :class="['payment-callback-page', $gameName]" id="payment-callback-page">
    <template v-if="status === 'completed'">
      <div class="info-wrapper">
        <div :class="['image','image-status__'+status]"></div>
        <section>
          <div class="title-desc">{{ $t(title[status]) }}</div>
          <div class="command">{{ $t(tips[status]) }}</div>
        </section>
      </div>
      <div class="btn btn-back" @click="backGame">{{ $t(backGameTxt) }}</div>
    </template>
    <template v-else-if="status === 'fail'">
      <div class="info-wrapper">
        <div :class="['image','image-status__'+status]"></div>
        <section>
          <div class="title-desc">{{ $t(title[status]) }}</div>
          <div class="command">{{ $t(tips[status]) }}</div>
        </section>
      </div>
      <p v-if="$route.query.tip_msg" class="mycard-error-tips">{{ $route.query.tip_msg }}</p>
      <div class="btn btn-back" @click="backGame">{{ $t(backGameTxt) }}</div>
    </template>
    <template v-else-if="status === 'pending'">
      <div class="info-wrapper" style="flex-direction: column">
        <div :class="['image','image-status__'+status]"></div>
        <section>
          <div :class="['title-desc', 'title-desc__'+status]">{{ $t('cb_page_pending_desc') }}</div>
          <div class="command">{{ $t('cb_page_pending_tips') }}</div>
        </section>
        <div class="btn-wrapper">
          <div class="btn-status btn-status-not-pay" @click="navTo('/')">{{ $t('btn_status_not_pay') }}</div>
          <div class="btn-status btn-status-has-pay" @click="judgeStatus">{{ $t('btn_status_has_pay')}}</div>
        </div>
      </div>
    </template>
    <template v-else>Configuration Error!</template>
  </div>
</template>

<script>
import { getTokenOrderDetails } from '@/server'
import { mapState } from 'vuex'
import { backAppGame } from '@/utils/utilsSdk2'
// import { exeCommand } from '@/lib/utils'

export default {
  name: 'PaymentCallback',
  data () {
    return {
      status: this.$route.path.replace('/', ''),
      title: { completed: 'cb_pay_succeed', fail: 'cb_page_title_err' },
      tips: { completed: 'cb_view_tips', fail: 'cb_view_err_tips' },

      // pending
      interval: '',
      timeStop: false
    }
  },
  computed: {
    ...mapState(['IS_CHECKOUT_SDK_V2']),
    backGameTxt () {
      return this.IS_CHECKOUT_SDK_V2 ? 'order-back-btn-txt' : 'cb_back_home'
    }
  },
  methods: {
    clearInterval () {
      if (this.interval) {
        clearInterval(this.interval)
        this.interval = ''
      }
    },
    getPaymentStatus () {
      const query = this.$route.query
      const transactionId = query.foreignInvoice || query.orderId || query.OrderId
      const params = { transaction_id: transactionId, hideErrToast: true }

      getTokenOrderDetails(params)
        .then(res => {
          if (res.code === 0) {
            this.$router.replace('/completed')
            this.clearInterval()
          }
        })
    },
    adapterStatus () {
      const map = { fail: 2, completed: 1, pending: 0 }
      return map[this.status]
    },
    backGame () {
      if (this.IS_CHECKOUT_SDK_V2) return backAppGame()
      const flag = location.href.includes('ir=ad') || location.href.includes('ir=aw') || location.href.includes('ir=cko') || location.href.includes('rf=1')
      this.$router.replace('/')
      if (flag) setTimeout(() => window.location.reload(), 300)
      // if (this.isCommonMode) exeCommand('shop_back_game', { state: this.adapterStatus.bind(this)() })
      // else {
      //   try {
      //     const game = this.$store.state.urlParams.g.split('_')
      //     window.location.href = `${location.origin}/${game[0]}`
      //   } catch (e) {
      //     window.location.href = location.origin
      //   }
      // }
    },
    navTo (aim) {
      // logOnBtnClick(aim)
      this.$router.replace(aim)
        .then(res => {
          if (res.fullPath.includes('/gallery')) window.location.reload()
        })
    },
    judgeStatus () {
      if (this.interval) {
        clearInterval(this.interval)
        this.interval = ''
      }
      this.$root.$emit('showPop', 'CallbackPendingTips')
    }
  },
  created () {
    window.fetchOrderStatus = this.adapterStatus.bind(this)
    if (this.status === 'pending') {
      this.getPaymentStatus()
      this.interval = setInterval(() => {
        this.getPaymentStatus()
      }, 2000)

      setTimeout(() => {
        this.clearInterval()
        this.timeStop = true
      }, 60000)
    }
  },
  mounted () {
    if (this.$store.state.IS_CHECKOUT_SDK && !this.IS_CHECKOUT_SDK_V2) {
      const backBtn = document.querySelector('.btn-back')
      backBtn.style.display = 'none'
    }
  },
  beforeDestroy () {
    this.clearInterval()
  }
}
</script>

<style scoped lang="scss">
@use "~@/utils/utils.scss" as utils;

.payment-callback-page {
  box-sizing: border-box;
  font-weight: 500;
  color: #111111;
  min-height: 100%;

  .info-wrapper{
    display: flex;
    align-items: center;
    justify-content: center;
    @include utils.setPropByBp(
      $m:(margin-top: 262px),
      $p:(margin-top: 106px),
    );

    .image{
      background-position: center center;
      background-size: 96% 96%;
      background-repeat: no-repeat;
      @include utils.setPropByBp(
        $p:( height: 50px,width: 50px),
        $m:( height: 75px,width: 75px),
      );
      &.image-status__pending {
        background-image: url('~@/assets/koa/cb/cb-pending-image-status.png');
      }
      &.image-status__fail {
        background-image: url('~@/assets/koa/cb/cb-fail-image-status.png');
      }
      &.image-status__completed {
        background-image: url('~@/assets/koa/cb/cb-completed-image-status.png');
      }
    }

    section{
      text-align: left;
      @include utils.setPropByBp(
        $p:(margin-left: 18px),
        $m:(margin-left: 25px),
      );

      .title-desc {
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #FFFFFF;

        @include utils.setPropByBp(
          $p:(line-height: 33px, font-size: 24px),
          $m:(line-height: 56px, font-size: 40px),
        );
      }

      .command {
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #8C8C8C;

        @include utils.setPropByBp(
          $p:(font-size: 14px),
          $m:(font-size: 24px),
        );
      }
    }

    .btn-wrapper {
      display: flex;
      align-items: center;
      justify-content: center;

      @include utils.setPropByBp(
        $p:(margin-top: 107px),
        $m:(margin-top: 77px),
      );

      .btn-status {
        font-weight: 400;
        display: flex;
        align-items: center;
        justify-content: center;

        box-sizing: border-box;
        white-space: nowrap;

        @include utils.setPropByBp(
          $m:(font-size: 30px,padding: 0 60px,border-radius: 8px,height: 70px,max-width: 37vw,),
          $p:(font-size: 22px,padding: 0 54px,border-radius: 8px,height: 52px),
        );

        &.btn-status-not-pay {
          color: #F05805;
          border: 1PX solid #F05805;
        }

        &.btn-status-has-pay {
          color: white;
          background-color: #F05805;
          @include utils.setPropByBp(
            $p:(margin-left: 67px),
            $m:(margin-left: 65px),
          );
        }
      }
    }
  }

  .mycard-error-tips{
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #8C8C8C;

    @include utils.setPropByBp(
      $p:(font-size: 16px,margin-top: 50px),
      $m:(font-size: 25px,margin-top: 60px),
    );
  }

  .btn-back{
    display: inline-block;
    cursor: pointer;
    background: #FF5E0F;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #FFFFFF;

    @include utils.setPropByBp(
      $p:(padding: 0 60px, height: 52px,font-size: 22px,line-height: 52px, margin-top: 107px,border-radius: 8px),
      $m:(padding: 0 60px, height: 70px,font-size: 30px,line-height: 70px, margin-top: 80px,border-radius: 8px),
    );
  }
}

/* dc */
.payment-callback-page.dc{
  .image{
    &.image-status__pending {
      background-image: url('~@/assets/dc/cb/cb-pending-image-status.png');
    }
    &.image-status__fail {
      background-image: url('~@/assets/dc/cb/cb-fail-image-status.png');
    }
    &.image-status__completed {
      background-image: url('~@/assets/dc/cb/cb-completed-image-status.png');
    }
  }
  .info-wrapper{
    .btn-wrapper {
      .btn-status {
        border-radius: 0;
        color: #393A3E;
        @extend .dc-btn-decoration;
        &.btn-status-not-pay {
          background: rgb(226, 78, 78);
          border: none;
        }

        &.btn-status-has-pay {
          background: rgb(255, 210, 51);
          @include utils.setPropByBp(
            $p:(margin-left: 67px),
            $m:(margin-left: 65px),
          );
        }
      }
    }
  }
  .btn-back{
    background: rgb(53, 121, 209);
    border-radius: 0;
    font-weight: bold;
    @extend .dc-stroke;
    padding: 0 54px;

    @extend .dc-btn-decoration;
  }

  /* pc央视 */
  @include utils.setPcContent{
    .btn-back{
      padding: 0 62px;
    }
  }
}
</style>
