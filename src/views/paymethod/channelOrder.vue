<template>
  <div class="info-wrapper" style="text-align: left">
      <div class="title">{{ $t('adyen-order-details') }}</div>
      <p>{{ $t('adyen-order-info') }}
        <span class="sdk-products-name" v-if="goodsName">{{ goodsName }}</span>
        <span class="token-name" v-else>{{ coin }} {{ $vt('tokenName') }}</span>
      </p>
      <p>{{ $t('totalPrice') }} <span>{{ currency }} {{ amount }}</span></p>

    <div v-if="showForm" class="form">
      <div class="form-title">{{ $t('channel-order-info') }}</div>
      <div class="divider"></div>

      <div class="form-item">
        <div class="label">{{ $t('label-zipcode') }}</div>
        <div class="content">
          <i></i>
          <input type="text" v-model="zipCode" @change="change" @input="fixInput()" :placeholder="$t('label-zipcode')">
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { service } from '@/server/http'

export default {
  name: 'orderInfo',
  props: ['coin', 'amount', 'currency'],
  data () {
    const zipCode = this.$store.state.zipCode
    const showForm = window.__showEmailForm
    return {
      zipCode,
      showForm,
      goodsName: sessionStorage.getItem('goodsName')
    }
  },
  methods: {
    change () {
      const sing = sessionStorage.getItem('id_sign')
      const url = sessionStorage.getItem('url') + '/api/payment/save_billingaddress'
      if (!sing || !sessionStorage.getItem('url')) return

      if (!this.zipCode) return
      if (this.zipCode.length > 15) this.zipCode = this.zipCode.slice(0, 15)

      service.post(url, {
        zipcode: this.zipCode,
        order_id: sing
      })
    },
    fixInput () {
      if (this.$store.state.country !== 'US') return
      this.zipCode = this.zipCode.replace(/[^0-9]/g, '')
    }
  }
}
</script>

<style scoped lang="scss">
@use "~@/utils/utils.scss" as utils;
  .info-wrapper{
    width: calc(100% - 60px);
    background-color: white;
    margin: 30px auto 0;
    padding: 0 18px;
    border-radius: 10px;

    .title{
      line-height: 81px;
      font-size: 30px;
      font-family: SourceHanSansCN-Medium, SourceHanSansCN;
      font-weight: 500;
      color: #00122C;
      border-bottom: 1px solid #EDEEEF;
    }
    p{
      height: 65px;
      font-size: 24px;
      font-family: SourceHanSansCN-Regular, SourceHanSansCN;
      font-weight: 400;
      color: #9AA6BC;
      display: flex;
      justify-content: space-between;
      line-height: 65px;
      white-space: nowrap;

      span{
        color: #000000;
        margin-left: 10px;
      }

      .sdk-products-name{
        line-height: 1.1;
        white-space: normal;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .token-name{
        @include utils.flexCenter;
        height: 100%;
        white-space: normal;
        line-height: 1.1;
        text-align: right;
      }
    }

    .form{
      padding-bottom: 20px;
      margin-top: 40px;

      .form-title{
        font-family: SourceHanSansCN, SourceHanSansCN;
        font-weight: 500;
        font-size: 30px;
        color: #00122C;
        line-height: 44px;
        text-align: left;
      }

      .divider{
        width: 100%;
        height: 1px;
        border: 1px solid #BAC4C9;
        opacity: 0.4;
        margin-top: 18px;
        margin-bottom: 20px;
      }

      .form-item{
        display: flex;
        height: 60px;
        align-items: center;
        justify-content: space-between;

        .label{
          font-family: SourceHanSansCN, SourceHanSansCN;
          font-weight: 400;
          font-size: 24px;
          color: #9AA6BC;
          flex-shrink: 0;
        }

        .content{
          display: flex;
          align-items: center;
          justify-content: center;
          width: 315px;
          border-radius: 4px;
          border: 1px solid #B9C4C9;
          padding-left: 20px;
          padding-right: 1px;

          i{
            @include utils.bgCenter('common/channel/channel-post-code.png', 25px, 28px);
          }

          input{
            appearance: none;
            -webkit-appearance:none;
            background-color: transparent;
            font-size: 24px;
            font-family: SourceHanSansCN, SourceHanSansCN;
            font-weight: 400;
            color: #000000;
            line-height: 40px;
            flex-grow: 1;
            border: none;
            height: 60px;
            width: 1px;
            margin-left: 12px;

            &:active, &:focus{
              appearance: none;
              -webkit-appearance:none;
              outline: none;
            }
          }
          input::-webkit-outer-spin-button,
          input::-webkit-inner-spin-button {
            -webkit-appearance: none !important;
            margin: 0;
          }
          input[type=number]{-moz-appearance:textfield;}
        }
      }
    }
  }
  @media screen and (min-width: 1200PX){
    .info-wrapper{
      width: 320px;
      margin-left: 20px;
      margin-top: 0;

      .title{
        line-height: 40px;
        font-size: 14px;
      }

      p{
        font-size: 12px;
        height: 40px;
        line-height: 40px;
      }

      .form{
        padding-bottom: 15px;
        margin-top: 30px;

        .form-title{
          font-size: 14px;
          line-height: 20px;
        }

        .divider{
          width: 100%;
          margin-top: 12px;
          margin-bottom: 10px;
        }

        .form-item{
          height: 35px;

          .label{
            font-weight: 400;
            font-size: 10px;
          }

          .content{
            width: 130px;
            border-radius: 4px;
            padding-left: 10px;

            i{
              @include utils.bgCenter('common/channel/channel-post-code.png', 14px, 16px);
            }

            input{
              font-size: 10px;
              line-height: 40px;
              height: 35px;
              margin-left: 4px;
            }
          }
        }
      }
    }
  }
</style>
