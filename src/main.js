import Vue from 'vue'
import '@/utils/resLoaderHelper'
import './utils/webReporter'
import './utils/flexible_custom'
import './utils/prepareBasicInfo'
import App from './App.vue'
import router from './router'
import store from './store'
import i18n from '@/utils/i18n'
import VueLazyload from 'vue-lazyload'
import loading from '@/components/common/loading/loading'
import toast from '@/components/common/toast/toast'
import dialog from '@/components/common/dialog/dialog'
import VueGtag from 'vue-gtag'
import { fixToFixed } from '@/utils/utils'
import device from 'current-device'

window.isMobile = device.mobile()
fixToFixed()
Vue.config.productionTip = false
Vue.use(VueGtag, {
  config: { id: window.$idLoader('gid') }
})

Vue.use(VueLazyload)
Vue.use(loading)
Vue.use(toast)
Vue.use(dialog)

new Vue({
  router,
  store,
  i18n,
  render: (h) => h(App)
}).$mount('#app')
