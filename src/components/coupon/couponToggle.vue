<template>
  <div class="coupon-type-toggle-wrapper"
       @click.stop="beginToggleCoupon"
       @mouseover="tips = couponItem.feType === 'discount_coupon' ? $t('toggle-to-rebate') : $t('toggle-to-common')"
       @mouseleave="tips = ''">

    <div class="tips" v-if="tips">{{ tips }}</div>
  </div>
</template>

<script>
import { toggleCouponSingle } from '@/server'

export default {
  name: 'couponToggle',
  props: ['couponItem', 'tempChosen'],
  data () {
    return {
      tips: ''
    }
  },
  methods: {
    beginToggleCoupon () {
      this.tips = ''
      const couponItem = this.couponItem
      const params = {
        change_type: couponItem.feType === 'discount_coupon' ? 'rebate' : 'coupon',
        coupon_id: this.couponItem.coupon_id
      }

      this.$loading.show()
      toggleCouponSingle(params)
        .then(res => {
          const { code } = res
          if (code === 0) {
            sessionStorage.setItem('reopenCoupon', '1') // 用户保持弹窗打开状态
            if (this.tempChosen) sessionStorage.setItem('reChooseCoupon', this.couponItem.coupon_id) // 用于恢复本次选中的券
            this.$root.$emit('couponToggleSuccess')
          } else {
            this.$toast.err(this.$t('toggle-fail-tips'))
          }
        })
        .catch(err => {
          console.error(err.message)
        })
        .finally(() => this.$loading.hide())
    }
  },
  mounted () {
    if (this.couponItem.FE_INDEX === sessionStorage.getItem('popIndex')) {
      sessionStorage.removeItem('popIndex')
      this.tips = this.couponItem.feType === 'discount_coupon' ? this.$t('toggle-to-rebate') : this.$t('toggle-to-common')
      this.$el.scrollIntoView({ behavior: 'smooth' })
    }
  }
}
</script>

<style scoped lang="scss">
@use "~@/utils/utils.scss" as utils;

.coupon-type-toggle-wrapper{
  @include utils.bgCenter('koa/activity/coupon-toggle-btn-bg.png', 24px, 24px);
  margin-left: -4px;
  margin-right: 6px;
  position: relative;
  z-index: 1;

  .tips{
    font-family: SourceHanSansCN, SourceHanSansCN;
    font-weight: 500;
    font-size: 16px;
    color: #E0E0E0;
    line-height: 24px;
    text-align: left;
    font-style: normal;
    position: absolute;
    bottom: -10px;
    left: 0;
    transform: translate(-30px, 100%);
    background: #606060;
    padding: 7px 24px;
    border-radius: 8px;
    max-width: 550px;
    white-space: nowrap;

    &:after{
      content: '';
      display: inline-block;
      @include utils.bgCenter('koa/activity/coupon-toggle-caret.png', 7px, 7px);
      position: absolute;
      top: 2px;
      left: 37px;
      transform: translate(0, -100%);
      z-index: -1;
    }
  }
}
@include utils.setPcContent{
  .coupon-type-toggle-wrapper{
    @include utils.bgCenter('koa/activity/coupon-toggle-btn-bg.png', 20px, 20px);

    .tips{
      transform: translate(-15px, 100%);
      background: #606060;
      border-radius: 5px;
      max-width: 750px;
      font-size: 13px;
      line-height: 18px;

      &:after{
        @include utils.bgCenter('koa/activity/coupon-toggle-caret.png', 6px, 6px);
        top: 1px;
        left: 21px;
      }
    }
  }
}
</style>
