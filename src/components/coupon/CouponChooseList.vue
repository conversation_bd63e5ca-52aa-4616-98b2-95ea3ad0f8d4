<template>
  <div :class="['ticket-wrapper', $gameName]" id="ticket-wrapper">
    <div class="ticket-list">
      <template v-for="(item, index) in couponList">
        <div :class="[
          'item',
          {'item__active': reach && item.FE_INDEX === tempChosenCoupon.FE_INDEX },
          {'item__unavailable':!isFirstChargeUsed || !reach || item.is_invalid === 0}]"
             :key="item.coupon_id + item.type + index"
             v-if="item.feType!=='first_pay' && item.feType!=='first_pay_rebate' && ('leaveCount' in item ? item.leaveCount > 0 : true )"
             @click="choose(index, item)">
          <div class="left">
            <coupon-toggle
              v-if="$store.state.formdata.switchToggleState && item.change_enable"
              :temp-chosen="reach && item.FE_INDEX === tempChosenCoupon.FE_INDEX"
              :coupon-item="item">
            </coupon-toggle>
            <div class="title">
              <template v-if="item.feType==='discount_coupon'">{{ item.rate }} <span>OFF</span></template>
              <template v-else-if="item.feType==='cash_coupon'">
                <span :class="{'is-ar-zone': isArZone}">{{ item.deduct_price}} {{ currencyUnit }}</span> OFF
              </template>
              <template v-else-if="item.feType ==='rebate_coupon'">
                <span>{{ $t('bonus_tips') }} </span>{{ item.rate }}<i class="diamond-icon"></i>
              </template>
            </div>
            <!--<div class="desc">满xxx</div>-->
          </div>
          <div class="right">
            <div class="desc">{{ $t(item.langKey, {0:item.num}) }} {{ item.langValue }}</div>
            <div class="time" v-if="item.showLeaveDate">{{ item.showLeaveDate }}</div>
          </div>
        </div>
      </template>
    </div>
    <span class="no-data" v-if="showEmpty">{{ $t('nothingHere') }}</span>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import CouponToggle from '@/components/coupon/couponToggle'
import { onceADay } from '@/utils/utils'

export default {
  name: 'CouponChooseList',
  components: { CouponToggle },
  props: ['couponList', 'isFirstChargeUsed', 'reach', 'tempChosenCoupon'], // reach 未获取
  methods: {
    choose (index, item) {
      /* 券不可使用 */
      if (!this.isFirstChargeUsed || !this.reach || item.is_invalid === 0) return null

      if (this.tempChosenCoupon.FE_INDEX === item.FE_INDEX) this.$emit('update:tempChosenCoupon', {})
      else {
        this.$emit('update:tempChosenCoupon', item)
        this.$root.$emit('availableTicketChosen')
      }
    }
  },
  computed: {
    ...mapState(['isArZone', 'currencyUnit']),
    showEmpty () {
      return !this.couponList.filter(item => item.feType.includes('_coupon')).length
    }
  },
  created () {
    if (this.$store.state.formdata.switchToggleState && this.reach && onceADay('toggleCoupon')) {
      const findIndex = this.couponList.findIndex(item => item.change_enable)
      if (this.couponList[findIndex]) sessionStorage.setItem('popIndex', this.couponList[findIndex].FE_INDEX)
    }
  }
}
</script>

<style scoped lang="scss">
@use "~@/utils/utils.scss" as utils;

.is-ar-zone{
  display: inline-block;
}

.ticket-wrapper {
  margin-top: 27px;
  padding: 3px 20px;
  text-align: center;

  @include utils.setPropByBp(
    $m: (margin-top: 22px, padding: 3 24px),
    $p: (margin-top: 37, padding: 3px 20px)
  );

  .ticket-list {
    overflow-y: scroll;
    display: flex;
    flex-wrap: wrap;
    box-sizing: border-box;
    padding-bottom: 20px;

    @include utils.setPropByBp(
      $m: (max-height: 600px),
      $p: (max-height: 270px),
    );

    .item {
      cursor: pointer;
      box-sizing: border-box;
      font-weight: 400;
      display: flex;
      transition: all .1s;
      background-size: 100% 100%;
      margin: 0 auto;

      @include utils.setPropByBp(
        $m: (
          height: 90px,
          width: calc(100% - 28px),
          background-image: url(~@/assets/koa/coupon/shopping-modal-ticket-bg-m.png)),
        $p: (
          height: 64px,
          width: 619px,
          background-image: url(~@/assets/koa/coupon/shopping-modal-ticket-bg.png)),
      );

      .left {
        height: 100%;
        flex-grow: 0;
        display: flex;
        align-items: center;
        //flex-direction: column;
        justify-content: center;
        @include utils.setPropByBp(
          $m: (width: 29%),
          $p: (width: 157px),
        );

        .title {
          font-family: PingFang SC;
          font-weight: bold;
          color: #373737;
          //white-space: nowrap;
          max-width: 70%;

          @include utils.setPropByBp(
            $m: (line-height: 20px, font-size: 18px),
            $p: (line-height: 16px, font-size: 16px),
          );
        }

        .desc {
          font-family: PingFang SC;
          font-weight: bold;
          color: #545454;

          @include utils.setPropByBp(
            $m: (line-height: 22px, font-size: 16px, margin-top: 6px),
            $p: (line-height: 12px, font-size: 12px, margin-top: 6px)
          );
        }

        i.diamond-icon {
          @include utils.bgCenterForKoaIcon('koa/diamond/diamond.png', 15px, 12px);
          display: inline-block;
          margin-left: 2px;
          position: relative;
        }
      }

      .right {
        position: relative;
        width: 0;
        flex-grow: 1;

        .desc {
          position: absolute;;
          left: 50%;
          top: 50%;
          transform: translate(-50%, -50%);
          font-family: PingFang SC;
          font-weight: bold;
          color: #373737;
          @include utils.setPropByBp(
            $m: (line-height: 22px,width: 96%,font-size: 16px),
            $p: (line-height: 16px,width: 414px,font-size: 14px)
          );
        }

        .time {
          position: absolute;
          font-family: PingFang SC;
          font-weight: bold;
          color: #545454;
          @include utils.setPropByBp(
            $m: (right: 15px, bottom: 9px, font-size: 16px,),
            $p: (right: 8px, bottom: 10px, font-size: 12px,),
          );
        }
      }

      &.item__active {
        position: relative;
        background-size: 100% 100%;

        @include utils.setPropByBp(
          $m: (background-image: url(~@/assets/koa/coupon/shopping-modal-ticket-bg-m_active.png)),
          $p: (background-image: url(~@/assets/koa/coupon/shopping-modal-ticket-bg_active.png)),
        );
      }

      &.item__unavailable{
        cursor: not-allowed;
        opacity: .5;
      }

      &:nth-of-type(n+2) {
        @include utils.setPropByBp(
          $m: (margin-top: 12px),
          $p: (margin-top: 10px)
        );
      }
    }
  }

  .no-data {
    text-align: center;
    margin: 0 auto;
    font-weight: bold;
    color: #666666;
    width: 100%;
    position: relative;

    @include utils.setPropByBp(
      $m: (font-size: 25px, line-height: 220px, top: -40px),
      $p: (font-size: 18px, line-height: 220px, top: -10px)
    );
  }
}

.ticket-wrapper.dc{
  .ticket-list {
    min-height: 200px;
    .item {
      @include utils.bgCenterForDC('coupon/coupon-pop-list-item.png', 614px, 88px);

      &.item__active {
        @include utils.bgCenterForDC('coupon/coupon-pop-list-item-active.png', 614px, 88px);
      }

      .left {
        .title{
          color: white;
          @extend .dc-stroke;
        }
        i.diamond-icon {
          @include utils.bgCenterForDC('diamond/diamond-icon.png', 18px, 18px);
        }
      }

      .right{
        .desc{
          color: #4A402C;
        }
        .time {
          color: #7E7055;
        }
      }
    }
  }

  @include utils.setPcContent{
    .ticket-list {
      .item {
        @include utils.bgCenterForDC('coupon/coupon-pop-list-item-pc.png', 564px, 58px);
        .left{
          i.diamond-icon {
            @include utils.bgCenterForDC('diamond/diamond-icon.png', 17px, 17px);
          }
        }
        &.item__active {
          @include utils.bgCenterForDC('coupon/coupon-pop-list-item-active-pc.png', 564px, 58px);
        }
      }
    }
  }
}
</style>
