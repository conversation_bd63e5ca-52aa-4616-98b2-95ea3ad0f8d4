<script>
import './Preload'
import CommonPart from '../common/CommonPart.vue'
import FixedCouponSwitch from '../FixedCouponSwitch.vue'
import { getRedirectProductList } from '../../server'

export default {
  components: { CommonPart, FixedCouponSwitch },
  data () {
    return {
      goodsName: '-'
    }
  },
  methods: {
    loadDiamondList () {
      const params = {}

      if (this.$store.state.urlParams.tc) {
        const tc = JSON.parse(this.$store.state.urlParams.tc)
        params.product_id = tc.product_id

        this.goodsName = tc.product_name
      }
      this.$loading.show()
      getRedirectProductList(params)
        .then(res => {
          const { data, code } = res
          if (code === 0) {
            if (data.length && data.length !== 1) return this.$toast.err(this.$t('cb_page_title_err'))
            this.$store.commit('formdata/setChosenDiamond', data[0])
          }
        })
        .finally(() => this.$loading.hide())
    },
    loadOtherDiamondList () {
      const params = {
        game_order_id: this.$store.state.urlParams.oid || ''
      }
      if (this.$store.state.urlParams.tc) {
        const tc = JSON.parse(this.$store.state.urlParams.tc)
        params.product_id = tc.product_id
        if (tc.product_name) {
          this.goodsName = tc.product_name
          this.$root.$emit('updateSdk2PackageName', tc.product_name)
        }
      }
      if (!params.game_order_id) return this.$toast.err(this.$t('sdk2_error_order'))

      this.$loading.show()
      getRedirectProductList(params)
        .then(res => {
          const { data, code } = res
          switch (code) {
            case 0: {
              this.$store.commit('formdata/setChosenDiamond', data)
              if (data.sku_name && this.goodsName === '-') {
                this.goodsName = data.sku_name
                this.$store.commit('updateUrlParams', { product_name: data.sku_name })
                this.$root.$emit('updateSdk2PackageName', this.goodsName)
              }
              if (this.goodsName === '-') this.setDefaultName(data) // sdk2 有些游戏没有礼包名称
              break
            }
            case 106041: {
              this.$toast.err(this.$t('cb_view_err_tips'))
              break
            }
            default: {
              this.$toast.err(this.$t('RU_refused'))
            }
          }
        })
        .finally(() => this.$loading.hide())
    },
    resetSdkStatus () {
      localStorage.setItem('isWhatDiamondPop', 'true') // sdk不需要弹什么事钻石
    },
    loginFail () {
      this.$nextTick(() => {
        if (document.querySelector('.checkout-footer-wrapper')) document.querySelector('.checkout-footer-wrapper').style.display = 'none'
        if (document.querySelector('.checkout-counter-sdk-b')) document.querySelector('.checkout-counter-sdk-b').style.display = 'none'
      })
    },
    setDefaultName (data) {
      const defaultName = this.$t('sdk2_default_package_name', { 1: data.no_tax_price, 0: data.currency_symbol })
      this.goodsName = defaultName

      this.$store.commit('updateUrlParams', { product_name: defaultName })
      this.$root.$emit('updateSdk2PackageName', defaultName)
    }
  },
  created () {
    this.resetSdkStatus()
    this.$root.$on('loginEnd', (status) => {
      if (status === 1) this.$gameName === 'dc' ? this.loadDiamondList() : this.loadOtherDiamondList()
      if (status === 0) this.loginFail()
    })

    console.log(this.$gcbk('switch.switchRebate', false))
  }
}
</script>

<template>
  <!-- <common-part :labelFont="$t('adyen-order-info')"> -->
  <common-part>
    <template #label>
      <div class="package-label">
        <div>{{ $t('adyen-order-info') }}</div>
        <div v-if="$gcbk('switch.switchRebate', false) && $store.state.formdata.isFixedEventOpen">
          <fixed-coupon-switch></fixed-coupon-switch>
        </div>
      </div>
    </template>
    <div class="goods-name" id="direct-package-name" :class="[$gameName]">
      {{ goodsName }}
    </div>
  </common-part>
</template>

<style scoped lang="scss">
@use "~@/utils/utils.scss" as utils;
.package-label {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.goods-name{
  width: 674px;
  height: 80px;
  background: #34353D;
  border: 1px solid #000000;

  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  font-size: 24px;
  color: #FFFFFF;
  line-height: 33px;
  text-align: left;
  font-style: normal;

  @include utils.flexCenter;
  justify-content: flex-start;
  padding-left: 21px;
}

@include utils.setPcContent{
  .goods-name{
    width: auto;
    height: 48px;
    font-size: 18px;
    padding-left: 10px;
  }
}
</style>
