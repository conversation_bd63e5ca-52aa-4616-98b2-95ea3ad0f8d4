<template>
  <div class="cover-bg" @click="$emit('close')">
    <section :style="{transform: `translate(-50%, -50%) scale(${1.5 * $store.state.scaleSize})`}" class="toggle-user-info" @click.stop>
      <div class="now-info">
        <div class="avatar" v-lazy:background-image='userinfo.icon'></div>
        <div class="id" v-if="userinfo.name"><i></i><span>{{ userinfo.name }}</span></div>
      </div>
      <div class="info-panel">
        <div class="info-list">
          <div v-for="roleItem in uidList"
               :key="roleItem.uid"
               @click="choose(roleItem)"
               :class="['info-item', {'info-item__active':chosenObj.uid === roleItem.uid}]">
            <div class="avatar" v-lazy:background-image='roleItem.avatar'></div>
            <div class="other-info">
              <div class="row row-1">
                <div class="id">{{ roleItem.name }}</div>
                <div class="last-time">{{ roleItem.last_login }}</div>
              </div>
              <div class="row row-2">{{ $t('userinfo_level', {0: roleItem.level}) }}<span></span>{{ $t('userinfo_server', {0: roleItem.server}) }}</div>
            </div>
            <div v-if="chosenObj.uid === roleItem.uid || userinfo.uid === roleItem.uid" class="active-mark"></div>
          </div>
        </div>
        <div :class="['toggle-confirm', {'toggle-confirm__disable': !chosenObj.uid}]" @click="confirmToggle">{{ $t('switch_character') }}</div>
      </div>
    </section>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import { OrderPageOpenidKey } from '@/config/OrderPageConf'

export default {
  name: 'toggleInfo',
  props: ['uidList'],
  data () {
    return {
      chosenObj: {}
    }
  },
  computed: {
    ...mapState('orderPage', ['userinfo'])
  },
  methods: {
    choose (roleItem) {
      if (roleItem.uid === this.userinfo.uid) {
        return null
      }
      if (roleItem.uid === this.chosenObj.uid) {
        this.chosenObj = {}
        return null
      }
      this.chosenObj = roleItem
    },
    confirmToggle () {
      if (this.chosenObj.uid) {
        this.$emit('choose', this.chosenObj.uid)
        this.$emit('close')

        localStorage.removeItem(OrderPageOpenidKey)
        // 切换完账号去掉后面的token参数
        // this.$router.replace('/order')
      }
    }
  }
}
</script>

<style scoped lang="scss">
@use "~@/utils/utils.scss" as utils;

.cover-bg {
  position: fixed;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, .4);
  z-index: 100;
  left: 0;
  top: 0;

  .toggle-user-info {
    position: absolute;
    width: 400px;
    height: 267px;
    background: #F2F2F2;
    border-radius: 10px;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%) scale(1.5);
    display: flex;
    overflow: hidden;

    .now-info {
      width: 104px;
      display: flex;
      flex-direction: column;
      align-items: center;
      background: #FFFFFF;

      .avatar {
        height: 66px;
        width: 66px;
        background-color: black;
        border-radius: 50%;
        margin-top: 25px;
        background-size: cover;
      }

      .id {
        display: flex;
        align-items: center;
        justify-content: center;
        margin-top: 7px;

        span{
          font-size: 11px;
          font-family: PingFang TC;
          font-weight: 400;
          color: #333333;
          line-height: 11px;
          max-width: 70px;
          overflow: hidden;
          text-overflow: ellipsis;
          display: inline-block;
        }

        i {
          display: inline-block;
          //@include koaBgCenterMobile(14px, 15px, 'login-toggle-Info-id.png');
          margin-right: 5px;
          display: none;
        }
      }
    }

    .info-panel {
      padding-top: 10px;
      padding-left: 15px;
      position: relative;
      flex-grow: 1;

      .info-list {
        height: 200px;
        margin: 0 auto;
        overflow-y: auto;
        padding-top: 5px;
        padding-bottom: 10px;

        .info-item {
          width: 267px;
          height: 48px;
          background: #FFFFFF;
          border: 1px solid transparent;
          box-shadow: 0px 11px 19px 2px rgba(0, 0, 0, 0.02);
          border-radius: 8px;
          display: flex;
          align-items: center;
          padding-left: 12px;
          cursor: pointer;
          transition: border 150ms;
          position: relative;

          .avatar {
            width: 34px;
            height: 34px;
            border-radius: 8px;
            background-size: cover;
          }

          .other-info {
            margin-left: 10px;
            width: 80%;

            .row-1 {
              display: flex;
              justify-content: space-between;
              position: relative;
              top: 2px;

              .id {
                font-size: 12px;
                font-family: PingFang SC;
                font-weight: 400;
                color: #111111;
                line-height: 12px;
                overflow: hidden;
                max-width: 120px;
                text-overflow: ellipsis;
              }

              .last-time {
                font-size: 14px;
                font-family: PingFang TC;
                font-weight: 400;
                color: #666666;
                transform: scale(.5);
                white-space: nowrap;
                transform-origin: right center;
                position: absolute;
                right: 0;
              }
            }

            .row-2 {
              font-size: 12px;
              transform: scale(0.75);
              transform-origin: left center;
              font-family: PingFang SC;
              font-weight: 400;
              color: #999999;
              line-height: 12px;
              text-align: left;
              display: flex;
              margin-top: 11px;

              span {
                display: inline-block;
                width: 10px;
              }
            }
          }

          .active-mark {
            @include utils.bgCenter('common/login/login-toggle-list-active-mark.png', 9px, 9px);
            position: absolute;
            left: 2px;
            top: 2px;
          }

          &:nth-of-type(n+2) {
            margin-top: 13px;
          }

          &:hover, &.info-item__active{
            border-color: rgba(255, 90, 0, 0.2);
          }
        }
      }

      .toggle-confirm {
        //width: 110px;
        padding: 0 10px;
        height: 36px;
        max-width: 200px;
        background: #FE6917;
        border-radius: 10px;
        font-size: 14px;
        font-family: PingFang SC;
        font-weight: 400;
        color: #FFF6E9;
        line-height: 36px;
        left: 50%;
        transform: translateX(-50%);
        position: absolute;
        bottom: 11px;
        cursor: pointer;

        &.toggle-confirm__disable{
          opacity: .7;
          cursor: auto;
        }
      }
    }
  }
}
</style>
