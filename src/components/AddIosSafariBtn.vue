<template>
  <div :class="['add-ios-wrapper', $gameName]" id="add-ios-wrapper">
    <transition name="addScreen">
      <div v-if="(isShowBtn && showTimes < defaultShowTimes) || !this.hadInstall" @click="showGuide" class="add-to-main-screen__mobile heart">{{  $t('add-to-screen')  }}</div>
    </transition>

    <transition name="iosGuide">
      <div v-show="isShowGuide" class="ios-guide" :class="{ active: isShowGuide }" @click="isShowGuide = false">
        <div class="guide-wrap" @click.stop>
          <div class="close" @click="isShowGuide = false"></div>
          <div class="title">{{ $t('ios-guide-0') }}</div>
          <div class="subtitle">{{ $t('ios-guide-2') }} {{ $t('ios-guide-3') }}</div>
          <div class="logo"></div>
          <div class="game">{{ gameName }} TopupCenter</div>
          <div class="phone"></div>
          <i18n path="ios-guide-4" tag="div" class="txt">
            <div :slot="0" class="up"></div>
          </i18n>
          <i18n path="ios-guide-5" tag="div" class="txt">
            <div :slot="0" class="add"></div>
            <template :slot="1">{{ $t('ios-guide-1') }}</template>
          </i18n>
          <div class="mune">
            <div>{{ $t('ios-guide-1') }}</div>
          </div>
          <div class="arr"></div>
        </div>
      </div>
    </transition>
  </div>
</template>

<script>
import { ameHoldByGet } from '@/server'
import { calcDisplayMode } from '@/utils/utils'

export default {
  name: 'AddIosSafariBtn',
  data () {
    return {
      isShowBtn: false,
      isShowGuide: false,
      showTimes: Number(window.localStorage.getItem('_i_g_times') || 0),
      defaultShowTimes: 3,
      hadInstall: true
    }
  },
  created () {
    const ua = window.navigator.userAgent.toLowerCase()
    const isQuickApp = window.navigator.standalone
    var issafariBrowser = (ua.indexOf('applewebkit') > -1 && ua.indexOf('mobile') > -1 && ua.indexOf('safari') > -1 &&
      ua.indexOf('linux') === -1 && ua.indexOf('android') === -1 && ua.indexOf('chrome') === -1 &&
      ua.indexOf('ios') === -1 && ua.indexOf('browser') === -1)
    if (issafariBrowser && this.$store.state.isMobile && !isQuickApp) {
      if (this.showTimes < this.defaultShowTimes) {
        this.isShowBtn = true
      }
      window.localStorage.setItem('_i_g_times', this.showTimes + 1)
    }

    // 重新判断pwa安装
    if (issafariBrowser && this.$store.state.isMobile && !isQuickApp) {
      this.$root.$on('loginSuccess', () => this.rejudge())
    }

    this.$root.$on('mobileSafariGuide', () => {
      this.isShowGuide = true
    })
  },
  methods: {
    showGuide () {
      this.$gtag.event('click_guide', { event_label: 'safari' })
      this.showTimes = this.defaultShowTimes
      window.localStorage.setItem('_i_g_times', this.showTimes)
      this.isShowGuide = true
    },
    rejudge () {
      const { projectId, pwaOpenAction } = this.$gcbk('apiParams.boonAme', {})
      if (!pwaOpenAction) return
      const params = { p0: 'web', p1: projectId, p2: pwaOpenAction }
      this.$loading.show()
      ameHoldByGet(params)
        .then(res => {
          if (calcDisplayMode() === 'standalone') return false
          const { data = [], code } = res
          if (code === 0) {
            this.hadInstall = data.length === 1

            if (!this.hadInstall) {
              const cancel = setInterval(() => {
                if (calcDisplayMode() === 'standalone') {
                  clearInterval(cancel)
                  this.hadInstall = true
                }
              }, 2000)
            }
          }
        })
        .finally(() => this.$loading.hide())
    }
  },
  computed: {
    gameName(){
      return this.$gcbk('gameinfo.sortName') || (this.$store.state.gameinfo.game || '').toUpperCase()
    }
  }
}
</script>

<style scoped lang="scss">
@use "~@/utils/utils.scss" as utils;
@keyframes breathe {
  0%{ transform: scale(.98); }
  50%{ transform: scale(1.05); }
  100%{ transform: scale(.98); }
}
.add-ios-wrapper{
  @include utils.setMobileContent{
    .addScreen-enter{
      transform: translateY(-100%);
      opacity: 0;
    }
    .addScreen-leave-to{
      transform: translateY(100%);
      opacity: 0;
    }
    .addScreen-enter-active,
    .addScreen-leave-active{
      transition: all .3s;
    }

    .add-to-main-screen__mobile{
      height: 50px;
      top: 10px;
      right: 15px;
      border-radius: 35px;
      font-size: 24px;
      line-height: 50px;
      padding: 0 30px;
      position: fixed;
      background-color: rgba(255, 255, 255, .7);
      cursor: pointer;
      z-index: 100;
    }
  }
  .ios-guide {
    position: fixed;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    z-index: 10000;
    background: rgba(0, 0, 0, 0.5);
    transition: all .3s linear;
    text-align: center;
    &.iosGuide-enter{
      .guide-wrap {
        transform: translateY(100%);
      }
    }
    &.iosGuide-leave-to{
      .guide-wrap {
        transform: translateY(100%);
      }
    }
    .guide-wrap {
      border-top-left-radius: 20px;
      border-top-right-radius: 20px;
      background-color: rgba(30, 30, 42, 1);
      position: absolute;
      left: 0;
      bottom: 0;
      width: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 40px 10px 20px;
      transition: all .3s linear;
    }
    .close {
      width: 28px;
      height: 28px;
      position: absolute;
      right: 30px;
      top: 20px;
      background-image: url("~@/assets/common/pwa/icon_guide_close.png");
      background-size: 100% 100%;
      background-position: center center;
      background-repeat: no-repeat;
    }
    .title {
      font-size: 40px;
      padding: 0 40px;
      font-weight: 600;
      color: #FFFFFF;
      line-height: 56px;
    }
    .subtitle {
      font-size: 28px;
      font-weight: 400;
      color: #A5A5A5;
      line-height: 40px;
      margin-top: 5px;
    }
    .logo {
      background-size: 100% 100%;
      background-position: center center;
      background-repeat: no-repeat;
      width: 130px;
      height: 134px;
      margin-top: 10px;
    }
    .game {
      font-size: 30px;
      font-weight: 600;
      color: #FFFFFF;
      line-height: 42px;
    }
    .phone {
      background-image: url("~@/assets/koa/pwa/icon_guide_phone.png");
      background-size: 100% 100%;
      background-position: center center;
      background-repeat: no-repeat;
      width: 240px;
      height: 261px;
      margin-top: -62px;
      margin-bottom: -20px;
    }
    .txt {
      font-size: 32px;
      font-weight: 600;
      color: #FFFFFF;
      line-height: 1;
      margin-top: 10px;
      div {
        display: inline-block;
        vertical-align: middle;
        background-size: 100% 100%;
        background-position: center center;
        background-repeat: no-repeat;
        width: 68px;
        height: 68px;
      }
      .up {
        background-image: url("~@/assets/common/pwa/icon_guide_up.png");
      }
      .add {
        background-image: url("~@/assets/common/pwa/icon_guide_add.png");
      }
    }
    .mune {
      margin-top: 15px;
      width: 454px;
      height: 226px;
      background-image: url("~@/assets/common/pwa/guide_mune.png");
      background-size: 100% 100%;
      background-position: center center;
      background-repeat: no-repeat;
      position: relative;
      text-align: left;
      div {
        position: absolute;
        height: 85px;
        line-height: 85px;
        bottom: 0;
        left: 20px;
        right: 80px;
        font-size: 26px;
        color: #585555;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        font-weight: 600;
      }
    }
    .arr {
      width: 50px;
      height: 50px;
      background-image: url("~@/assets/common/pwa/icon_guide_arr.png");
      background-size: 100% 100%;
      background-position: center center;
      background-repeat: no-repeat;
      margin-top: 20px;
    }
  }
  .heart {
    animation: breathe 1s ease-in-out infinite;
  }
}

.add-ios-wrapper.koa{
  .ios-guide{
    .logo{
      background-image: url("~@/assets/koa/pwa/icon_guide_logo.png");
    }
    .phone{
      background-image: url("~@/assets/koa/pwa/icon_guide_phone.png");
    }
  }
}
.add-ios-wrapper.aof{
  .ios-guide{
    .logo{
      background-image: url("~@/assets/koa/aof/icon_guide_logo.png");
    }
    .phone{
      background-image: url("~@/assets/koa/aof/icon_guide_phone.png");
    }
  }
}
.add-ios-wrapper.rom{
  .ios-guide{
    .logo{
      background-image: url("~@/assets/koa/rom/icon_guide_logo.png");
    }
    .phone{
      background-image: url("~@/assets/koa/rom/icon_guide_phone.png");
    }
  }
}
.add-ios-wrapper.dc{
  .ios-guide{
    .logo{
      background-image: url("~@/assets/dc/pwa/icon_guide_logo.png");
    }
    .phone{
      background-image: url("~@/assets/dc/pwa/icon_guide_phone.png");
    }
  }
}
.add-ios-wrapper.ssv2{
  .ios-guide{
    .logo{
      background-image: url("~@/assets/ss/ssv2/icon_guide_logo.png");
    }
    .phone{
      background-image: url("~@/assets/ss/ssv2/icon_guide_phone.png");
    }
  }
}
</style>
