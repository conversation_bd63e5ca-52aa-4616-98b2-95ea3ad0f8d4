<template>
  <div class="tips-wrapper">
    <transition name="bg">
      <div v-show="showBg" class="modal-bg"></div>
    </transition>
    <transition name="modal">
      <div v-if="message" class="modal-click-before-choose">
        <div class="custom-modal-title">
          <h3>{{ i18n.t('text_tips') }}</h3>
          <div class="custom-modal-close" @click="dismiss"></div>
        </div>
        <div class="custom-modal-content">{{ message }}</div>
        <div class="custom-modal-footer">
          <div class="custom-modal-btn" @click="dismiss">{{ i18n.t('modalBtnOk') }}</div>
        </div>
      </div>
    </transition>
  </div>
</template>

<script>
import i18n from '@/utils/i18n'
export default {
  name: 'message',
  data () {
    return {
      message: '',
      showBg: false,
      cb: '',
      i18n
    }
  },
  methods: {
    open (msg, duration, cb) {
      this.showBg = true
      this.$nextTick(() => {
        this.message = msg
        this.cb = cb
      })
    },
    dismiss () {
      this.message = ''
      this.showBg = false
      // setTimeout(() => { this.cb() }, 300)
    }
  }
}
</script>

<style lang="scss" scoped>
@use "~@/utils/utils.scss" as utils;

.modal-enter-active,.modal-leave-active{
  transition: all .3s
}
.modal-enter{
  transform: translate(-50%, -25%) !important;
  opacity: 0;
}
.modal-leave-to{
  opacity: 0;
}

.bg-enter-active, .bg-leave-active{
  transition: opacity .3s;
}
.bg-enter, .bg-leave-to{
  opacity: 0;
}

.tips-wrapper{
  position: fixed;
  z-index: 100;
}

.modal-bg{
  background-color: rgba(0,0,0,0.6);
  height: 100%;
  width: 100%;
  position: fixed;
  left: 0;
  top: 0;
  z-index: 100;
}
.modal-click-before-choose{
  position: fixed;
  left: 50%;
  top: 50%;
  transform: translate(-50%,-50%);
  background-color: #393434;
  background-image: url("~@/assets/common/pop/dialog-bg.png");
  background-size: 100% auto;
  background-repeat: no-repeat;
  display: flex;
  flex-direction: column;
  align-items: center;
  z-index: 101;
  min-height: 200px;
  width: 670px;
  padding-bottom: 45px;

  .custom-modal-title{
    width: 100%;
    text-align: center;

    h3{
      height: 80px;
      line-height: 80px;
      font-size: 38px;
      font-family: PingFangSC-Semibold, PingFang SC;
      font-weight: 600;
      color: #D6CBB1;
    }

    .custom-modal-close{
      right: 4px;
      top: 4px;
      position: absolute;
      z-index: 1;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      //background-image: url("~@/assets/image/shopping-modal-close.png");
      background-size: 20px 20px;
      background-position: center center;
      background-repeat: no-repeat;
    }
  }

  .custom-modal-content{
    font-size: 28px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #8C8C8C;
    line-height: 40px;
    padding: 65px 52px;
    text-align: center;
    width: 100%;
  }

  .custom-modal-footer{
    display: flex;
    align-items: center;
    justify-content: center;

    .custom-modal-btn{
      background-color: #FF5A00;
      cursor: pointer;
      font-size: 31px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #FFFFFF;
      line-height: 86px;
      padding: 0 68px;
      border-radius: 8px;
    }
  }

  @include utils.setPcContent{
    background-image: url("~@/assets/common/pop/dialog-bg_pc.png");
    width: 550PX;
    min-height: 100PX;
    padding-bottom: 6PX;

    .custom-modal-title{
      h3{
        height: 62PX;
        line-height: 50PX;
        font-size: 22PX;
      }
    }

    .custom-modal-content{
      font-size: 18PX;
      line-height: 25PX;
      padding: 30PX 77PX;
    }

    .custom-modal-footer{
      .custom-modal-btn{
        font-size: 24PX;
        line-height: 53PX;
        height: 53PX;
        padding: 0 36PX;
        border-radius: 8PX;
      }
    }
  }
}
</style>
