<template>
  <transition name="loading">
    <div v-if="loadingNum>0" :class="['loading-wrapper', $gameName, {'rp': $gameName.includes('RP')}]">
      <div class="lds-dual-ring"></div>
    </div>
  </transition>
</template>

<script>
export default {
  name: 'Loading',
  data: function () {
    return {
      loadingNum: 0
    }
  },
  methods: {
    showLoading () {
      this.loadingNum++
    },
    hideLoading () {
      if (this.loadingNum - 1 < 0) console.error('mistake match！')
      this.loadingNum = Math.max(0, --this.loadingNum)
    }
  }
}
</script>

<style lang="scss" scoped>
@use "~@/utils/utils.scss" as utils;

.loading-enter,.loading-leave-to{
  opacity: 0;
}

.loading-enter-active,.loading-leave-active{
  transition: all .3s;
}

@keyframes lds-dual-ring {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.loading-wrapper{
  height: 100vh;
  width: 100vw;
  background-color: rgba(0,0,0,0);
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  z-index: 100000;
  display: flex;
  align-items: center;
  justify-content: center;
  // opacity: 0;

  .lds-dual-ring {
    display: inline-block;

    &:after{
      content: " ";
      display: block;
      border-radius: 50%;
      animation: lds-dual-ring 1.2s linear infinite;
    }
  }

  @include utils.setPcContent{
    .lds-dual-ring{
      width: 60PX;
      height: 60PX;

      &:after{
        width: 36PX;
        height: 36PX;
        margin: 5PX;
        border: 4.3PX solid #fff;
        border-color: #DDB358 transparent #DDB358 transparent;
      }
    }
  }
  @include utils.setMobileContent{
    .lds-dual-ring{
      width: 80px;
      height: 80px;
      display: flex;
      align-items: center;
      justify-content: center;

      &:after{
        width: 60px;
        height: 60px;
        border: 8px solid #fff;
        border-color: #DDB358 transparent #DDB358 transparent;
      }
    }
  }
}

.loading-wrapper.ssv2{
  @include utils.setPcContent{
    .lds-dual-ring{
      width: 40PX;
      height: 40PX;

      &:after{
        width: 33PX;
        height: 33PX;
        border: 3PX solid #fff;
        border-color: #FF5A00 transparent #FF5A00 transparent;
      }
    }
  }
  @include utils.setMobileContent{
    .lds-dual-ring{
      width: 60px;
      height: 60px;
      display: flex;
      align-items: center;
      justify-content: center;

      &:after{
        width: 50px;
        height: 50px;
        border: 5px solid #fff;
        border-color: #FF5A00 transparent #FF5A00 transparent;
      }
    }
  }
}

.loading-wrapper.foundation,
.loading-wrapper.ssd,
.loading-wrapper.rp{
  @extend .ssv2;
}

.loading-wrapper.ssCP, .loading-wrapper.stCP, .loading-wrapper.mcCP, .loading-wrapper.gogCP, .loading-wrapper.romCP {
  @include utils.setPcContent{
    .lds-dual-ring {
      width: 60px;
      height: 60px;

      &:after {
        margin: 0;
        border: none;
        animation-duration: 1.5s;
        @include utils.bgCenter("common/icon/loading.png", 45px, 45px);
      }
    }
  }

  @include utils.setMobileContent {
    .lds-dual-ring {
      width: 100px;
      height: 100px;

      &:after {
        margin: 0;
        border: none;
        animation-duration: 1.5s;
        @include utils.bgCenter("common/icon/loading.png", 60px, 60px);
      }
    }
  }
}
</style>
