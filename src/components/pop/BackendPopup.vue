<template>
  <div>
    <transition name="pop">
      <div class="popup" v-show="popupShow">
        <img class="popup-image" :src="popupSrc" @click="jumpPage" />
        <button class="popup-close" @click="closePopup"></button>
      </div>
    </transition>
  </div>
</template>

<script>
import { getAmeDo } from "@/server";
export default {
  name: "BackendPopup",
  props: {
    option: Object,
  },
  data(){
    return {
      game: this.$store.state.gameinfo.gameProject.split('_')[0],
      gameEnv: "master",
      popupShow: false,
      popupList: [],
      index: 0,
      ymd: "",
      lang: "",
      popupSrc: "",
      jumpLink: "",
    };
  },
  methods: {
    changePopup() {
      this.popupShow = false;
      let curPopup = this.popupList[this.index];
      let curPopupSrc = "";
      let curJumpLink = "";
    //   if (curPopup.mode == 1 && localStorage.getItem(`${this.game}_${curPopup.id}`) === this.ymd) {
    //     this.closePopup();
    //     return;
    //   }
      if (curPopup.category == 1) {
        if (!curPopup.file_details[this.option.country]) {
          this.closePopup();
          return;
        } else curPopupSrc = curPopup.file_details[this.option.country];
      } else {
        curPopupSrc = curPopup.file_details[this.$i18n.locale || this.lang] || curPopup.file_details.en;
      }
      if (!curPopupSrc) {
        this.closePopup();
        return;
      }
      curJumpLink = curPopup.jump_link;
      localStorage.setItem(`${this.game}_${curPopup.id}`, this.ymd);
      setTimeout(() => {
        this.popupSrc = curPopupSrc;
        this.jumpLink = curJumpLink;
        this.popupShow = true;
      }, 350);
    },
    closePopup() {
      this.index++;
      if (this.index >= this.popupList.length) {
        this.popupShow = false
        setTimeout(() => this.$root.$emit("closePop"), 350)
      }
      else this.changePopup();
    },
    jumpPage() {
      let finalUrl = "";
      if (this.jumpLink) {
        if (this.jumpLink.split("?").length >= 2) {
          finalUrl = `${this.jumpLink}&openid=${encodeURIComponent(localStorage.getItem("openid") || "")}&l=${this.$i18n.locale || this.lang}`;
        } else {
          finalUrl = `${this.jumpLink}?openid=${encodeURIComponent(localStorage.getItem("openid") || "")}&l=${this.$i18n.locale || this.lang}`;
        }
        window.open(finalUrl, "_blank");
      }
    },
  },
  created() {
    const broswerLang = navigator.language || navigator.userLanguage;
    if (broswerLang.toLowerCase() === "zh-tw") this.lang = "zh_tw";
    else if (broswerLang.startsWith("zh")) this.lang = "zh_cn";
    else this.lang = broswerLang.split("-")[0];

    const date = new Date(Date.now() + new Date().getTimezoneOffset() * 60 * 1000);
    this.ymd = `${date.getFullYear()}/${date.getMonth() + 1}/${date.getDate()}`;

    // const params = Object.assign({
    //     p0: 'web',
    //     p1: 9,
    //     p2: '1653',
    //     p3: 'api',
    //     game: this.game
    // }, process.env.VUE_APP_PROD_ENV === 'ONLINE' ? {} : { gameEnv: this.gameEnv })
    // getAmeDo(params).then((response) => {
    //     if (response.code === 0) {
    //         this.popupList = response.data
    //         if (this.popupList.length) this.changePopup()
    //         else this.closePopup()
    //         for (let i = 0; i < this.popupList.length; i++) {
    //             const image = new Image()
    //             image.src = this.popupList[i].category == 1
    //                 ? this.popupList[i].file_details[this.option.country]
    //                 : this.popupList[i].file_details[this.$i18n.locale || this.lang]
    //         }
    //     }
    // })
    this.popupList = this.option.popupList;
    if (this.popupList.length) this.changePopup();
    else this.closePopup();
    for (let i = 0; i < this.popupList.length; i++) {
      const image = new Image();
      image.src =
        this.popupList[i].category == 1
          ? this.popupList[i].file_details[this.option.country]
          : this.popupList[i].file_details[this.$i18n.locale || this.lang];
    }
  },
};
</script>

<style scoped lang="scss">
@use "~@/utils/utils.scss" as utils;

.popup {
  z-index: 100001;
  position: fixed;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  align-items: center;
}

.popup-image {
  width: 550px;
  border-radius: 5px;
  cursor: pointer;
  transition: all 0.5s;

  @include utils.setPcContent {
    width: 500PX;
  }
}

.popup-close {
  width: 54px;
  height: 54px;
  margin-top: 30px;
  background: none;
  background-image: url("~@/assets/common/pop/close-popup.png");
  background-size: cover;
  outline: none;
  border: 0;
  cursor: pointer;

  @include utils.setPcContent {
    width: 45px;
    height: 45px;
  }

  &:active {
    transform: scale(0.95);
    -webkit-filter: brightness(0.85);
    filter: brightness(0.85);
  }
}

.pop-enter-active,
.pop-leave-active {
  transition: all 0.3s;
}

.pop-enter {
  opacity: 0;
}

.pop-leave-to {
  opacity: 0;
}
</style>
