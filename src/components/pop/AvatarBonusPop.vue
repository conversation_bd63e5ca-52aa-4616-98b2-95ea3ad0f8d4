<template>
  <container :customContent=true size="avatar-bonus-pop-container" :hideFooter="true" :hideHeader="true" class="arrears-reminder-wrapper" :class="[$i18n.locale]">
    <i @click="$root.$emit('closePop')"></i>
    <div class="avatar-bonus-pop-content">
      <div class="content-body">
        <div class="title">{{ $t('gog-activity-banner-avatar-title') }}</div>
        <div class="frame"></div>
        <div class="btn" @click="$root.$emit('closePop')">{{ $t('shop_now') }}</div>
      </div>
    </div>
  </container>
</template>

<script>
import Container from '@/components/pop/container'

export default {
  name: 'AvatarBonusPop',
  components: { Container }
}
</script>

<style scoped lang="scss">
@use "~@/utils/utils.scss" as utils;
.avatar-bonus-pop-container {
  background-color: transparent !important;
  border: 0px !important;
  @include utils.setPropByBp(
    $m: (padding: 0px 0 0px,width: 700px, height: 385px),
    $p: (padding: 0px 0 0px,width: 771px),
  );

  i {
    @include utils.bgCenter('koa/pop/pop-close-bg.png',36px,36px);
    display: inline-block;
    position: absolute;
    z-index: 100001;
    cursor: pointer;

    @include utils.setPropByBp(
      $m: (top:0px, right: 0px, width:36px, height: 36px),
      $p: (top:0px, right: 0px, width:36px, height: 36px),
    );
  }
 .avatar-bonus-pop-content {
    @include utils.bgCenter('koa/pop/avatar-bonus-bg.png',100%, 100%);
    position: relative;
    padding-right: 25px;
    display: flex;
    justify-content: flex-end;
    .content-body {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: flex-start;
      width: 409px;
    }
    .title {
      margin: 0;
      color: #FFF7CB;
      padding-top: 89px;
      width: 100%;
      height: 126px;
      display: flex;
      align-items: center;
      justify-content: center;
      text-align: center;
      font-size: 26px;
      font-family: SourceHanSansCN-Bold, SourceHanSansCN;
      font-weight: bold;
      color: #FFF429;
      line-height: 1.2;
      @include utils.setPropByBp(
        $m: (padding-top: 80px, font-size: 24px, line-height: 34px, height: 114px)
      );
    }
    .frame {
      @include utils.bgCenter('koa/pop/avatar-bonus-frame.png', 91px, 88px);
      margin-top: 54px;
      @include utils.setPropByBp(
        $m: (padding-top: 48px, width: 83px, height: 81px)
      );
    }

    .btn {
      @include utils.bgCenter('koa/pop/avatar-bonus-btn.png', 251px, 54px);
      cursor: pointer;
      margin-top: 70px;
      font-size: 24px;
      color: #ffffff;
      text-shadow: 0px 1px 1px rgba(255,255,255,0.77);
      line-height: 54px;
      @include utils.setPropByBp(
        $m: (margin-top: 62px, width: 228px, height: 50px, font-size: 22px, line-height: 50px)
      );
    }

  }

  @include utils.setPcContent{
    .avatar-bonus-pop-content {
      @include utils.bgCenter('koa/pop/avatar-bonus-bg.png', 771px, 424px);
      padding-right: 30px;
      .content-body {
        width: 437px;
      }
    }
  }
}

</style>
