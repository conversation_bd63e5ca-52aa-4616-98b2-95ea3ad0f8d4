<template>
  <container :hide-close="true" class="arrears-reminder-wrapper" :class="[$i18n.locale]" id="pop-arrears-reminder">
    <div class="desc">{{ $t('arrears-reminder-desc') }}</div>
<!--<div class="debt" v-html="$t('arrears-reminder-debt', {0: `<span>${option.debt}</span>`, 1: `<i></i>`})"></div>-->

    <template #footerBtn>
      <div class="custom-btn btn-cancel" @click="cancel">{{ $t('arrears-reminder-cancel') }}</div>
      <div class="custom-btn btn-ok" @click="go">{{ $t('arrears-reminder-ok') }}</div>
    </template>
  </container>
</template>

<script>
import Container from '@/components/pop/container'
export default {
  name: 'ArrearsReminder',
  props: {
    option: Object
  },
  components: { Container },
  methods: {
    go () {
      this.$root.$emit('closePop')
      this.$root.$emit('arrearsReminderResult', true)
    },
    cancel () {
      this.$root.$emit('closePop')
      this.$root.$emit('arrearsReminderResult', false)
    }
  }
}
</script>

<style scoped lang="scss">
@use "~@/utils/utils.scss" as utils;

.arrears-reminder-wrapper {
  box-shadow: 0px 2px 12px 0px rgba(0,0,0,0.25);
  border-radius: 20px;
  border: 2px solid #000000;
  background-color: white;
  padding-top: 12px!important;

  .desc{
    font-size: 20px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    line-height: 28px;
    color: #2C2C2C;
    text-align: left;
  }

  .debt{
    margin-top: 15px;
    background: #F6F6F6;
    border-radius: 6px;
    font-size: 18px;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: #A2A2A2;
    line-height: 28px;
    text-align: left;
    padding: 12px 15px;
    overflow: hidden;

    ::v-deep{
      span{
        color: #FE6917;
        margin: 0 4px;
      }
      i{
        @include utils.bgCenter('common/diamond/diamond-icon.png', 20px, 20px);
        display: inline-block;
      }
    }
  }

  ::v-deep{
    .footer-wrapper{
      display: flex;
      align-items: center;
      justify-content: center;
      margin-top: 30px;

      .custom-btn{
        min-width: 200px;
        border-radius: 10px;
        font-size: 20px;
        padding: 11px 0;
        line-height: 28px;
        cursor: pointer;

        &.btn-ok{
          margin-left: 32px;
          background: #FE6917;
          color: #FFFFFF;
        }

        &.btn-cancel{
          color: #7C7C7C;
          border: 1px solid #A8A6A8;
        }
      }
    }
  }

  @include utils.setPcContent{
    padding-top: 2PX!important;

    .desc{
      font-size: 18PX;
      line-height: 25PX;
    }

    .debt{
      margin-top: 15PX;
      padding: 12PX 15PX;
      font-size: 18PX;
      line-height: 25PX;

      ::v-deep{
        span{
          margin: 0 4PX;
        }
        i{
          @include utils.bgCenter('common/diamond/diamond-icon.png', 16PX, 16PX);
          margin-left: 1PX;
        }
      }
    }

    ::v-deep{
      .footer-wrapper{
        margin-top: 30PX;

        .custom-btn{
          min-width: 200PX;
          border-radius: 10PX;
          font-size: 20PX;
          padding: 11PX 0;
          line-height: 28PX;

          &.btn-ok{
            margin-left: 32PX;
          }

          &.btn-cancel{
            color: #7C7C7C;
            border: 1PX solid #A8A6A8;
          }
        }
      }
    }
  }

  &.ar{
    .desc, .debt{
      text-align: right;
      direction: rtl;
    }
  }
}
</style>
