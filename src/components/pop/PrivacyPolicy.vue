<template>
  <container :title="$t('text_tips')" :hide-close="true" class="arrears-reminder-wrapper" :class="[$i18n.locale, $gameName]">
    <div class="desc">{{ $t('settings_terms_of_service_agreement_description') }}</div>
    <div class="private">
      <span @click="go(0)">《{{ $t('agreement') }}》</span>
      <span @click="go(1)">《{{ $t('privacy') }}》</span>
    </div>
    <template #footerBtn>
      <div class="custom-btn btn-ok" @click="close">{{ $t('confirm-btn') }}</div>
    </template>
  </container>
</template>

<script>
import Container from '@/components/pop/container'
import { StorageUtils } from '@/utils/storageUtils'
import { getAmeDo } from '@/server'

export default {
  name: 'PrivacyPolicy',
  props: {
    option: Object
  },
  components: { Container },
  methods: {
    go (seq) {
      const country = (this.$store.state.country || '').toLowerCase()
      const global = ['https://funplus.com/terms-conditions', 'https://funplus.com/privacy-policy/']
      const special = {
        // tw: ['https://privacy.sosgame.tw/terms-conditions.html', 'https://privacy.sosgame.tw/privacy-policy.html'],
        kr: ['https://funplus.com/terms-conditions-en-as/kr/', 'https://funplus.com/privacy-policy-en-as/kr/'],
        jp: ['https://funplus.com/terms-conditions-en-as/ja/', 'https://funplus.com/privacy-policy-en-as/ja/ ']
      }

      let finalUrl = global[seq]
      if (special[country]) finalUrl = special[country][seq]

      if (this.$store.getters['gameinfo/isPuzalaGame']) {
        finalUrl = ['https://www.puzala.com/terms-of-service', 'https://www.puzala.com/privacy-policy'][seq]
      }

      window.open(finalUrl, '_blank')
    },
    close () {
      StorageUtils.setLocalStorage('confirmPrivacyPolicy', 1)
      this.$store.commit('setPrivacyPolicyStatus', true)
      const params = {
        p0: 'web',
        p1: 7,
        p2: '1096',
        silence: true
      }
      getAmeDo(params)

      this.$root.$emit('closePop')
    }
  }
}
</script>

<style scoped lang="scss">
@use "~@/utils/utils.scss" as utils;

.arrears-reminder-wrapper {
  border-radius: 20px;
  background-color: #383838;
  padding-top: 30px!important;

  .desc{
    text-align: left;
    font-size: 18px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #FFFFFF;
    line-height: 25px;
  }

  .private{
    margin-top: 20px;
    text-align: left;
    font-size: 16px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #FF5E0F;
    line-height: 24px;
    position: relative;
    left: -8px;

    span{
      cursor: pointer;
      position: relative;

      &:after{
        content: '';
        height: 2px;
        width: calc(100% - 30px);
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        background-color: #FF5E0F;
      }
    }
  }

  ::v-deep{
    .content{
      margin-top: 20px;
    }

    .footer-wrapper{
      display: flex;
      align-items: center;
      justify-content: center;
      margin-top: 30px;

      .custom-btn{
        min-width: 200px;
        border-radius: 10px;
        font-size: 20px;
        padding: 11px 0;
        line-height: 28px;
        cursor: pointer;

        &.btn-ok{
          margin-left: 32px;
          background: #FE6917;
          color: #FFFFFF;
        }
      }
    }
  }

  @include utils.setPcContent{
    padding-bottom: 30PX;

    .desc{
      font-size: 18PX;
      line-height: 25PX;
    }
    .private{
      margin-top: 20PX;
      font-size: 16PX;
      line-height: 24PX;
      left: -8PX;

      span{
        &:after{
          height: 1PX;
          width: calc(100% - 20PX);
        }
      }
    }

    ::v-deep{
      .content{
        margin-top: 20PX;
      }
      .footer-wrapper{
        margin-top: 30PX;

        .custom-btn{
          min-width: 200PX;
          border-radius: 10PX;
          font-size: 20PX;
          padding: 11PX 0;
          line-height: 28PX;

          &.btn-ok{
            margin-left: 32PX;
          }
        }
      }
    }
  }

  &.ar{
    .desc, .debt{
      text-align: right;
      direction: rtl;
    }
  }
}

.arrears-reminder-wrapper.dc{
  border-radius: 0;
  .desc{
    @extend .dc-stroke
  }
  ::v-deep{
    .content{
      margin-top: 20px;
    }

    .footer-wrapper{
      .custom-btn{
        &.btn-ok{
          @extend .dc-stroke;
          @extend .dc-btn-decoration;
          border-radius: 0;
        }
      }
    }
  }
}
</style>
