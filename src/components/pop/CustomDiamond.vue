<template>
  <container class="custom-diamond-wrapper" :class="[$i18n.locale, $gameName]">
    <div class="title">
      {{ $t('custom-diamond-label') }}
      <i class="close" @click="$root.$emit('closePop')"></i>
    </div>
    <div class="diamond-input-wrapper">
      <span class="basic-num">{{ coin }}</span>
      <i></i>
      x
      <input type="number" placeholder="" v-model="num" :class="{ 'error-input': errorInput }" @input="floorInput" @blur="validateCustomInput">
    </div>
    <div class="tips" :class="{ 'error-input': errorInput }">*{{ $vt('customDiamondLimitTips') }}</div>

    <div class="checkout-wrapper">
      <div class="left">
        <div class="total-diamond">{{ $t('totalPrice') }}：{{ coin * num }} <i></i></div>
<!--        <template v-if="customProductId === chosenDiamond.product_id && chosenCoupon.productId === customProductId">-->
<!--          <div :class="['now-price',{'is-ar-zone': isArZone}]">-->
<!--            <span class="symbol">{{ currencySymbol }}</span>-->
<!--            <template v-if="chosenCoupon.feType==='first_pay'">{{ chosenCoupon.discount_price }}</template>-->
<!--            <template v-if="chosenCoupon.feType==='discount_coupon'">{{ chosenCoupon.discount_price }}</template>-->
<!--            <template v-if="chosenCoupon.feType==='cash_coupon'">{{ chosenCoupon.price }}</template>-->
<!--          </div>-->
<!--          <div :class="['origin-price',{'is-ar-zone': isArZone}]">{{ `${currencySymbol} ${defaultPrice}` }}</div>-->
<!--        </template>-->
<!--        <template v-else-if="chosenCouponOther[customProductId]">-->
<!--          <div :class="['now-price',{'is-ar-zone': isArZone}]">-->
<!--            <span class="symbol">{{ currencySymbol }}</span>-->
<!--            {{ chosenCouponOther[customProductId].price }}-->
<!--          </div>-->
<!--          <div :class="['origin-price',{'is-ar-zone': isArZone}]"> {{ `${currencySymbol} ${chosenCouponOther[customProductId].original_price}` }}</div>-->
<!--        </template>-->
<!--        <template v-else>-->
<!--          <div class="now-price">{{ currencySymbol }} {{ calcPrice(price * num) }}</div>-->
<!--        </template>-->
          <div v-if="priceState.nowPrice" :class="['now-price',{'is-ar-zone': isArZone}]">{{ priceState.nowPrice }}</div>
          <div v-if="priceState.originPrice" :class="['origin-price',{'is-ar-zone': isArZone}]">{{ priceState.originPrice }}</div>
      </div>
      <div class="right">
        <div class="btn" @click="submit">{{ $t('confirm-btn') }}</div>
      </div>
    </div>
  </container>
</template>

<script>
import Container from '@/components/pop/containerV2'
import { mapGetters, mapState } from 'vuex'
import { priceHelper } from '@/utils/utils'

const MAX_VALUE = 100
const MIN_VALUE = window.$gcbk('ids.minCustomDiamondNum', 11)
export default {
  name: 'CustomDiamond',
  components: { Container },
  props: {
    option: Object
  },
  data () {
    return {
      num: MIN_VALUE,
      price: 0,
      coin: 0,
      currencySymbol: '',
      customProductId: '',
      defaultPrice: 0
    }
  },
  methods: {
    submit () {
      if (this.errorInput) return null
      if (this.option.cb) this.option.cb(this.num)
      this.$root.$emit('closePop')
    },
    floorInput () {
      this.num = Math.floor(this.num)
      if (this.num > MAX_VALUE) this.num = MAX_VALUE
    },
    validateCustomInput () {
      if (this.num > MAX_VALUE) this.num = MAX_VALUE
      if (this.num < MIN_VALUE) this.num = MIN_VALUE
    }
  },
  computed: {
    ...mapState('formdata', ['chosenDiamond', 'chosenCoupon', 'chosenCouponOther']),
    ...mapState(['currency', 'isArZone']),
    ...mapGetters('formdata', ['isDiamondOwn95Off', 'isDiamondOwnRebate']),
    calcPrice () {
      return price => priceHelper(price, this.currency)
    },
    errorInput () {
      return this.num < MIN_VALUE || this.num > MAX_VALUE
    },
    priceState () {
      /* 现在只兼容了：固定返钻和95折，如需增加优惠券需要在开发 */
      const priceState = { nowPrice: 0, originPrice: 0 }
      if (this.isDiamondOwn95Off({ product_id: this.customProductId })) {
        priceState.nowPrice = this.calcPrice(this.price * this.num * 0.95)
        priceState.originPrice = this.calcPrice(this.price * this.num)
      } else {
        // 固定返钻和默认情况
        priceState.nowPrice = this.calcPrice(this.price * this.num)
      }

      if (priceState.nowPrice) priceState.nowPrice += ` ${this.currencySymbol}`
      if (priceState.originPrice) priceState.originPrice += ` ${this.currencySymbol}`
      return priceState
    }
  },
  created () {
    const { diamond } = this.option
    this.price = diamond.level_currency_price
    this.coin = diamond.coin
    this.num = diamond.chosenNum
    this.currencySymbol = diamond.currency_symbol
    this.customProductId = diamond.product_id
    this.defaultPrice = diamond.defaultPrice
  }
}
</script>

<style scoped lang="scss">
@use "~@/utils/utils.scss" as utils;

$red: #FF5E0F;
.error-input{
  color: $red!important;
}

.custom-diamond-wrapper {
  background-color: #4C4C4C;
  color: white;
  border-radius: 18px;
  padding-top: 20px;
  overflow: hidden;

  .title{
    height: 56px;
    font-size: 40px;
    font-family: PingFangSC-Semibold, PingFang SC;
    font-weight: 600;
    color: #FFFFFF;
    line-height: 56px;
    position: relative;

    .close{
      @include utils.bgCenter('koa/pop/pop-close.png',34px,33px);
      display: inline-block;
      position: absolute;
      cursor: pointer;
      right: 30px;
      top: 50%;
      transform: translateY(-50%);
    }
  }

  .diamond-input-wrapper{
    display: inline-block;
    align-items: center;
    justify-content: center;
    margin-top: 30px;

    // x *
    font-size: 28px;
    font-family: PingFangSC-Semibold, PingFang SC;
    font-weight: 600;

    .basic-num{
      font-size: 30px;
      font-family: PingFangSC-Semibold, PingFang SC;
      font-weight: 600;
      color: #FFFFFF;
      line-height: 42px;
    }

    i {
      margin: 0 10px 0 2px;
      cursor: pointer;
      @include utils.bgCenter( 'koa/diamond/diamond.png', calc(23px * 1.1), calc(19px * 1.1));
      flex-shrink: 0;
      position: relative;
      display: inline-block;
    }

    input{
      border: 1px solid #979797;
      background: transparent;
      border-radius: 8px;
      height: 60px;
      margin-left: 10px;
      appearance: none;
      -webkit-appearance:none;
      color: white;
      text-indent: 15px;
      font-size: 30px;
      width: 260px;
      font-weight: 600;

      &:active, &:focus{
        appearance: none;
        -webkit-appearance:none;
        outline: none;
      }

      &.error-input{
        border-color: $red;
      }
    }
    input::-webkit-outer-spin-button,
    input::-webkit-inner-spin-button {
      -webkit-appearance: none !important;
      margin: 0;
    }
    input[type=number]{-moz-appearance:textfield;}
  }

  .tips{
    font-size: 18px;
    font-family: PingFangSC-Semibold, PingFang SC;
    font-weight: 600;
    color: #FFFFFF;
    line-height: 22px;
    margin-top: 10px;
  }

  .checkout-wrapper{
    background: #383838;
    margin-top: 30px;
    justify-content: space-between;
    padding: 18px 30px 18px 30px;
    display: flex;
    align-items: center;

    .left{
      text-align: left;
      .total-diamond{
        font-size: 26px;
        font-family: PingFangSC-Regular, PingFang SC;
        color: #FFFFFF;
        line-height: 1;
        font-weight: 600;

        i {
          @include utils.bgCenter( 'koa/diamond/diamond.png', calc(23px * 1.1), calc(19px * 1.1));
          flex-shrink: 0;
          display: inline-block;
        }
      }
      .now-price{
        font-size: 42px;
        font-family: PingFangSC-Semibold, PingFang SC;
        font-weight: 600;
        color: #FDDB70;
        line-height: 1;
        margin-top: 10px;

        .symbol{
          font-size: 26px;
          line-height: 1;
        }
      }
      .origin-price{
        font-size: 22px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #FDDB70 ;
        line-height: 1;
        text-decoration: line-through;
        margin-top: 10px;
      }
    }
    .right{
      .btn{
        line-height: 70px;
        background: #FDDB70;
        border-radius: 35px;
        padding: 0 50px;
        font-size: 30px;
        color: #422E00;
        font-weight: bold;
      }
    }
  }
}
@include utils.setPcContent{
  .custom-diamond-wrapper{
    border-radius: 18PX;
    padding-top: 20PX;

    .title{
      font-size: 24PX;
      line-height: 33PX;
      height: 33px;

      .close{
        @include utils.bgCenter('koa/pop/pop-close.png',20PX,20PX);
        right: 20PX;
      }
    }

    .diamond-input-wrapper{
      margin-top: 20PX;
      // x *
      font-size: 20PX;

      .basic-num{
        font-size: 20PX;
        line-height: 28PX;
      }

      i {
        margin: 0 10PX 0 2PX;
        @include utils.bgCenter( 'koa/diamond/diamond.png', calc(23px * .82), calc(19px * .82));
      }

      input{
        border: 2PX solid #FFFFFF;
        border-radius: 8PX;
        height: 50PX;
        margin-left: 10PX;
        text-indent: 15PX;
        font-size: 20PX;
        width: 200PX;
      }
    }

    .tips{
      font-size: 14PX;
      line-height: 1;
      margin-top: 10PX;
    }

    .checkout-wrapper{
      margin-top: 16PX;
      padding: 8PX 20PX 10PX;

      .left{
        .total-diamond{
          font-size: 20PX;
          line-height: 32PX;

          i {
            @include utils.bgCenter( 'koa/diamond/diamond.png', calc(23px * .75), calc(19px * .75));
          }
        }
        .now-price{
          font-size: 26PX;
          line-height: 32PX;
          margin-top: 2px;

          .symbol{
            font-size: 18PX;
          }
        }
        .origin-price{
          font-size: 18PX;
          margin-top: 6px;
        }
      }
      .right{
        .btn{
          line-height: 50PX;
          border-radius: 25PX;
          padding: 0 56PX;
          font-size: 22PX;
          cursor: pointer;
          font-weight: bold;
        }
      }
    }
  }
}

/* ssv */
.custom-diamond-wrapper.ssv{
  .diamond-input-wrapper{
    i {
      @include utils.bgCenterForSSV('diamond/diamond.png', 24px, 24px);
    }
  }
  .checkout-wrapper{
    .left{
      .total-diamond{
        i {
          @include utils.bgCenterForSSV('diamond/diamond.png', 24px, 24px);
        }
      }
      .now-price{
        color: #FF5E0F;
      }
      .origin-price{
        color: #C74200;
      }
    }
    .right{
      .btn{
        background: #FF5E0F;
        color: white;
      }
    }
  }
}

.custom-diamond-wrapper.ssv2{
  .diamond-input-wrapper{
    i {
      @include utils.bgCenterForSS('diamond/diamond.png', 24px, 24px);
    }
  }
  .checkout-wrapper{
    .left{
      .total-diamond{
        i {
          @include utils.bgCenterForSS('diamond/diamond.png', 24px, 24px);
        }
      }
      .now-price{
        color: #FF5E0F;
      }
      .origin-price{
        color: #C74200;
      }
    }
    .right{
      .btn{
        background: #FF5E0F;
        color: white;
      }
    }
  }
}
</style>
