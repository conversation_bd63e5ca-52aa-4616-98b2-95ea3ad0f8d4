<template>
  <section class="pop-container">
    <slot></slot>
  </section>
</template>

<script>
export default {
  name: 'containerV2'
}
</script>

<style scoped lang="scss">
@use "~@/utils/utils.scss" as utils;

.pop-container{
  position: fixed;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  z-index: 100001;
  display: inline-block;
  @include utils.setPropByBp(
    $m: (width: calc(100vw - 48px), max-width: 750px),
    $p: (width: 600px),
  );
}
</style>
