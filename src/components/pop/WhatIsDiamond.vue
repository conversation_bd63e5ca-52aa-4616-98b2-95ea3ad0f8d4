<template>
  <container :hide-close="true" :option="config" :title="$vt('howToUseDiamond')" class="arrears-reminder-wrapper" :class="[$i18n.locale, this.$store.state.gameinfo.gameCode, $gameName]" id="what-is-diamond-wrapper">
    <div class="description">{{ $vt('whatIsDiamondTitle') }} </div>
    <poster-swiper></poster-swiper>
  </container>
</template>

<script>
import Container from '@/components/pop/container'
import PosterSwiper from '@/components/PosterSwiper'
export default {
  name: 'WhatIsDiamond',
  props: {
    option: Object
  },
  data () {
    return {
      config: {
        confirmBtnTxt: this.$t('got-it')
      }
    }
  },
  components: { PosterSwiper, Container }
}
</script>

<style scoped lang="scss">
@use "~@/utils/utils.scss" as utils;

.arrears-reminder-wrapper {
  box-shadow: 0px 2px 12px 0px rgba(0,0,0,0.25);
  border-radius: 20px;
  background:rgba(56, 56, 56, 1);
  padding-top: 14px;
  padding-bottom: 22px;

  .description {
    font-size: 20px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    line-height: 28px;
    margin-bottom: 12px;
    color: lighten(#999999, 30);
  }

  ::v-deep{
    .content{
      margin-top: 9px;
      padding: 0 44px;
    }
    .footer-wrapper{
      .btn-confirm{
        margin-top: 26px;
      }
    }
  }

  @include utils.setPcContent{
    padding-top: 21PX;

    .description{
      font-size: 16PX;
      line-height: 22PX;
      margin-bottom: 10PX;
      padding-bottom: 21PX;
    }

    ::v-deep{
      .title{
        font-size: 22PX;
        line-height: 30PX;
      }
      .content{
        margin-top: 10PX;
        padding: 0 56PX;
      }
      //.footer-wrapper{
      //  .btn-confirm{
      //    margin-top: 26px;
      //  }
      //}
    }
  }
}

.KOA{
  background: rgb(44, 55, 87);
  border-radius: 0;

  @include utils.setPcContent{
    border: 1PX solid rgb(72, 96, 131);
  }
}

.arrears-reminder-wrapper.dc{
  border: 1px solid #979797;
  border-radius: 0;
  box-shadow: none;

  .description{
    color: #525280;
  }
}
</style>
