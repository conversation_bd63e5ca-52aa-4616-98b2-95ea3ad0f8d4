<template>
  <div class="footer-wrapper">
    <div class="copyright">
      <img class="logo" src="../../../assets/common/icon/fp-logo.png" alt="funplus" style="vertical-align: text-bottom; padding-right: 10px;" v-if="!isMobile">
      <div>
        <p class="links-p">
          <a class="links" href="https://nenglianghe.cn/compliance/privacyAgreement.html" target="_blank">隐私政策</a>
          <a class="links" href="https://nenglianghe.cn/compliance/userAgreement.html" target="_blank">用户协议</a>
          <a class="links" href="https://nenglianghe.cn/compliance/children.html" target="_blank">儿童个人信息保护政策</a>
          <a class="links" href="http://koa.nenglianghe.cn/hook/" target="_blank">防沉迷</a>
        </p>
        <p>网站备案/许可证号: <a href="https://beian.miit.gov.cn/" target="_blank">京ICP备16053236号-11</a></p>
        <p>
          <img class="gongan" src="../../../assets/common/icon-police-gongan.png" alt="">
          <a href="http://www.beian.gov.cn/portal/registerSystemInfo?recordcode=11010502051888" target="_blank">京公网安备 11010502051888号</a>
        </p>
      </div>
    </div>
  </div>
</template>

<script>
import { mapState } from 'vuex'
export default {
  name: 'CommonFooter',
  computed: {
    ...mapState(['isMobile'])
  }
}
</script>

<style scoped lang="scss">
@use "~@/utils/utils.scss" as utils;
.copyright {
  height: 100PX;
  font-size: 13PX;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #CACACA;
  background-color: black;
  font-size: 14PX;

  .logo {
    margin-right: 59PX;
    height: 36PX;
  }

  p {
    margin: 10PX 0;
    display: flex;
    align-items: center;
  }
  .gongan {
    width: 16PX;
    margin-right: 5PX;
  }
  a, a:active, a:visited {
    color: #CACACA;
  }
  a:hover {
    color: #ffffff;
  }
  a.links {
    color: #ffffff;
    font-size: 16PX;
    & + a.links {
      margin-left: 25PX;
    }
    &:hover {
      color: #d5d5d5;
    }
  }
}
@include utils.setMobileContent{
  .footer-wrapper {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
  }
  .copyright {
    padding: 10px 0;
    height: auto;
    margin-top: 40px;
    background-color: rgba(0, 0, 0, 0.2);
    font-size: 16px;
    text-align: center;

    p {
      margin: 10px 0;
      justify-content: center;
    }
    a.links {
      font-size: 20px;
      & + a.links {
        margin-left: 30px;
      }
    }
  }
}
</style>
