<template>
  <common-part :class="$store.state.gameinfo.gameCode" label-font="合计" style="color:#000;">
    <div class="total-price" id="total-price">
      <!-- 有券的情况 -->
      <template v-if="chosenCoupon.FE_INDEX">
        <span class="now-price" :class="{'is-ar-zone': isArZone}">
           <template v-if="chosenCoupon.feType==='first_pay'">{{ currencyUnit }}{{ chosenCoupon.discount_price | formatPrice }}</template>
           <template v-if="chosenCoupon.feType==='discount_coupon'">{{ currencyUnit }}{{ chosenCoupon.discount_price | formatPrice }}</template>
           <template v-if="chosenCoupon.feType==='cash_coupon'">{{ currencyUnit }}{{ chosenCoupon.price | formatPrice }}</template>
           <template v-if="chosenCoupon.feType==='rebate_coupon'">{{ currencyUnit }}{{ chosenCoupon.price | formatPrice }}</template>
        </span>
        <span v-if="chosenCoupon.feType!=='rebate_coupon'" :class="['origin-price', {'is-ar-zone': isArZone}]">{{ currencyUnit }}{{ chosenDiamond.nowPrice || chosenDiamond.price | formatPrice }}</span>
        <div class="off-count-tips">
          <template v-if="chosenCoupon.feType==='first_pay'">{{ `首充 ${chosenCoupon.rateWidthOutPercent} 折` }}</template>
          <template v-if="chosenCoupon.feType==='discount_coupon'">{{ chosenCoupon.rateWidthOutPercent }}折 优惠券</template>
          <template v-if="chosenCoupon.feType==='cash_coupon'"><span :class="{'is-ar-zone': isArZone}">减 {{ currencyUnit }}{{ chosenCoupon.deduct_price }}</span></template>
          <template v-if="chosenCoupon.feType==='rebate_coupon'"><span :class="{'is-ar-zone': isArZone}">送 {{ chosenCoupon.rate }} <i class="diamond-icon"></i></span></template>
        </div>
      </template>
      <!-- 其他游戏无任何活动 -->
      <span v-else :class="{'is-ar-zone': isArZone}">{{ currencyUnit }}{{ chosenDiamond.nowPriceWidthTax || chosenDiamond.price | formatPrice }}</span>
    </div>
  </common-part>
</template>

<script>
import CommonPart from '@/components/common/CommonPart.vue'
import { mapState } from 'vuex'
export default {
  name: 'CheckoutCounter',
  components: { CommonPart },
  data () {
    return {
      defaultDiscountInfo: {}
    }
  },
  computed: {
    ...mapState(['urlParams', 'isArZone', 'currencyUnit']),
    ...mapState('formdata', ['chosenChannel', 'chosenDiamond', 'chosenCoupon']),
    ...mapState('gameinfo', ['defaultDiscount'])
  },
  created () {
    this.$root.$on('setDefaultDiscountInfo', item => {
      this.defaultDiscountInfo = item
    })
  }
}
</script>

<style scoped lang="scss">
@use "~@/utils/utils.scss" as utils;
.total-price {
  font-weight: bold;
  color: #FF5E0F;

  .is-ar-zone {
    display: inline-block;
  }

  .now-price{
    font-size: 26px;
  }

  .origin-price {
    text-decoration: line-through;
    font-weight: bold;
    color: #F5895A;
    font-size: 16PX;
    margin-left: 10PX;
  }

  .off-count-tips {
    display: inline-block;
    background: rgb(255,93,6);
    position: relative;
    font-weight: bold;
    color: #FFFFFF;
    font-size: 10PX;
    top: -19PX;
    left: 5PX;
    padding: 0 6PX;
    border-radius: 4PX;
    height: 18PX;
    line-height: 18PX;

    &:after,&:before{
      content: ' ';
      height: 2PX;
      width: 2PX;
      background-color: black;
      left: 30%;
      position: absolute;
      z-index: 1;
      transform: translateX(-50%);
    }

    &:after{
      top: 0;
      border-bottom-left-radius: 50%;
      border-bottom-right-radius: 50%;
    }

    &:before{
      bottom: 0;
      border-top-left-radius: 50%;
      border-top-right-radius: 50%;
    }

    .diamond-icon {
      @include utils.bgCenter('koa/diamond/diamond.png', 11px, 9px);
      display: inline-block;
      margin-left: 1px;
      position: relative;
    }
  }
}

.KOA{
  .total-price{
    color: #FFFFFF;
    font-size: 26px;
    font-family: PingFangSC-Semibold, PingFang SC;
    font-weight: 600;
    line-height: 34px;
  }

  .origin-price{
    font-size: 16PX;
    color: #C9C9C9;
  }

  .off-count-tips{
    background: rgb(228,184,86);
    color: #735100;
  }
}
</style>
