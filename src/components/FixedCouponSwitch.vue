<script>
import { mapState } from 'vuex'
export default {
  name: 'fixed-coupon-switch',
  computed: {
    ...mapState('formdata', ['isFixedRebateWork'])
  }
}
</script>

<template>
  <div class="coupon-switch-toggle" @click="$root.$emit('toggleFixedCoupon', true)">
    <!-- <span class="toggle-lang">{{ $t('gog-fixed-coupon-switch') }}</span> -->
    <i class="toggle-icon" :class="{ active: isFixedRebateWork }"></i>
    <span class="toggle-lang">{{ $t('gog-fixed-coupon-switch').replace('5 %', '5%') }}</span>
  </div>
</template>

<style scoped lang="scss">
@use "~@/utils/utils.scss" as utils;

.coupon-switch-toggle{
  display: flex;
  align-items: center;
  .toggle-icon{
    display: inline-block;
    margin: 0 10px;
    flex-shrink: 0;
    background-color: #9D9D9D;
    width: 50px;
    height: 30px;
    border-radius: 15px;
    position: relative;
    transition: all .3s;
    &:after{
      content: '';
      height: 25px;
      width: 25px;
      position: absolute;
      left: 2px;
      top: 50%;
      transform: translateY(-50%);
      background-color: white;
      border-radius: 50%;
      transition: all .3s;
    }

    &.active{
      background-color: #34CA5A;
      &:after{
        content: '';
        left: calc(100% - 25px - 4px);
      }
    }
  }
  .toggle-lang{
    font-family: PingFangSC, PingFang SC;
    font-weight: 600;
    font-size: 24px;
    // color: #FFFFFF;
    color: #ff5e00;
    line-height: 33px;
  }
}

@include utils.setPcContent{
  .coupon-switch-toggle{
    // position: absolute;
    // left: 440px;
    // top: 50%;
    // transform: translateY(-50%);
    cursor: pointer;
    .toggle-icon{
      width: 40px;
      height: 24px;
      border-radius: 12px;
      &:after{
        width: 20px;
        height: 20px;
      }

      &.active{
        background-color: #34CA5A;
        &:after{
          content: '';
          left: calc(100% - 20px - 2px);
        }
      }
    }
    .toggle-lang{
      font-size: 18px;
      line-height: 28px;
    }
  }
}
</style>
