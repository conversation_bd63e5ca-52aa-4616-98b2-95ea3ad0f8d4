<template>
  <common-part :label-font="$t('channelChosen')"  :class="[$store.state.gameinfo.gameCode, 'channel-part-wrapper', $gameName, { sdk: $store.state.IS_CHECKOUT_SDK }]" id="channel-part-wrapper">
    <div class="channel-list">
      <template v-if="calChannelList.length">
        <div v-for="(channel, index) in calChannelList"
             @click="toggleStatus(index)"
             :key="channel.FE_CHANNEL_ID"
             :class="[{'channel-chosen__active': channel.FE_CHANNEL_ID === chosenChannel.FE_CHANNEL_ID}]"
             class=" channel-btn">
          <div class="image common-fade-in" v-lazy:backgroundImage='channel.icon_url'></div>

          <!-- initVipInfo不调用就不会展示-->
<!--          <transition name="bonus">-->
<!--            <div class="bonus" v-if="whetherShowVipBonus(channel)">-->
<!--              <span class="bonus-description">-->
<!--                VIP {{ $t('bonus_tips') }} +{{ vip.channelBonus * (chosenDiamond.type === 2 ? chosenDiamond.totalDiamond : chosenDiamond.coin) }}-->
<!--                <i></i>-->
<!--              </span>-->
<!--            </div>-->
<!--          </transition>-->

          <div v-if="channel.subscript || whetherShowVipBonus(channel)" :class="['recommendation', `recommendation-REC`]">
            <span class="blank"></span>
            <span v-if="whetherShowVipBonus(channel)" class="bonus-description">
                VIP +{{ vip.channelBonus * (chosenDiamond.type === 2 ? chosenDiamond.totalDiamond : chosenDiamond.coin) }}
                <i></i>
              </span>
            <span v-else class="txt">{{ $t('recommend-txt') }}</span>
          </div>
        </div>
      </template>
      <div v-else class="empty">{{ $t('nothingHere') }}</div>
    </div>
  </common-part>
</template>

<script>
import CommonPart from '@/components/common/CommonPart.vue'
import { getTokenChannelList, getLastChosenChannel } from '@/server'
import { mapState } from 'vuex'
import { isWx } from '@/utils/utils'

export default {
  name: 'ChannelChoose',
  components: { CommonPart },
  props: {
    activity: {
      type: Object,
      default: () => {}
    }
  },
  data () {
    return {
      channelList: [],

      unwatch: undefined,
      isUserChosen: false
    }
  },
  computed: {
    ...mapState(['urlParams', 'isArZone', 'currencyUnit']),
    ...mapState('formdata', ['chosenChannel', 'chosenDiamond', 'chosenCoupon', 'vip', 'isInit', 'isFirstPayUsed']),
    whetherShowVipBonus () {
      return channelInfo =>
        this.isInit &&
        this.vip.discountSubChannelId.includes(channelInfo.sub_channel_id) &&
        this.vip.channelBonus &&
        this.isFirstPayUsed &&
        !this.chosenCoupon.FE_INDEX
    },
    calChannelList () {
      const someChannelNeedHide = this.$store.getters['riskPolicy/hideSomeChannel'] || []
      if (someChannelNeedHide.length) {
        if (someChannelNeedHide.includes(this.chosenChannel.channel_id)) this.$store.commit('formdata/resetChannel')
        return this.channelList.filter(item => !someChannelNeedHide.includes(item.channel_id))
      }
      return this.channelList
    }
  },
  methods: {
    loadChannelList () {
      const testParam = {
        currency: this.urlParams.cr,
        price: +this.chosenDiamond.price,
        product_id: this.chosenDiamond.product_id
      }

      const chosenCoupon = this.$store.state.formdata.chosenCoupon
      if (chosenCoupon.FE_INDEX) {
        if (chosenCoupon.feType === 'discount_coupon') testParam.price = chosenCoupon.discount_price
        if (chosenCoupon.feType === 'cash_coupon') testParam.price = chosenCoupon.price
        if (chosenCoupon.feType === 'first_pay') testParam.price = chosenCoupon.discount_price
      }
      if (this.$store.getters['formdata/takeEffectDefaultDiscount']) {
        const arr = ['BDT', 'CLP', 'COP', 'CRC', 'DZD', 'HUF', 'IDR', 'INR', 'IQD', 'JPY', 'KES', 'KRW', 'KZT', 'LBP', 'LKR', 'MMK', 'NGN', 'PHP', 'PKR', 'PYG', 'RSD', 'RUB', 'THB', 'TWD', 'TZS', 'VND']
        let finalMoney = testParam.price * 0.95
        if (arr.includes(this.chosenDiamond.currency)) finalMoney = Math.ceil(finalMoney)
        else finalMoney = finalMoney.toFixed(2)
        testParam.price = +finalMoney
      }

      // 兼容自定义的档位
      const { type, nowPrice } = this.chosenDiamond
      if (type === 2) testParam.price = +nowPrice

      this.$loading.show()

      this.$store.commit('formdata/resetChannel')

      getTokenChannelList(testParam)
        .then(({ data, code, message }) => {
          if (code === 0) {
            this.channelList = this.adapterChannel(data)
            setTimeout(() => {
              const { lastChannel } = this.$store.state.userinfo
              if (lastChannel) {
                const lastList = this.calChannelList.filter(item => (item.channel_id === lastChannel.channel_id) && (lastChannel.sub_channel_id === item.sub_channel_id))
                if (lastList && lastList.length) this.$store.commit('formdata/setChosenChannel', lastList[0])

                const lastList2 = this.calChannelList.filter(item => (item.channel_name === lastChannel.channel_name))
                if (lastList2 && lastList2.length) return this.$store.commit('formdata/setChosenChannel', lastList2[0])
              }
            }, 0)
            if (this.$gcbk('switch.enableAnimation', false) && !window.channelFlag && this.$store.state.isPc) {
              window.channelFlag = 1
              this.$nextTick(() => {
                gsap && gsap.from('.channel-list', { height: 0, duration: .4, clearProps: 'height' })
              })
            }

          } else {
            this.$toast.err(this.$t('fetchChannelError'))
          }
        })
        .finally(() => this.$loading.hide())
    },
    adapterChannel (list) {
      if (this.$store.state.gameinfo.isCn) {
        let delSubChannel
        if (isWx) {
          delSubChannel = ['WxpayJSAPI', 'Alipaywap']
        } else if (window.isMobile) {
          delSubChannel = ['WxpayMWEB', 'Alipaywap']
        } else {
          delSubChannel = ['WxpayPcNATIVE', 'Alipaypc']
        }
        list = list.filter(item => {
          if (item.channel_id !== 'wxpay' && item.channel_id !== 'alipay') return true
          return delSubChannel.indexOf(item.channel_name + item.sub_channel_id) > -1
        })
      }

      return list.map(item => {
        item.FE_CHANNEL_ID = `${item.channel_id}__${item.channel_name}`
        return item
      })
    },
    toggleStatus (index) {
      this.isUserChosen = true

      const newChosen = this.calChannelList[index]
      const oldChosen = this.chosenChannel
      if (newChosen.FE_CHANNEL_ID === oldChosen.FE_CHANNEL_ID) return null

      this.$store.commit('formdata/setChosenChannel', this.calChannelList[index])
    },
    initLastChannel () {
      getLastChosenChannel()
        .then(res => {
          const { code, data } = res
          if (code === 0) {
            this.$store.commit('userinfo/saveLastChannelInfo', data)

            // 如果获取到的渠道信息比获取到的 上次选中渠道信息 早，且用户已经选中了新的渠道，就不需要使用上次的信息来初始化本次渠道选择。
            if (this.isUserChosen) return null
            const { lastChannel } = this.$store.state.userinfo
            if (lastChannel) {
              const lastList = this.calChannelList.filter(item => (item.channel_id === lastChannel.channel_id) && (lastChannel.sub_channel_id === item.sub_channel_id))
              if (lastList && lastList.length) return this.$store.commit('formdata/setChosenChannel', lastList[0])
            }
          }
        })
    }
  },
  created () {
    // const objUrl = '$store.state.formdata.chosenDiamond.product_id'
    // this.unwatch = this.$watch(objUrl, value => value && this.loadChannelList(), { immediate: true })
    // 选中优惠券价格变化也需要更新渠道
    this.$root.$on('couponChoose', () => this.loadChannelList())
    // 优惠券初始化完成后需要更新渠道
    this.$root.$on('activityInitEnd', () => this.loadChannelList())

    this.$root.$on('loginEnd', state => {
      // 登录成功，初始化last channel
      if (state === 1) this.initLastChannel()
    })
  },
  beforeDestroy () {
    this.unwatch && this.unwatch()
  }
}
</script>

<style scoped lang="scss">
@use "~@/utils/utils.scss" as utils;
.channel-list {
  width: 100%;
  direction: ltr;
  display: flex;
  flex-wrap: wrap;
  @include utils.setMobileContent{
    width: calc(100% + 18px);
    position: relative;
    left: -5px;
    top: -5px;

    .channel-btn{
      margin: 5px;
    }
  }
  @include utils.setPcContent{
    width: calc(100% + 24PX);
    position: relative;
    //left: -12PX;
  }

  .channel-btn {
    background: #FFFFFF;
    position: relative;
    cursor: pointer;
    vertical-align: top;
    width: 216px;
    height: 80px;
    border-radius: 8px;
    border: 2px solid transparent;
    box-sizing: border-box;

    &.channel-chosen__active {
      border: 2px solid #FF5E0F;

      &:after {
        display: inline-block;
        content: ' ';
        position: absolute;
        right: -2px;
        top: -2px;
        @include utils.bgCenter('common/channel/choose-active.png', 36px, 36px);
      }
    }

    .image {
      width: 194px;
      height: 64px;
      position: absolute;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);
      background-repeat: no-repeat;
      background-position: center center;
      background-size: contain;
      overflow: hidden;
    }

    .recommendation-REC{
      position: absolute;
      top: -17px;
      left: -8px;
      display: flex;
      align-items: flex-end;
      justify-content: space-between;
      z-index: 2;

      @include utils.bgCenter('common/channel/recommend-logo-m.png', 118px, 33px);

      .blank{
        width: 30px;
        height: 100%;
      }

      .txt{
        font-size: 15px;
        font-family: SourceHanSansCN-Medium, SourceHanSansCN;
        font-weight: 500;
        color: #FFFFFF;
        line-height: 21px;
        text-shadow: 0px 1px 0px rgba(0,0,0,0.5);
        flex-grow: 1;
        height: 23px;
        display: flex;
        align-items: center;
        justify-content: center;
        white-space: nowrap;
      }

      .bonus-description{
        font-size: 12px;
        font-family: PingFangSC-Semibold, PingFang SC;
        font-weight: 600;
        color: #FFFFFF;
        line-height: 21px;
        flex-grow: 1;
        height: 23px;
        display: flex;
        align-items: center;
        justify-content: center;
        white-space: nowrap;

        i{
          @include utils.bgCenter('koa/diamond/diamond.png', 10px, 8px);
          display: inline-block;
          margin:0 1px;
        }
      }
    }

    @include utils.setPcContent{
      width: 134PX;
      height: 51PX;
      border-radius: 8PX;
      border: 2PX solid transparent;

      .image{
        width: 120PX;
        height: 40PX;
      }

      $size: 9.8PX;
      &:nth-of-type(n+6){
        margin-top: $size;
      }
      &:not(:nth-of-type(5n + 1)) {
        margin-left: $size;
      }

      &.channel-chosen__active{
        border-width: 2PX;

        &:after{
          display: inline-block;
          content: ' ';
          position: absolute;
          right: -1PX;
          top: -1PX;
          @include utils.bgCenter('common/channel/choose-active.png', 20PX, 20PX);
        }
      }

      .recommendation-REC{
        top: -15px;
        left: -9px;

        @include utils.bgCenter('common/channel/recommend-logo.png', 86px, 25px);

        .blank{
          width: 22px;
        }

        .txt{
          font-size: 12px;
          line-height: 18px;
          text-shadow: 0px 1px 0px rgba(0,0,0,0.5);
          flex-grow: 1;
          height: 18px;
        }

        .bonus-description{
          font-size: 12px;
          line-height: 18px;
          height: 18px;
        }
      }
    }
  }

  .empty {
    font-size: 18px;
    color: #8C8C8C;
    margin-left: 10px;

    @include utils.setPcContent{
      font-size: 14PX;
      margin-left: 12PX;
      line-height: 30PX;
    }
  }
}
.KOA{
  .channel-list{
    .channel-btn{
      border-radius: 0;
      position: relative;
      &.channel-chosen__active{
        &:before {
          height: 10px;
          width: 10px;
          display: inline-block;
          content: ' ';
          background-color: #FF5E0F;
          right: -2px;
          top: -2px;
          position: absolute;
          z-index: 0;
        }
      }
    }
  }
  @include utils.setPcContent{
    .channel-list{
      .channel-btn{
        border-radius: 0;
        position: relative;
        &.channel-chosen__active{
          &:before {
            height: 5PX;
            width: 5PX;
            display: inline-block;
            content: ' ';
            background-color: #FF5E0F;
            z-index: 1;
            right: -2PX;
            top: -2PX;
            position: absolute;
          }
        }
      }
    }
  }
}

.channel-part-wrapper.dc{
  .channel-btn {
    background: #E7EDFF;
    border-radius: 0;

    &.channel-chosen__active{
      border: none;

      &:after{
        @include utils.bgCenterForDC('channel/channel-chosen-bg-m.png', 100%, 100%);
        top: 0;
        left: 0;
        z-index: 0;
      }
    }
  }
}
.channel-part-wrapper.sdk{
  @include utils.setPcContent{
    .channel-list{
      width: 100%;
      .channel-btn{
        zoom: 1.109;
        /*大小放大1.11倍*/

        margin-top: 0;
        margin-left: 0;
        $size: 7.8PX;
        &:nth-of-type(n+7){
          margin-top: $size;
        }
        &:not(:nth-of-type(6n + 1)) {
          margin-left: $size;
        }
      }
      .empty {
        margin-left: 0;
      }
    }
  }
}
</style>
