<template>
  <common-part :class="['wrapper', $gameName ]" id="login-part-wrapper" :label-font="userinfo.isLogin? '':$vt('loginPlaceHolder')">
    <div v-if="userinfo.isLogin" class="login-status__login">
      <div class="avatar lazy" v-lazy:backgroundImage="userinfo.icon"></div>
      <div class="user-info">
        <div class="row-1">
          <div class="name">{{ userinfo.name }}</div>
          <div v-if="!$store.state.isPCSDK" class="toggle click-btn" @click="logout">
            <template v-if="$gameName !== 'ssv'">{{ $t('switch_account') }}</template>
          </div>
        </div>
        <div v-if="isKOA && vip.isInit" class="row-koa">
          <div class="grade">VIP {{ vip.level }}</div>
          <div class="btn-jump">
            <a :href="vipIntroducePageUrl" target="_blank">{{ $t('title_s_vip') }}</a>
          </div>
        </div>
        <div class="row-2">
          <span class="leave">{{ $t('user_level',{0: userinfo.level}) }}</span>
          <span class="server">{{ $t('user_server',{0: userinfo.server}) }}</span>
        </div>
      </div>
    </div>
    <template v-else>
      <div class="login-status__not-login" :class="[{'emphasize': focusEmphasize}]">
        <div class="login-input-wrapper">
          <input type="number" v-model="uid" id="uidInput" :placeholder="focusEmphasize ? '' : $vt('loginPlaceHolder')">
          <i @click.stop="isShowTipsImg = true" @mouseenter.stop="isShowTipsImg = true"></i>
          <img class="tips-img" @click.stop :class="{ active: isShowTipsImg }" :src="$imageLoader('uidTips')" alt="">
        </div>
        <div :class="['btn-login', 'click-btn', 'btn',{ 'btn_disable': !uid}, {'disable': !uid}]" @click="query()">{{ $t('login') }}</div>

        <!--只有koa有vip -->
        <div v-if="isKOA" class="vip-jump-wrapper">
          <a :href="vipIntroducePageUrl" target="_blank">{{ $t('title_s_vip') }}</a>
        </div>
      </div>

      <div v-if="focusEmphasize" class="tip tip-please-input">{{ $vt('loginPlaceHolder') }}</div>
      <div v-if="!focusEmphasize" class="tip tip-uid-get">{{ $t('where-uid-is') }}</div>
    </template>
  </common-part>
</template>

<script>
import CommonPart from '@/components/common/CommonPart.vue'
import { getUserInfoForToken, getAmeDo, getCommonInfo, sendCode } from '@/server'
import { mapState } from 'vuex'
import { logForClickLogin, logForLoginSuccess } from '@/utils/logHelper'
import { StorageUtils } from '@/utils/storageUtils'
import { dealSmDeviceId, decryptAES } from '@/utils/utils'
import { GetAesResult } from '@/utils/uidEncrypto'

export default {
  name: 'LoginModule',
  components: { CommonPart },
  data () {
    return {
      uid: '',
      isShowTipsImg: false,

      focusEmphasize: false
    }
  },
  computed: {
    ...mapState(['userinfo']),
    ...mapState('gameinfo', ['whiteChannel', 'blackChannel', 'greyChannel', 'gameCode', 'isKOA', 'isROMCP']),
    ...mapState('functionSwitch', ['loginValidation']),
    ...mapState(['isPc']),
    ...mapState('formdata', ['vip']),
    vipIntroducePageUrl () {
      return process.env[`VUE_APP_VipIntroducePageUrl_${this.$gameName}`] + '?l=' + this.$i18n.locale
    }
  },
  methods: {
    /* 通用 */
    async query (openid) {
      if (!(this.uid || openid)) return
      const state = this.$store.state
      const params = {
        hideErrToast: true,
        game_project: state.gameProject
      }
      if (openid && typeof openid === 'string') params.openid = openid
      if (this.uid) {
        localStorage.removeItem('openid')
        params.uid = +this.uid

        if (this.$store.state.gameinfo.gameCode === 'KOA') {
          params.ticket = GetAesResult(`${this.uid}|${Math.floor(Date.now() / 1000)}`)
          params.fopenid = GetAesResult(`${Math.ceil(Date.now() / 1000)}|${this.uid}`)
        }
      }

      if (this.uid) logForClickLogin(this.uid)

      try {
        this.$loading.show()
        let { data = {}, code } = await getUserInfoForToken(params)
        this.$loading.hide()
        switch (code) {
          case 0: {
            try {
              const secretKey = this.$gcbk('ids.secretKey')
              if (typeof data === 'string') data = JSON.parse(decryptAES(data, secretKey))
            } catch (e) {
              console.error(`解密失败！${this.uid}`)
            }
            /* 白名单 */
            const whiteChannel = this.whiteChannel || []
            if (whiteChannel.length && !whiteChannel.includes(data.pkg_channel)) {
              this.uid = ''
              return this.$toast.err(this.$t('pkg_not_allow')) // ss 直接提示
              // return this.$root.$emit('showPop', 'IosForbidden', { openid: data.openid }) // koa cn跳走 todo 未兼容
            }
            /* 灰名单 跳转到其他商城 */
            const greyChannel = this.greyChannel || []
            const { pkg_channel: pkgChannel, openid } = data
            if (greyChannel.length) {
              for (const greyItem of greyChannel) {
                const { channel, to } = greyItem
                if (channel.includes(pkgChannel)) {
                  localStorage.removeItem('openid')
                  window.location.href = `${to}?openid=${encodeURIComponent(openid)}`
                  return
                }
              }
            }
            // 黑名单 禁止登录
            const blackChannel = this.blackChannel || []
            if (blackChannel.length && blackChannel.includes(pkgChannel)) return this.$toast.err(this.$t('pkg_not_allow'))

            // if (getPlatform === 'ios') return this.$root.$emit('showPop', 'IosForbidden')
            // if (this.uid) {
            //   localStorage.setItem('openid', data.openid)
            //   window.location.reload()
            //   return
            // }

            // 安全验证
            const isSDK = this.$store.state.IS_CHECKOUT_SDK || this.$store.state.isPCSDK // pc/直购收银台不需要登录验证
            if (this.loginValidation && !isSDK) {
              const isValidation = await this.goValidation(data)
              if (!isValidation) return null
            }
            this.$store.commit('userinfo/setUserInfo', data)

            if (this.isKOA || this.isROMCP) await this.loadVipStatus()
            this.judgePop()

            // 初始化风控规则相关
            this.initRiskPolicy()

            logForLoginSuccess()

            this.$root.$emit('loginSuccess') // 老代码在用
            this.$root.$emit('loginEnd', 1)
            this.uid = ''
            if (this.$gcbk('switch.enableAnimation', false)) {
              setTimeout(
                () => gsap && gsap.from('.login-status__login', { opacity: 0, xPercent: -5, duration: .5 }),
                0
              )
            }
            break
          }
          case 2: {
            this.$tips.show(this.$t('maintenance_tips'))
            break
          }
          case 1000: case 1002: case 4001: case 1007: {
            const msgMap = {
              1000: 'toast_invalid_param',
              1002: 'text_no_role',
              4001: 'toast_black_user',
              1007: 'toast_black_country'
            }
            this.$toast.err(this.$t(msgMap[code]))
            if (code === 1002 && !params.uid) this.logout() // 如果使用uid登录，不需要刷新
            // logOnRoleCheck(msgMap[code].replace(/toast_|text_/g, ''), +this.valueId)
            break
          }
          case 2001: {
            this.$toast.err(this.$t('account_login_fail'))
            setTimeout(() => this.$store.commit('userinfo/logout'), 1000)
            break
          }
          default: {
            this.$toast.err(this.$t('account_login_fail'))
          }
        }
      } catch (error) {
        if (error.message.includes('timeout of')) this.$loading.hide()
      }
    },
    logout () {
      this.$store.commit('userinfo/logout')
    },
    judgePop () {
      if (window.location.pathname.includes('/common/')) return
      const flag = window.localStorage.getItem('isWhatDiamondPop')
      if (!flag) {
        window.localStorage.setItem('isWhatDiamondPop', 'true')
        this.$root.$emit('showWhatIsDiamondPop')
      }
    },
    initRiskPolicy () {
      const emit = this.$root.$emit.bind(this.$root)

      this.$loading.show()
      getCommonInfo()
        .then(res => {
          const { code, data } = res
          if (code === 0) {
            this.$store.commit('riskPolicy/init', { list: data.user_risk_list, emit })
            this.$store.commit('vb/savePrefixChannel', data.reserve_card_channel_gray_ratio)
            this.$store.commit('vb/resetBuiltInCashierStatus', data.switch_global_config)
            this.$store.commit('formdata/switchToggle', data.change_coupon_enable)
            this.$store.commit('functionSwitch/updateFunctionInfo', data)
            this.$root.$emit('updateSpecialDiamond', data.point_card_product)
          }
        })
        .finally(() => {
          this.$loading.hide()
        })
    },
    async goValidation (userinfo) {
      let deviceId = ''
      dealSmDeviceId((id) => {
        deviceId = id
      })

      return new Promise((resolve, reject) => {
        this.$loading.show()
        sendCode({ fp_device_id: deviceId, openid: userinfo.openid })
          .then(res => {
            let { code, data } = res

            if (!data) data = {}
            // todo 注释
            // data.remaining_verification_attempts = 10
            // code = 0

            data.username = userinfo.name
            data.openid = userinfo.openid
            switch (code) {
              case 0: case 5004: {
                data.successCb = () => resolve(true)
                data.failCb = () => resolve(false)
                this.$root.$emit('saveValidation', data)
                break
              }
              case 5011: case 5001: case 5002: {
                resolve(true)
                break
              }
              // case 5003: {
              //   this.$toast.err('今天验证码已达上限')
              //   resolve(false)
              //   break
              // }
              default: {
                resolve(false)
                this.$toast.err(this.$t('login-validation-error-text'))
              }
            }
          })
          .finally(() => this.$loading.hide())
      })
    },

    /* koa相关 */
    judgeAvatarPop () {
      if (this.$store.state.IS_CHECKOUT_SDK) return
      const avatarFlag = StorageUtils.getLocalStorage('isAvatarBonusPop')
      const popDate = `${(new Date().getMonth() + 1)}/${(new Date().getDate())}`
      // 头像框弹框
      if (popDate !== avatarFlag) {
        this.$root.$emit('showPop', 'AvatarBonusPop')
        StorageUtils.setLocalStorage('isAvatarBonusPop', popDate)
      }
    },
    async loadVipStatus () {
      if (this.$store.state.IS_CHECKOUT_SDK_V2) return
      const params = { p0: 'web', p1: 11, p2: 1075 }
      this.$loading.show()
      try {
        const { code, data } = await getAmeDo(params)
        // data = { exp: 0, level: 1, level_up_exp: { level: 2, pay_bonus: 0.02, recharge: 60000, vip_bonus: 0.02 }, pay_bonus: 0.03, recharge: 0, vip_bonus: 0.02 }
        if (code === 0) this.$store.commit('formdata/initVipInfo', data)
      } catch (error) {}
      this.$loading.hide()

      // this.judgeDailyReward() // 判断是否需要弹每日弹窗
      if (this.vip.isNewUser) this.judgeAvatarPop()

      let isFirstToast = false
      this.$root.$on('availableTicketChosen', () => {
        !isFirstToast && this.$toast.success(this.$t('bonus_coupon_mutually_exclusive'))
        isFirstToast = true
      })
      this.$root.$on('TicketPopClose', () => (isFirstToast = false))
    }
  },
  created () {
    const openid = localStorage.getItem('openid')
    if (openid) this.query(openid)
    else {
      this.$root.$emit('loginEnd', 0)
      if (this.isKOA) this.judgeAvatarPop()
      this.judgePop()
    }
    this.$root.$on('BodyClick', () => {
      this.isShowTipsImg = false
    })

    this.$root.$on('ClickPayButNotLogin', () => {
      this.focusEmphasize = true
      const input = document.querySelector('#uidInput')
      // input.scrollIntoView({ behavior: 'smooth' })
      setTimeout(() => {
        input.focus()
      }, 300)

      input.addEventListener('input', () => {
        this.focusEmphasize = false
      })
    })
  }
}
</script>

<style scoped lang="scss">
@use "~@/utils/utils.scss" as utils;
.wrapper{
  margin-top: 0;
  .login-status__not-login{
    display: flex;
    align-items: center;

    .login-input-wrapper{
      position: relative;
      width: 100%;
      @include utils.setMobileContent{
        flex-grow: 1;
        margin-right: 44px;
        border: 1px solid white;
      }

      input{
        background-color: transparent;
        appearance: none;
        -webkit-appearance:none;
        border: none;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #8C8C8C;
        width: 100%;
        @include utils.setMobileContent{
          background-color: rgba(0,0,0,.3);
          font-size: 23px;
          line-height: 62px;
          height: 62px;
          width: 100%;
          padding-right: 66px;
          text-indent: 6px;
          padding-left: 22px;
        }

        &:active, &:focus{
          appearance: none;
          -webkit-appearance:none;
          border: none!important;
          outline: none;
        }
      }
      input::-webkit-outer-spin-button,
      input::-webkit-inner-spin-button {
        -webkit-appearance: none !important;
        margin: 0;
      }
      input[type=number]{-moz-appearance:textfield;}

      i{
        display: inline-block;
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        cursor: pointer;
        z-index: 0;
        transition: all .3s;
        right: 16px;
        @include utils.bgCenter('koa/login/login-tips-btn.png', 31px, 31px);
      }

      .tips-img{
        position: absolute;
        cursor: default;
        top: 0;
        opacity: 0;
        transition: all .3s;
        pointer-events: none;
        z-index: 1000;
        display: inline-block;
        width: 420px;
        right: 25px;
        &.active{
          right: 0;
          opacity: 1;
        }
      }
    }

    .btn-login{
      background: #FF5E0F;
      font-family: PingFangSC-Semibold, PingFang SC;
      font-weight: 600;
      color: #FFFFFF;

      font-size: 26px;
      height: 59px;
      line-height: 59px;
      border-radius: 8px;
      padding: 0 30px;
      white-space: nowrap;
      cursor: pointer;
      //transition: all .3s;

      &.btn_disable{
        opacity: .3;
        cursor: not-allowed;
      }
    }

    &.emphasize{
      .login-input-wrapper{
        border-color: #F83939;

        input{
          color: #F83939;
        }

        i {
          background-image: url("~@/assets/common/login/login-tips-btn-red.png");
        }

        @include utils.setMobileContent{
          border: 1px solid opacify(#F83939, .3);
          box-sizing: border-box;
        }
      }
    }
  }
  .login-status__login{
    display: flex;
    align-items: center;
    .avatar {
      background-color: #F7F7F7;
      border-radius: 50%;
      flex-shrink: 0;
      background-size: cover;
      background-position: center center;
      width: 130px;
      height: 130px;
      margin-right: 31px;

      &[lazy=error], &[lazy=loading]{
        opacity: .4;
      }
      &[lazy=loaded]{
        transition: all .5s;
      }
    }

    .user-info {
      width: 0;
      flex-grow: 1;

      .row-1 {
        display: flex;
        align-items: center;
        justify-content: flex-start;

        .name {
          font-family: PingFangSC-Semibold, PingFang SC;
          font-weight: 600;
          color: #FFFFFF;
          font-size: 32px
        }

        .toggle {
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #FF813C;
          text-decoration: underline;
          cursor: pointer;
          font-size: 22px;
          margin-left: 30px;
        }
      }

      .row-2 {
        font-family: PingFangSC-Semibold, PingFang SC;
        font-weight: 600;
        color: #BFBFBF;
        font-size: 26px;
        margin-top: 16px;

        .server {
          margin-left: 57px
        }
      }
    }
  }
  .tip-uid-get{
    font-size: 16px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #A8A8A8;
    line-height: 22px;
    margin-top: 4px;
  }
  .tip-please-input{
    font-size: 16px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #F83939;
    line-height: 22px;
    letter-spacing: 1px;
    //margin-left: 19px;
    margin-top: 4px;
  }
}
@include utils.setPcContent{
  ::v-deep{
    .label{
      margin-top: 12px;
    }
  }
  .wrapper{
    .login-status__not-login{
      .login-input-wrapper{
        border: 1PX solid white;
        width: 428PX;
        height: 50PX;
        display: flex;
        align-items: center;

        input{
          height: 25px;
          font-size: 18px;
          line-height: 1;
          font-weight: 400;
          text-indent: 10px;
          color: #FFFFFF;
        }

        i{
          right: 14PX;
          @include utils.bgCenter('koa/login/login-tips-btn.png', 25PX, 25PX);
        }

        .tips-img{
          width: 420PX;
          right: 25PX;
        }
      }
      .btn-login{
        margin-left: 34PX;
        padding: 0 38PX;
        width: auto;
        height: 53PX;
        border-radius: 8PX;
        font-size: 26PX;
        line-height: 53PX;
      }
    }
    .login-status__login{
      .avatar{
        width: 96px;
        height: 96px;
        margin-right: 14px
      }

      .user-info{
        margin-left: 37px;
        .row-1{
          .name{
            font-size: 24px
          }
          .toggle{
            font-size: 16px;
            margin-left: 72px
          }
        }
        .row-2{
          font-size: 20px;
          margin-top: 20px;

          .server{
            margin-left: 24px
          }
        }
      }
    }
    .tip-please-input{
      font-size: 16PX;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #F83939;
      line-height: 20PX;
    }
    .tip-uid-get{
      font-size: 14PX;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #A8A8A8;
      line-height: 20PX;
    }
  }
}

/* 各游戏主题 */
.wrapper.koa{
  .login-status__login{
    .avatar{
      &[lazy=error], &[lazy=loading]{
        background-image: url("~@/assets/koa/login/home-default-image.png") !important;
        background-color: transparent;
      }
    }
    .user-info{
      .row-1{
        .name{
          font-size: 28px;
          font-family: PingFangSC-Semibold, PingFang SC;
          font-weight: 600;
          color: #FFFFFF;
          line-height: 30px;
          flex-shrink: 1;
          overflow: hidden;
          text-overflow: ellipsis;
        }

        .toggle{
          background: linear-gradient(180deg, #F6E190 0%, #CBA455 100%);
          text-decoration: none;
          padding: 6px 11px 6px 34px;
          font-size: 20px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #633B00;
          line-height: 28px;
          position: relative;
          margin-left: 40px;
          white-space: nowrap;
          flex-shrink: 0;
          &:after{
            @include utils.bgCenterForKoa('koa/login/toggle-account.png', 16px, 14px);
            content: ' ';
            display: inline-block;
            position: absolute;
            left: 12px;
            width: 14px;
            height: 14px;
            top: 50%;
            transform: translateY(-50%);
          }
        }
      }

      .row-koa{
        display: flex;
        align-items: center;
        margin-top: 8px;
        .grade{
          font-size: 12px;
          font-family: PingFangSC-Semibold, PingFang SC;
          font-weight: 600;
          color: #543000;
          line-height: 29px;
          padding-left: 28px;
          @include utils.bgCenter('koa/vip/vip-grade-bg.png', 67px, 26px);
          display: none;
        }

        .btn-jump{
          font-size: 20px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          line-height: 25px;
          letter-spacing: 1px;
          text-decoration: underline;
          margin-left: 29px;
          margin-left: 0!important;
          cursor: pointer;
          a{
            color: #FEB522;
          }
        }

        &+ .row-2{
          margin-top: 13px;
        }
      }

      .row-2{
        font-size: 20px;
        line-height: 22px;

        .server{
          margin-left: 30px;
        }
      }
    }
  }
  .login-status__not-login{
    .btn-login{
      background: linear-gradient(180deg, #F6E190 0%, #CBA455 100%);
      font-weight: 600;
      color: #633B00;
      border-radius: 0;
    }

    .vip-jump-wrapper{
      font-size: 14PX;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      line-height: 20PX;
      margin-left: 52PX;
      white-space: nowrap;
      text-decoration: underline;
      flex-shrink: 0;

      a{
        color: #FEB522;
      }
    }
  }
  @include utils.setMobileContent{
    .login-status__not-login{
      position: relative;
      .vip-jump-wrapper{
        position: absolute;
        right: 0;
        top: -30px;
        transform: translateY(-50%);
        font-size: 18px;
        line-height: 25px;
      }
    }
  }

  @include utils.setPcContent(){
    .login-status__not-login{
      .login-input-wrapper{
        max-width: 428PX;
      }
      .btn-login{
        margin-left: 16PX;
      }
      .vip{
        font-size: 14PX;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #FEB522;
        line-height: 20PX;
        margin-left: 52PX;
        white-space: nowrap;
        text-decoration: underline;
        flex-shrink: 0;
      }
    }
    .login-status__login{
      .user-info{
        .row-1{
          .name{
            font-size: 20PX;
            font-family: PingFangSC-Semibold, PingFang SC;
            font-weight: 600;
            color: #FFFFFF;
            line-height: 28PX;
          }

          .toggle{
            background: linear-gradient(180deg, #F6E190 0%, #CBA455 100%);
            text-decoration: none;
            padding: 4PX 10PX 3PX 32PX;
            font-size: 18PX;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #633B00;
            line-height: 25PX;
            position: relative;
            margin-left: 60PX;
            &:after{
              @include utils.bgCenterForKoa('koa/login/toggle-account.png', 16PX, 16PX);
              content: ' ';
              display: inline-block;
              width: 16PX;
              height: 16PX;
              position: absolute;
              left: 10PX;
              top: 50%;
              transform: translateY(-50%);
            }
          }
        }
        .row-koa{
          display: flex;
          align-items: center;
          margin-top: 10PX;
          .grade{
            font-size: 12PX;
            font-family: PingFangSC-Semibold, PingFang SC;
            font-weight: 600;
            color: #543000;
            line-height: 27PX;
            padding-left: 28PX;
            height: 26PX;
            width: 67PX;
            @include utils.bgCenterForKoa('koa/vip/vip-grade-bg.png', 67PX, 26PX);
          }

          .btn-jump{
            font-size: 18PX;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #FEB522;
            line-height: 25PX;
            letter-spacing: 1PX;
            text-decoration: underline;
            margin-left: 29PX;
            cursor: pointer;
          }

          &+ .row-2{
            margin-top: 11PX!important;
          }
        }
        .row-2{
          font-size: 16PX;

          .server{
            margin-left: 30PX;
          }
        }
      }
    }
  }
}
.wrapper.aof,
.wrapper.rom{
  @extend .koa;
}
.wrapper.dc{
  $color: rgb(53,121,209);
  .login-status__not-login{
    .login-input-wrapper{
      background: #34353D;
      border: 1px solid #000000;
      margin-right: 0;

      input{
        color: #7E8089;
        font-family: PingFangSC, PingFang SC;
        @extend .dc-stroke;
      }

      i{
        @include utils.bgCenterForDC('/login/login-tips-btn.png', 39px, 39px);
      }
    }
    .btn-login{
      width: 216px;
      margin-left: 12px;
      border-radius: 0;
      background: rgb(53,121,209);
      text-align: center;
      font-family: PingFangSC, PingFang SC;
      @extend .dc-stroke;
      @extend .dc-btn-decoration;
      @include utils.flexCenter;
    }
  }
  .login-status__login{
    .avatar {
      width: 100px;
      height: 100px;
      margin-right: 21px;

      &[lazy=error], &[lazy=loading]{
        background-image: url("~@/assets/dc/pc/logo.png") !important;
        background-color: transparent;
        opacity: .4;
      }
    }
    .user-info {
      .row-1 {
        .name {
          font-size: 28px;
          line-height: 40px;
        }
        .toggle {
          //width: 140px;
          width: auto!important;
          height: 40px;
          background-color: $color;
          font-size: 20px;
          color: #F4FBFF;
          text-decoration: none;
          margin-left: 20px;
          @include utils.flexCenter;
          @extend .dc-stroke;
          @extend .dc-btn-decoration;
          white-space: nowrap;
          padding: 0 10px;

          &:before{
            content: ' ';
            @include utils.bgCenterForDC('login/uid-btn-toggle.png', 16px, 16px);
            display: inline-block;
            margin-right: 4px;
          }
        }
      }
      .row-2 {
        font-size: 20px;
        line-height: 28px;
        margin-top: 12px;
        .server {
          margin-left: 30px;
        }
      }
    }
  }

  @include utils.setPcContent{
    .login-status__not-login{
      .login-input-wrapper{
        background: #34353D;
        border: 1px solid #000000;
        margin-right: 0;

        input{
          color: #7E8089;
        }

        i{
          @include utils.bgCenterForDC('login/login-tips-btn.png', 32px, 32px);
        }
      }
      .btn-login{
        width: 150px;
        height: 50px;
        margin-left: 12px;
        border-radius: 0;
        background: rgb(53,121,209);
        text-align: center;
        font-family: PingFangSC, PingFang SC;
        font-size: 21px;
      }
    }
    .login-status__login{
      .avatar {
        width: 70px;
        height: 70px;
        margin-right: 12px;
      }
      .user-info {
        margin-left: 0;
        .row-1 {
          .name {
            font-size: 20px;
            line-height: 28px;
          }
          .toggle {
            width: 100px;
            height: 30px;
            background-color: $color;
            font-size: 15px;
            color: #F4FBFF;
            text-decoration: none;
            margin-left: 40px;

            &:before{
              @include utils.bgCenterForDC('login/uid-btn-toggle.png', 12px, 12px);
              margin-right: 4px;
            }
          }
        }
        .row-2 {
          font-size: 16px;
          line-height: 22px;
          margin-top: 2px;
          .server {
            margin-left: 20px;
          }
        }
      }
    }
  }
}

.wrapper.ssv{
  .login-status__not-login{
    .login-input-wrapper{
      margin-right: 0;
      height: 60px;
      box-sizing: border-box;

      input{
        color: #8C8C8C;
        font-family: PingFangSC, PingFang SC;
        text-indent: 20px;
        height: 40px;
        font-size: 28px;
        padding: 0;
      }

      i{
        @include utils.bgCenterForSSV('login/login-tips-btn.png', 31px, 31px);
      }
    }
    .btn-login{
      width: 216px;
      height: 60px;
      margin-left: 12px;
      text-align: center;
      font-family: PingFangSC, PingFang SC;
      @include utils.flexCenter;
    }
  }
  .login-status__login{
    .avatar {
      width: 60px;
      height: 60px;
      margin-right: 12px;

      &[lazy=error], &[lazy=loading]{
        background-image: url(https://d15h26rducpaui.cloudfront.net/kg-face/prod/ss_ea/social/cf182b6cdb5f713e29860055aea76fad.png) !important;
        opacity: .4;
      }
    }
    .user-info {
      .row-1 {
        .name {
          font-size: 28px;
          line-height: 40px;
        }
        .toggle {
          width: 48px;
          height: 32px;
          background-color: #FF5E0F;
          border-radius: 16px;
          margin-left: 10px;

          font-size: 20px;
          color: #F4FBFF;
          text-decoration: none;
          @include utils.flexCenter;

          &:before{
            content: ' ';
            @include utils.bgCenterForSSV('login/toggle-btn.png', 22px, 22px);
            display: inline-block;
          }
        }
      }
      .row-2 {
        height: 33px;
        font-size: 24px;
        line-height: 33px;
        margin-top: 0;
        .server {
          margin-left: 30px;
        }
      }
    }
  }

  @include utils.setPcContent{
    .login-status__not-login{
      .login-input-wrapper{
        padding-right: 40px + 10px; // 再留出10px
        width: 428px;
        height: 50px;

        input{
          text-indent: 10px;
          flex-grow: 1;
          font-size: 20px;
          line-height: 28px;
        }

        i{
          @include utils.bgCenterForSSV('login/login-tips-btn.png', 25px, 25px);
        }
      }
      .btn-login{
        padding: 0 38PX;
        width: auto;
        height: 52px;
        border-radius: 4px;
        font-size: 20PX;
        line-height: 52PX;
      }
    }
    .login-status__login{
      .avatar {
        width: 50px;
        height: 50px;
      }
      .user-info {
        margin-left: 0;
        .row-1 {
          .name {
            font-size: 20px;
            line-height: 28px;
          }
          .toggle {
            width: 32px;
            height: 24px;

            &:before{
              @include utils.bgCenterForSSV('login/toggle-btn.png', 16px, 16px);
            }
          }
        }
        .row-2 {
          font-size: 16px;
          line-height: 22px;
          margin-top: 2px;
          .server {
            margin-left: 20px;
          }
        }
      }
    }
  }
}

.standard-login{
  @include utils.setMobileContent{
    ::v-deep{
      .label{
        display: none;
      }
    }
  }
  .login-status__not-login{
    .login-input-wrapper{
      width: 444px;
      height: 60px;
      flex-grow: 0;
      margin-right: 12px;

      input{
        padding-left: 20px;
        text-indent: 0;
        line-height: 58px;
        height: 58px;
      }
    }
    .btn-login{
      width: 216px;
      height: 60px;
      text-align: center;
    }
  }
  .login-status__login{
    .avatar{
      width: 100px;
      height: 100px;
      margin-right: 20px;
    }
    .user-info{
      .row-1{
        .name{
          font-size: 28px;
          line-height: 40px;
        }
        .toggle{

        }
      }
      .row-2{
        font-size: 20px;
        line-height: 28px;
        margin-top: 12px;
        .server {
          margin-left: 30px
        }
      }
    }
  }
  .tip-uid-get,
  .tip-please-input{
    font-size: 18px;
    margin-top: 8px;
  }

  @include utils.setPcContent{
    .login-status__not-login{
      .login-input-wrapper{
        margin-right: 0;
        height: 50px;
        width: 428px;
        input{
          height: 35px;
          padding-left: 12px;
        }
      }
      .btn-login{
        height: 50px;
        width: auto;
        margin-left: 12px;
        padding: 0 10px;
        font-size: 21px;
        min-width: 150px;
        line-height: 50px;
      }
    }
    .login-status__login{
      .avatar{
        height: 50px;
        width: 50px;
        margin-right: 0;
      }
      .user-info{
        margin-left: 12px;
        .row-1{
          .name{
            font-size: 20px;
            line-height: 28px;
          }
          .toggle{
            font-size: 16px;
            margin-left: 22px
          }
        }
        .row-2{
          font-size: 16px;
          margin-top: 1px;

          .server{
            margin-left: 20px
          }
        }
      }
    }
    .tip-uid-get,
    .tip-please-input{
      font-size: 13px;
      margin-top: 4px;
    }
  }
}
.wrapper.ssv2{
  @extend .standard-login;
  .login-status__not-login{
    .login-input-wrapper{
      background: rgba(0,2,31,0.3);
      border-radius: 4px;
      border: 1px solid #FFFFFF;
    }
    .btn-login{
      border-radius: 5px;
    }
  }
}
</style>
