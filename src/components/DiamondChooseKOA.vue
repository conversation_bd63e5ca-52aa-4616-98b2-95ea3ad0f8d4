<!-- ss 大额档位充值 -->
<template>
  <common-part :class="['diamond-part-wrapper', $gameName ]" id="diamond-part-wrapper">
    <template #label>
      <div class="label-wrap">
        <span class="label" @click="$root.$emit('showWhatIsDiamondPop')">
          {{ $t('charge_gear') }} <i class="diamond-icon"></i>
        </span>
        <!--<div v-if="isPc" @click="$root.$emit('showPop', 'WhatIsDiamond')" class="whats-diamond-pop-btn"><i></i>{{ $t('how-to-use-diamond') }}</div>-->
        <div v-if="isMobile" class="charge-construction" @click="$root.$emit('showPop', 'ChargeConstruction')">
          <i></i>{{ $t('construction_title') }}
        </div>
      </div>
    </template>
    <div class="diamond-list-wrapper">
      <div
        :class="['diamond-item',{'diamond-item__active':diamondItem.product_id === chosenDiamond.product_id }, { 'sold-out': diamondItem.soldOut }]"
        v-for="(diamondItem,index) in diamondList"
        :key="diamondItem.product_id"
        @click="toggleStatus(index)">
        <template v-if="diamondItem.type === 2">
          <div class="top">
            <div class="coin-num">{{ lastDiamondCalc.totalDiamond }} <i class="diamond-icon"></i></div>
            <transition name="bonus">
              <div class="bonus" v-if="showBonus">
                <div class="bonus-description">+{{ vip.diamondBonus * lastDiamondCalc.totalDiamond }} <br> <i></i></div>
              </div>
            </transition>
            <div class="diamond-input-wrapper">
              <span class="basic-num">{{ lastDiamondCalc.coin }}</span>
              <i></i>
              x
              <input type="number" v-model="chosenNum" disabled>
              <div class="tips" @click.stop="showPopTips = true">
                <div v-if="showPopTips" class="custom-num-range-tips">{{ $vt('customDiamondLimitTips') }}</div>
              </div>
            </div>
          </div>
          <div class="bottom">
            <div :class="['now',{'is-ar-zone': isArZone}]">{{ diamondState(diamondItem).priceState.nowPrice }}</div>
            <div v-if="diamondState(diamondItem).priceState.originPrice" :class="['origin',{'is-ar-zone': isArZone}]">
              <del>{{ diamondState(diamondItem).priceState.originPrice }}</del>
            </div>
          </div>
        </template>
        <template v-else>
          <div class="top">
            <div class="coin-num">
              {{ diamondItem.coin }} <i class="diamond-icon"></i>
            </div>
            <transition name="bonus">
              <div class="bonus" v-if="showBonus">
                <div class="bonus-description">+{{ vip.diamondBonus * diamondItem.coin }} <br> <i></i></div>
              </div>
            </transition>
            <div :class="['image', 'image_' + index]" ></div>
            <div class="sold-out-mask" v-if="diamondItem.soldOut"></div>
          </div>
          <div class="bottom">
            <div :class="['now',{'is-ar-zone': isArZone}]">{{ diamondState(diamondItem).priceState.nowPrice }}</div>
            <div v-if="diamondState(diamondItem).priceState.originPrice" :class="['origin',{'is-ar-zone': isArZone}]">
              <del>{{ diamondState(diamondItem).priceState.originPrice }}</del>
            </div>
          </div>
        </template>

        <!-- koa 专用未登录 && 首充折扣档位  -  展示首充 20%折扣 -->
        <div v-if="isKOA && !isLogin && firstPayProducts[diamondItem.product_id]" :class="['bonus', 'orange']">
          <p class="discount"><over-size-scale>20%</over-size-scale></p>
          <p class="off">OFF</p>
        </div>
        <!-- koa之后，使用的通用的弹窗样式-->
        <div v-if="diamondState(diamondItem).bonusState.type" class="common-bonus">
          <template v-if="diamondState(diamondItem).bonusState.type === 'rebate'">
            <div class="send">
              <over-size-scale>{{ $t('bonus_tips') }}</over-size-scale>
            </div>
            <div class="num">{{ diamondState(diamondItem).bonusState.coin }}<i class="diamond-icon"></i></div>
          </template>
          <template v-if="diamondState(diamondItem).bonusState.type === 'coupon'">
            <div class="discount">{{ diamondState(diamondItem).bonusState.rate }}</div>
            <div class="off">OFF</div>
          </template>
        </div>
        <!--限购-->
        <div v-if="diamondItem.total_purchase_times || diamondItem.purchased_times" class="forbidden-by-num">
          {{ diamondItem.purchased_times }}/{{ diamondItem.total_purchase_times }}
        </div>
      </div>
      <div class="empty" v-if="!diamondList.length">{{ $t('nothingHere') }}</div>
    </div>
    <extra-diamond v-if="$store.state.country === 'TW' && $gameName === 'ssv'"></extra-diamond>
  </common-part>
</template>

<script>
import CommonPart from '@/components/common/CommonPart.vue'
import { mapState, mapGetters } from 'vuex'
import { priceHelper } from '@/utils/utils'
import { getActivityListForToken, getTokenList, getAmeDo } from '@/server'
import OverSizeScale from './OverSizeScale.vue'
import ExtraDiamond from '@/components/ExtraDiamond.vue'

const minCustomDiamondNum = window.$gcbk('ids.minCustomDiamondNum', 11)
export default {
  name: 'DiamondChooseKOA',
  components: { ExtraDiamond, CommonPart, OverSizeScale },
  data () {
    return {
      diamondList: [],
      canICustom: false,
      lastDiamondItem: {},
      chosenNum: minCustomDiamondNum,
      showPopTips: false
    }
  },
  computed: {
    ...mapState('formdata', ['chosenDiamond', 'chosenCoupon', 'vip', 'isInit', 'isFirstPayUsed', 'chosenCoupon', 'chosenCouponOther', 'firstPayProducts']),
    ...mapState('gameinfo', ['defaultDiscount', 'isKOA']),
    ...mapState('userinfo', ['isLogin']),
    ...mapState(['isPc', 'isMobile']),
    ...mapState(['isArZone']),
    ...mapGetters('formdata', ['takeEffectDefaultDiscount', 'takeEffectDefaultRebate', 'FinalPriceState', 'isDiamondOwn95Off', 'getRebateCoin', 'isDiamondOwnRebate']),
    ...mapState('functionSwitch', ['smallDiamondDoubleDiscount']),
    ...mapState('vb', ['isDiscountUsed']),
    showBonus () {
      return this.isInit && this.isFirstPayUsed && !this.chosenCoupon.FE_INDEX && this.vip.isInit
    },
    lastDiamondCalc () {
      const { level_currency_price: price, currency, coin, tax_rate: taxRate } = this.lastDiamondItem
      // 大额档位暂时没有折扣
      const obj = Object.assign({}, this.lastDiamondItem, {
        totalDiamond: coin * this.chosenNum,
        chosenNum: +this.chosenNum,
        defaultPrice: priceHelper(minCustomDiamondNum * price, currency), // 11倍的原价
        nowPrice: priceHelper(this.chosenNum * price, currency), // 折扣价
        nowPriceWidthTax: priceHelper(price * this.chosenNum * (taxRate || 1), currency) // level_currency_price * 倍数 * tax_rate
      })
      obj.taxation = priceHelper((obj.nowPriceWidthTax - obj.nowPrice), currency)
      return obj
    },
    /* 计算每个档位的状态 */
    diamondState () {
      const chosenDiamond = this.chosenDiamond
      const chosenCoupon = this.chosenCoupon
      const pickChosenCouponInfo = (formatState, priceState, couponInfo) => {
        if (['first_pay', 'discount_coupon', 'fixed_discount_coupon'].includes(chosenCoupon.feType)) {
          formatState.type = 'coupon'
          formatState.rate = chosenCoupon.rate

          priceState.originPrice = couponInfo.level_currency_price
          priceState.nowPrice = couponInfo.no_tax_price
        } else if (chosenCoupon.feType === 'cash_coupon') {
          formatState.type = 'coupon'
          formatState.rate = `${chosenCoupon.deduct_price} ${chosenDiamond.currency_symbol}`
          priceState.originPrice = couponInfo.level_currency_price
          priceState.nowPrice = couponInfo.no_tax_price
        } else if (['rebate_coupon', 'fixed_rebate', 'fixed_dynamic_rebate', 'first_pay_rebate'].includes(chosenCoupon.feType || couponInfo.feType)) { // 两种情况：不是优惠券就是foundation的首冲
          formatState.type = 'rebate'
          formatState.coin = couponInfo.rate || chosenCoupon.rate

          priceState.nowPrice = couponInfo.level_currency_price
        }
      }
      return (diamondItem) => {
        const formatState = {
          type: '', // coupon、rebate
          rate: '',
          coin: 0
        }
        const priceState = { nowPrice: 0, originPrice: 0 }
        // foundation 每个档位都有首冲，优先使用首冲优惠券
        if (this.firstPayProducts[diamondItem.product_id]) {
          const firstPayCoupon = this.firstPayProducts[diamondItem.product_id]
          pickChosenCouponInfo(formatState, priceState, firstPayCoupon)
        } else if ((diamondItem.product_id === chosenDiamond.product_id && chosenCoupon.productId === diamondItem.product_id)) {
          pickChosenCouponInfo(formatState, priceState, chosenCoupon)
        } else if (this.chosenCouponOther[diamondItem.product_id]) {
          const chosenCouponOther = this.chosenCouponOther[diamondItem.product_id]
          pickChosenCouponInfo(formatState, priceState, chosenCouponOther)
        } else if (this.isDiamondOwn95Off(diamondItem)) {
          // case2：固定折扣
          formatState.type = 'coupon'
          formatState.rate = `${this.$store.state.formdata.defaultDiscountInfo.rateWidthOutPercent}%`

          priceState.originPrice = this.isDiamondOwn95Off(diamondItem).level_currency_price
          priceState.nowPrice = this.isDiamondOwn95Off(diamondItem).no_tax_price
        } else if (this.isDiamondOwnRebate(diamondItem)) {
          // case3：固定返钻
          formatState.type = 'rebate'
          formatState.coin = this.getRebateCoin(diamondItem)

          if (this.$gameName === 'dc') {
            const levelCoin = this.$store.state.formdata.defaultRebateDynamicInfoAll[diamondItem.product_id].level_coin
            const discount = this.getRebateCoin(diamondItem) / levelCoin
            formatState.coin = `${Math.floor(discount * 100)}%`
          }

          priceState.nowPrice = this.isDiamondOwnRebate(diamondItem).no_tax_price
        } else {
          // case4：默认情况
          priceState.nowPrice = diamondItem.level_currency_price

          // 如果为大额 直接展示lastDiamondCalc的价格
          if (diamondItem.type === 2) priceState.nowPrice = this.lastDiamondCalc.nowPrice
        }

        // 小档位双倍钻石，修正上面的结果
        const minimumDiamondId = this.$gcbk('ids.minimumDiamondId', '')
        if (this.smallDiamondDoubleDiscount && diamondItem.product_id === minimumDiamondId) {
          const showDoubleDiamond = () => {
            if (this.chosenCoupon.feType === 'first_pay') return false
            return (this.chosenDiamond.product_id === minimumDiamondId && !this.chosenCoupon.FE_INDEX) || // 如果选中第一个档位需要，需要不选中优惠券
              this.chosenDiamond.product_id !== minimumDiamondId // 如果选中
          }
          if (!this.isDiscountUsed && showDoubleDiamond()) {
            formatState.coin = diamondItem.coin
            formatState.type = 'rebate'
          }
        }

        if (priceState.nowPrice) priceState.nowPrice += ` ${diamondItem.currency_symbol}`
        if (priceState.originPrice) priceState.originPrice += ` ${diamondItem.currency_symbol}`

        // console.log({
        //   bonusState: formatState,
        //   priceState
        // })

        if (this.isKOA) formatState.type = undefined // koa登录后不展示优惠券
        return {
          bonusState: formatState,
          priceState
        }
      }
    }
  },
  methods: {
    loadDiamondList () {
      this.$loading.show()
      getTokenList({ store_from: 'storeFromWeb' })
        .then(res => {
          const { data, code } = res
          if (code === 0) {
            this.diamondList = data.sort((a, b) => a.coin - b.coin)
            this.diamondList = this.diamondList.map((item, index) => {
              const { level_currency_price: price, currency, tax_rate: taxRate } = item
              item.nowPriceWidthTax = priceHelper(price * (taxRate || 1), currency)

              item.index = index
              item.soldOut = (item.total_purchase_times || item.purchased_times) && item.total_purchase_times === item.purchased_times
              return item
            })
            this.initCustomParams(this.diamondList)
            for (const [key, value] of Object.entries(data)) {
              if (value.product_id === this.$route.query.pd) return this.$store.commit('formdata/setChosenDiamond', data[key])
            }
            if (data && data.length) {
              const index = this.diamondList.findIndex(item => !item.soldOut)
              this.$store.commit('formdata/setChosenDiamond', data[index || 0])
            }

            if (this.$gameName === 'ssv2') this.initFixCouponByDiamond4(this.diamondList[4])
          } else {
            this.$toast.err(this.$t('fetchChannelError'))
          }
        })
        .finally(() => this.$loading.hide())
    },
    toggleStatus (index) {
      if (this.canICustom && index === this.diamondList.length - 1) {
        if (this.lastDiamondCalc.totalDiamond === this.chosenDiamond.totalDiamond) return null
        this.$root.$emit('showPop', 'CustomDiamond', { diamond: this.lastDiamondCalc, cb: this.toggleCustom.bind(this) })
      } else {
        const target = this.diamondList[index]
        if (target.soldOut) return this.$toast.err(this.$t('product-sold-out'))

        if (this.diamondList[index].product_id === this.chosenDiamond.product_id) return null
        this.$store.commit('formdata/setChosenDiamond', this.diamondList[index])
      }

      this.$store.commit('formdata/resetChannel')
    },
    initCustomParams (diamondList = []) {
      let index = -1
      for (const [key, value] of Object.entries(diamondList)) {
        if (value.type === 2) {
          index = key
          break
        }
      }

      if (index !== -1) {
        const aim = diamondList.splice(index, 1)[0]
        diamondList.push(aim)

        this.lastDiamondItem = aim
        this.canICustom = true
      }
    },
    toggleCustom (chosenNum) {
      if (chosenNum) {
        this.chosenNum = chosenNum
      }
      this.$store.commit('formdata/setChosenDiamond', this.lastDiamondCalc)
    },

    /* 小档位功能 */
    initSmallDiamond () {
      const { p1, p2 } = this.$gcbk('apiParams.smallDiamondDiscount', {})
      const params = { p0: 'web', p1, p2 }
      getAmeDo(params)
        .then(res => {
          const { code, data } = res
          if (code === 0) {
            this.$store.commit('vb/setIsDiscountUsed', data.is_received)
          }
        })

      this.judgeFirstDoubleDiscountStatus()
    },
    // 通知服务端发小档位资格
    judgeFirstDoubleDiscountStatus () {
      const params = { p0: 'web', p1: 10, p2: 1144 }

      params.p2 = 1144
      getAmeDo(params)
        .then(res => {
          const { code } = res
          if (code === 0) {
            setTimeout(() => {
              this.$root.$emit('reloadActivity')
            }, 400)
          }
        })
    },
    smallDiamondEvent () {
      this.$root.$on('couponChosen', () => {
        if (this.isDiscountUsed) return null
        if (this.chosenDiamond.coin === 100) this.$toast.err(this.$t('bonus_coupon_mutually_exclusive'))
      })
      // 如果是第一个档位有首冲双倍、且不是首冲8折
      this.$root.$on('activityInitEnd', () => {
        if (this.isDiscountUsed) return null
        const chosenDiamond = this.chosenDiamond
        const chosenCoupon = this.chosenCoupon

        if (chosenDiamond.coin === 100 && ['discount_coupon', 'cash_coupon'].includes(chosenCoupon.feType)) {
          console.log('reset coupon!')
          this.$store.commit('formdata/resetCouponInfo')
        }
      })
    },

    // ss 特有，前面几个没有固定折扣，通过第四个来判断折扣
    initFixCouponByDiamond4 (diamond) {
      const getRate = value => ((1 - value) * 100).toFixed(0)
      const params = {}
      params.price = diamond.price
      params.product_id = diamond.product_id

      this.$loading.show()
      getActivityListForToken(params)
        .then(res => {
          const { code, data } = res
          if (code === 0) {
            let defaultDiscountOrigin = data.fixed_discount || []
            defaultDiscountOrigin = defaultDiscountOrigin.map((item, index) => ({
              ...item,
              feType: 'fixed_discount_coupon',
              FE_INDEX: `fixed_discount_coupon_${index}`,
              rateWidthOutPercent: getRate(item.discount)
            }))
            if (defaultDiscountOrigin.length) this.$store.commit('formdata/setFixedCouponByProduct4', defaultDiscountOrigin[0])

            let fixedRebateOrigin = data.fixed_rebate || []
            fixedRebateOrigin = fixedRebateOrigin.map((item, index) => ({
              ...item,
              feType: 'fixed_rebate',
              rateWidthOutPercent: getRate(item.discount),
              rate: `${getRate(item.discount)}%`,
              FE_INDEX: `fixed_rebate_${index}`,
              productId: params.product_id
            }))
            if (fixedRebateOrigin.length) this.$store.commit('formdata/setFixedRebateByProduct4', fixedRebateOrigin[0])
          }
        })
        .finally(() => this.$loading.hide())
    }
  },
  created () {
    this.loadDiamondList()
    if (this.smallDiamondDoubleDiscount) this.smallDiamondEvent()

    this.$root.$on('loginEnd', state => {
      if (state === 1) {
        // 登录成功，重新刷新diamond
        this.loadDiamondList()
        if (this.smallDiamondDoubleDiscount) this.initSmallDiamond()
      }
    })
    this.$root.$on('BodyClick', () => {
      this.showPopTips = false
    })
  }
}
</script>

<style scoped lang="scss">
@use "~@/utils/utils.scss" as utils;

.is-ar-zone {
  display: inline-block;
}
.label-wrap {
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;

  .label{
    text-decoration: underline;
    cursor: pointer;
    margin-bottom: 0;
    i{
      @include utils.bgCenter('koa/diamond/diamond.png', calc(23px * 1.3), calc(19px * 1.3));
      display: inline-block;
      margin-left: 6px;
      position: relative;
      top: 2px;
    }

    @include utils.setPcContent{
      display: flex;
      align-items: center;
      justify-content: flex-end;
      i{
        @include utils.bgCenter('koa/diamond/diamond.png', calc(23PX * .8), calc(19PX * .8));
        display: inline-block;
        margin-left: 9px;
        top: 0;
      }
    }
  }

  .charge-construction {
    display: flex;
    align-items: center;
    justify-content: center;
    color: #FEB522;
    line-height: 1;
    cursor: pointer;
    text-decoration: underline;
    font-size: 18px;

    i {
      margin: 0 3px;
      cursor: pointer;
      @include utils.bgCenter('koa/charge-construction-flag.png', 18px, 18px);
      flex-shrink: 0;
    }
  }

  //.whats-diamond-pop-btn{
  //  font-size: 16PX;
  //  font-family: PingFangSC-Regular, PingFang SC;
  //  font-weight: 400;
  //  color: #FEB522;
  //  line-height: 22PX;
  //  position: absolute;
  //  transform: translateY(40PX);
  //  right: 0;
  //  word-break: break-all;
  //  max-width: 100%;
  //  text-decoration: underline;
  //
  //  display: flex;
  //  align-items: flex-start;
  //  justify-content: center;
  //  cursor: pointer;
  //
  //  i {
  //    margin: 2.5PX 3PX 0;
  //    cursor: pointer;
  //    @include utils.bgCenter('koa/charge-construction-flag.png', 17PX, 17PX);
  //    flex-shrink: 0;
  //  }
  //}
}
.diamond-list-wrapper {
  flex-wrap: wrap;
  display: flex;

  .diamond-item {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    cursor: pointer;
    box-sizing: border-box;
    flex-direction: column;
    background-color: white;
    position: relative;
    @include utils.bgCenter('koa/diamond/diamond-bg_mobile.png', 216px, 168px);

    .top{
      height: 118px;
      width: 100%;
      flex-grow: 0;
      position: relative;

      .coin-num {
        margin-top: 9px;
        font-size: 21px;
        font-family: PingFangSC-Semibold, PingFang SC;
        font-weight: 600;
        color: #FFFFFF;
        line-height: 20px;
        text-shadow: 0px 1px 1px rgba(0,0,0,0.7);
        text-align: center;
        display: flex;
        align-items: center;
        justify-content: center;

        i{
          @include utils.bgCenter( 'koa/diamond/diamond.png', calc(23px * 0.8), calc(19px * .8));
          display: inline-block;
          margin: 0 1PX;
        }
      }

      .bonus{
        @include utils.bgCenter('koa/diamond/bonus.png', 56px, 56px);
        position: absolute;
        left: -10px;
        z-index: 5;
        top: -8px;

        .bonus-description{
          font-size: 13px;
          transform: translate(-50%, -50%) rotate(-15deg);
          position: absolute;
          top: 50%;
          left: 50%;
          text-align: center;
          font-family: FZRUIZHK_DA--GBK1-0, FZRUIZHK_DA--GBK1;
          color: #372000;
          text-shadow: 0px 0px 0px #FBF299;
          font-weight: bold;
          transform-origin: center center;

          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;

          i{
            @include utils.bgCenter('koa/diamond/diamond.png', calc(23px * .52), calc(19px * .52));
            display: inline-block;
          }
        }
      }

      .image{
        width: 120px;
        height: 75px;
        bottom: 8px;
        position: absolute;
        left: 50%;
        transform: translateX(-50%);

        @for $i from 1 to 8 {
          &.image_#{$i - 1} {
            background-image: url(~@/assets/koa/diamond/diamond-#{$i}.png);
            background-size: 100% auto;
            background-position: center center;
            background-repeat: no-repeat;
          }
        }
      }

      .diamond-input-wrapper{
        display: flex;
        align-items: center;
        justify-content: center;
        text-align: center;
        width: 100%;
        position: absolute;
        bottom: 30px;

        font-size: 18px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #3D3D3D;

        .basic-num{
          font-size: 20px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #3B2A00;
        }

        i {
          margin: 0 3px 1px 0;
          @include utils.bgCenter( 'koa/diamond/diamond.png', calc(23px * 0.7), calc(19px * .7));
          flex-shrink: 0;
          display: inline-block;
        }

        input{
          border: none;
          height: 34px;
          margin-left: 3px;
          appearance: none;
          -webkit-appearance:none;
          text-indent: 8px;
          font-size: 18px;
          font-family: PingFangSC-Semibold, PingFang SC;
          font-weight: 600;
          color: white;
          background: #422E00;
          display: inline-block;
          width: 80px;
          pointer-events: none;

          &:active, &:focus{
            appearance: none;
            -webkit-appearance:none;
            outline: none;
          }
        }
        input::-webkit-outer-spin-button,
        input::-webkit-inner-spin-button {
          -webkit-appearance: none !important;
          margin: 0;
        }
        input[type=number]{-moz-appearance:textfield;}

        .tips{
          display: none;
        }
      }
    }

    .bottom {
      flex-grow: 1;
      width: 100%;
      text-align: center;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;

      .now {
        font-family: PingFangSC-Semibold, PingFang SC;
        font-weight: 600;
        color: #513900;
        font-size: 20px;
        line-height: 22px;
      }

      .origin {
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #897136;
        font-size: 12px;
        line-height: 14px;
      }
    }

    &.diamond-item__active {
      position: relative;
      box-sizing: border-box;

      &:after{
        display: inline-block;
        content: ' ';
        position: absolute;
        right: 0;
        top: 0;
        z-index: 1;
        @include utils.bgCenter('koa/diamond/choose-active_mobile.png', 100%, 100%);
      }
    }

    .bonus{
      &.orange{
        @include utils.bgCenter('koa/diamond/bonus_orange.png', 60px, 60px);
      }
      position: absolute;
      top: -8px;
      left: -8px;
      z-index: 5;
      color: white;
      text-align: center;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      transition: background-image .5s;

      .discount{
        font-family: PingFangSC-Semibold, PingFang SC;
        font-weight: 600;
        color: #FFFFFF;
        line-height: 1;
        width: 100%;
        align-items: center;
        justify-content: center;
        display:flex;
        font-size: 16px;

        :deep(.inner){
          transform-origin: left bottom;
          text-shadow: 1px 1px 2px #444444;
          font-weight: bold;
          font-size: 16px;
        }
      }

      .off{
        font-size: 10px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #FFFFFF;
        line-height: 1;
        text-shadow: 1px 1px 2px #444444;
      }
    }

    .common-bonus{
      @include utils.bgCenterForDC('diamond/bonus-bg.png', 85px, 85px);
      position: absolute;
      top: 0;
      left: 0;
      color: white;
      z-index: 0;

      .discount{
        transform: translate(-50%, -50%) rotate(-45deg);
        position: absolute;
        top: 24px;
        left: 24px;
        text-shadow: 0px 0px 2px #D5101B;
        font-size: 15px;
        font-weight: bold;
        white-space: nowrap;
      }
      .off{
        transform: translate(-50%, -50%) rotate(-45deg);
        position: absolute;
        top: 34px;
        left: 34px;
        font-size: 13px;
      }

      .send{
        transform: translate(-50%, -50%) rotate(-45deg);
        position: absolute;
        top: 23px;
        left: 23px;
        max-width: 70%;
        font-size: 14px;
        font-weight: bold;
      }
      .num{
        i{
          @include utils.bgCenterForDC('diamond/diamond-icon.png', 15px, 15px);
          margin-left: 2px;
        }

        transform: translate(-50%, -50%) rotate(-45deg);
        position: absolute;
        top: 35px;
        left: 35px;
        display: flex;
        align-items: center;
        font-size: 14px;
        font-weight: bold;
      }
    }

    /* 限制购买数量 */
    .forbidden-by-num{
      position: absolute;
      right: 4px;
      bottom: 45px;
      height: 18px;
      background: #8B755B;
      border-radius: 9px;
      font-size: 14px;
      font-family: PingFangSC-Semibold, PingFang SC;
      font-weight: 600;
      color: #FFFFFF;
      line-height: 18px;
      padding: 0 7px;
    }
    .sold-out-mask{
      @include utils.bgCenterForSSV('diamond/sold-out-mask.png', 98px, 72px);
      position: absolute;
      top: 22px;
      left: 50%;
      transform: translateX(-50%);
      z-index: 11;
      opacity: 1!important;
    }
    &.sold-out{
      cursor: not-allowed;
      * {
        opacity: .7;
      }
    }
  }

  .empty {
    font-size: 18px;
    color: #8C8C8C;
    margin-left: 9px;

    @include utils.setPcContent{
      font-size: 14PX;
      margin-left: -1PX;
      line-height: 30PX;
    }
  }
}
@include utils.setPcContent {
  .diamond-list-wrapper {

    .diamond-item{
      width: 134PX;
      height: 118PX;
      @include utils.bgCenterForKoa( 'koa/diamond/diamond-bg.png', 134PX, 118PX);
      transition: all .3s;

      .bonus{
        &.orange{
          @include utils.bgCenter('koa/diamond/bonus_orange.png', 44px, 44px);
        }
        top: -10px;
        left: -10px;

        .discount{
          font-size: 18px;
        }

        .off{
          font-size: 10px;
        }
      }

      .top{
        height: 80PX;
        width: 100%;
        flex-grow: 0;
        position: relative;

        .coin-num {
          margin-top: 11PX;
          font-size: 14PX;
          font-family: PingFangSC-Semibold, PingFang SC;
          font-weight: 600;
          color: #FFFFFF;
          line-height: 20px;
          text-shadow: 0px 1px 1px rgba(0,0,0,0.7);
          text-align: center;
          display: flex;
          align-items: center;
          justify-content: center;
          position: relative;
          left: 5PX;

          i{
            @include utils.bgCenter( 'koa/diamond/diamond.png', calc(23PX * 0.65), calc(19PX * .65));
            display: inline-block;
            margin: 0 1PX;
          }
        }

        .bonus{
          @include utils.bgCenter('koa/diamond/bonus.png', 48PX, 49PX);
          position: absolute;
          left: -8PX;
          z-index: 5;
          top: -6PX;

          .bonus-description{
            width: auto;
            font-size: 12PX;
            transform: translate(-50%, -50%) rotate(-15deg);
            line-height: 14PX;

            i{
              @include utils.bgCenter('koa/diamond/diamond.png', calc(23PX * .52), calc(19PX * .52));
              display: inline-block;
            }
          }
        }

        .image{
          width: 80PX;
          height: 50PX;
          bottom: 0;
          position: absolute;
          left: 50%;
          transform: translateX(-50%);

          @for $i from 1 to 8 {
            &.image_#{$i - 1} {
              background-image: url(~@/assets/koa/diamond/diamond-#{$i}.png);
              background-size: 93% auto;
              background-position: center center;
              background-repeat: no-repeat;
            }
          }
        }

        .diamond-input-wrapper{
          bottom: 20px;
          font-size: 12px;

          .basic-num{
            font-size: 14px;
          }

          i {
            margin: 0 3px 1px 0;
            @include utils.bgCenter( 'koa/diamond/diamond.png', calc(23px * 0.6), calc(19px * .6));
            flex-shrink: 0;
            display: inline-block;
          }

          input{
            width: 54px;
            height: 22px;
            margin-left: 1px;
            text-indent: 3px;
            font-size: 12px;
          }

          .tips{
            display: inline-block;
            height: 12PX;
            width: 12PX;
            margin-left: 1PX;
            cursor: pointer;
            position: relative;
            z-index: 10;
            @include utils.bgCenter('koa/diamond/custom-diamond-tips-pop-btn.png', 12PX, 12PX);

            .custom-num-range-tips{
              width: 140PX;
              min-height: 60PX;
              background: white;
              position: absolute;
              left: 100%;
              top: -16PX;
              margin-left: 10PX;
              border-radius: 5PX;

              font-size: 14PX;
              font-family: PingFangSC-Regular, PingFang SC;
              font-weight: 400;
              color: #383838;
              line-height: 20PX;
              text-shadow: 0PX 1PX 3PX rgba(0,0,0,0.5);
              padding: 10px;
              display: flex;
              align-items: center;
              justify-content: center;
              box-shadow: 0px 1px 3px 0px rgba(0,0,0,0.5);

              &:after{
                content: '';
                height: 0;
                width: 0;
                border-right: 5PX solid white;
                border-bottom: 5PX solid transparent;
                border-top: 5PX solid transparent;
                position: absolute;
                left: -4PX;
                top: 16PX;
              }
            }
          }
        }
      }

      .bottom {
        flex-grow: 1;
        width: 100%;
        text-align: center;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;

        .now {
          font-family: PingFangSC-Semibold, PingFang SC;
          font-weight: 600;
          color: #513900;
          font-size: 18PX;
          line-height: 20PX;
        }

        .origin {
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #897136;
          font-size: 12PX;
          line-height: 14PX;
          transform: scale(.75);
          transform-origin: center center;
          margin-top: -2PX;
        }
      }

      $size: 8PX;
      &:nth-of-type(n+6) {
        margin-top: $size;
      }
      &:not(:nth-of-type(5n + 1)) {
        margin-left: $size;
      }
      &.diamond-item__active {
        position: relative;
        box-sizing: border-box;

        &:after {
          display: inline-block;
          content: ' ';
          position: absolute;
          left: 0;
          top: 0;
          z-index: 1;
          width: 100%;
          height: 100%;
          box-sizing: border-box;
          @include utils.bgCenter('koa/diamond/choose-active.png', 134PX, 119PX);
        }
      }
      &:hover{
        transform: translateY(-5PX);
      }

      .common-bonus{
        @include utils.bgCenterForDC('diamond/bonus-bg.png', 53px, 53px);

        .discount{
          top: 15px;
          left: 15px;
          font-size: 10px;
          ::v-deep{
            .inner{
              font-size: 13px;
            }
          }
        }
        .off{
          top: 21px;
          left: 21px;
          font-size: 8px;
        }

        .send{
          font-size: 9px;
          top: 15px;
          left: 15px;
        }
        .num{
          font-size: 9px;
          i{
            @include utils.bgCenterForDC('diamond/diamond-icon.png', 11px, 11px);
            margin-left: 1px;
          }
          top: 21px;
          left: 21px;
        }
      }
      .forbidden-by-num{
        right: 3px;
        bottom: 40px;
        height: 14px;
        border-radius: 7px;
        font-size: 12px;
        line-height: 14px;
        padding: 0 5px;
      }
      .sold-out-mask{
        @include utils.bgCenterForSSV('diamond/sold-out-mask.png', 76.44px, 56.1px);
        top: 19px;
      }
    }
  }
}
@include utils.setMobileContent{
  .diamond-list-wrapper{
    width: calc(100% + 12px);
    transform: translate(-5px, -4px);

    .diamond-item{
      margin: 4px;
    }
  }
}

/* dc */
.diamond-part-wrapper.dc{
  .label-wrap {
    .label{
      text-decoration: none;
      @include utils.flexCenter;
      i{
        @include utils.bgCenterForDC('diamond/diamond-icon.png', 35px, 35px);
        display: inline-block;
        margin-left: 6px;
        position: relative;
        top: -3px;
      }
    }

    .charge-construction {
      color: #FFCC66;
      line-height: 1;
      font-size: 18px;

      i {
        @include utils.bgCenterForDC('diamond/charge-construction-flag-diamond.png', 20px, 20px);
      }
    }
  }
  .diamond-list-wrapper{
    .diamond-item{
      @include utils.bgCenterForDC('diamond/diamond-bg_mobile.png', 216px, 168px);
      background-color: transparent;

      .top{
        height: 113px;
        position: relative;
        .image{
          width: 100px;
          height: 100px;
          top: 6px;

          @for $i from 1 to 10 {
            &.image_#{$i - 1} {
              background-image: url(~@/assets/dc/diamond/diamond-#{$i}.png);
              background-size: cover;
            }
          }
        }
        .coin-num {
          height: 29px;
          font-family: SourceHanSansCN, SourceHanSansCN;
          font-weight: bold;
          font-size: 20px;
          color: #9399B7;
          line-height: 29px;
          text-shadow: none;
          width: 100%;
          position: absolute;
          bottom: 0;
          @include utils.flexCenter;
          flex-direction: row-reverse;

          i{
            @include utils.bgCenterForDC( 'diamond/diamond-icon.png', 23px, 23px);
            display: inline-block;
            margin: 0;
          }
        }
      }

      .bottom{
        .now{
          color: #393A3E;
        }
        .origin {
          color: #393A3E;
        }
      }

      &.diamond-item__active{
        &:after{
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          @include utils.bgCenterForDC('diamond/choose-active_mobile.png', 218px, 171px);
        }
      }

      .common-bonus{
        @include utils.bgCenterForDC('diamond/bonus-bg.png', 122px, 22px);
        position: absolute;
        top: 0;
        left: 0;
        color: white;
        z-index: 0;

        @include utils.flexCenter;
        justify-content: flex-start;
        align-items: flex-start;
        padding-left: 8px;
        padding-top: 5.5px;

        .discount{
          font-size: 14px;
          font-weight: bold;
          white-space: nowrap;

          position: static;
          transform: rotate(0) translate(0, 0);
          line-height: 14px;
          height: 14px;
          @include utils.flexCenter;
          @extend .dc-stroke;
        }
        .off{
          font-size: 14px;

          position: static;
          transform: rotate(0) translate(0, 0);
          line-height: 14px;
          height: 14px;
          @include utils.flexCenter;
          margin-left: 3px;
          font-weight: bold;
          @extend .dc-stroke;
        }

        .send{
          font-size: 14px;
          font-weight: bold;

          position: static;
          transform: rotate(0) translate(0, 0);
          line-height: 15px;
          height: 15px;
          @include utils.flexCenter;
          max-width: 45px;
          @extend .dc-stroke;

          ::v-deep{
            .inner{
              padding: 0 3px 0 4px;
            }
          }
        }
        .num{
          i{
            @include utils.bgCenterForDC('diamond/diamond-icon.png', 14px, 14px);
            margin-left: 2px;
            position: relative;
            top: -1.5px;
          }
          font-size: 14px;
          font-weight: bold;

          position: static;
          transform: rotate(0) translate(0, 0);
          line-height: 14px;
          @include utils.flexCenter;
          margin-left: 3px;
          @extend .dc-stroke;
        }
      }
    }
  }

  @include utils.setPcContent{
    .label-wrap {
      .label{
        @include utils.setPcContent{
          justify-content: flex-end;
          i{
            @include utils.bgCenterForDC('diamond/diamond-icon.png', 30px, 30px);
            margin-left: 0;
            top: -2px;
          }
        }
      }
    }
    .diamond-list-wrapper{
      .diamond-item{
        @include utils.bgCenterForDC('diamond/diamond-bg_pc.png', 134px, 118px);
        background-color: transparent;

        .top{
          height: 81px;
          position: relative;
          .image{
            width: 70px;
            height: 70px;
            top: 5px;
          }
          .coin-num {
            height: 29px;
            font-size: 14px;
            color: #9399B7;
            bottom: -3px;

            i{
              @include utils.bgCenterForDC( 'diamond/diamond-icon.png', 16px, 16px);
            }
          }
        }

        &.diamond-item__active{
          &:after{
            @include utils.bgCenterForDC('diamond/choose-active_pc.png', 140px, 122px);
          }
        }

        .common-bonus{
          @include utils.bgCenterForDC('diamond/bonus-bg-pc.png', 84px, 18px);

          padding-left: 4px;
          padding-top: 4.5px;

          .discount{
            font-size: 11px;

            line-height: 11px;
            height: 11px;
          }
          .off{
            font-size: 11px;

            line-height: 11px;
            height: 11px;
            margin-left: 3px;
          }

          .send{
            font-size: 11px;
            font-weight: bold;

            line-height: 11px;
            height: 11px;
            max-width: 35px;
            ::v-deep{
              .inner{
                padding: 0 2px;
              }
            }
          }
          .num{
            i{
              @include utils.bgCenterForDC('diamond/diamond-icon.png', 11px, 11px);
              margin-left: 1px;
              top: -1px;
            }
            font-size: 11px;
            line-height: 11px;
            margin-left: 1px;
          }
        }
      }
    }
  }
}
/* ssv */
.diamond-part-wrapper.ssv{
  .label-wrap {
    .label{
      text-decoration: none;
      @include utils.flexCenter;
      i{
        @include utils.bgCenterForSSV('diamond/diamond.png', 20px, 20px);
        display: inline-block;
        margin-left: 6px;
      }
    }

    .charge-construction {
      color: #FF813C;
      line-height: 1;
      font-size: 18px;

      i {
        @include utils.bgCenterForSSV('pc/charge-construction-flag.png', 22px, 22px);
      }
    }
  }
  .diamond-list-wrapper{
    .diamond-item{
      @include utils.bgCenterForSSV('diamond/diamond-bg.png', 216px, 130px);
      background-color: transparent;

      .top{
        height: 87px;
        position: relative;
        .image{
          width: 104px;
          height: 70px;
          top: 28px;

          @for $i from 0 to 9 {
            &.image_#{$i} {
              background-image: url(~@/assets/ss/diamond/ss-diamond-new-0#{$i}.png);
            }
          }
        }
        .coin-num {
          //height: 29px;
          //font-family: SourceHanSansCN, SourceHanSansCN;
          //font-weight: bold;
          //font-size: 20px;
          //color: #9399B7;
          //line-height: 1;
          //text-shadow: none;
          //width: 100%;
          //position: absolute;
          //bottom: 0;
          //@include utils.flexCenter;
          //flex-direction: row-reverse;
          height: 28px;
          font-size: 20px;
          margin-top: 4px;
          color: #FFFFFF;

          i{
            @include utils.bgCenterForSSV('diamond/diamond.png', 20px, 20px);
            margin-left: 5px;
          }
        }
        .diamond-input-wrapper{
          bottom: 10px;
          color: white;
          .basic-num{
            color: white;
          }
          i {
            margin: 2px 8px 1px 2px;
            @include utils.bgCenterForSSV('diamond/diamond.png', 16px, 16px);
          }
          input{
            border-radius: 2px;
            border: 1px solid #CFCFCF;
            height: 28px;
            color: #3D3D3D;
            background: rgba(239, 239, 239, 0.3);
            margin-left: 7px;
          }
        }
      }

      .bottom{
        .now{
          color: #000000;
        }
        .origin {
          color: #7D561E;
        }
      }

      .common-bonus{
        @include utils.bgCenterForDC('diamond/bonus-bg.png', 75px, 75px);

        .discount{
          top: 21px;
          left: 21px;
        }
        .off{
          top: 31px;
          left: 31px;
        }

        .send{
          top: 20px;
          left: 20px;
          max-width: 70%;
        }
        .num{
          i{
            @include utils.bgCenterForSSV('diamond/diamond.png', 15px, 15px);
            margin-left: 2px;
          }
          top: 30px;
          left: 30px;
        }
      }

      &.diamond-item__active{
        &:after {
          @include utils.bgCenterForSSV('diamond/chosen-diamond-active.png', 216px, 130px);
        }
      }
    }
  }

  @include utils.setPcContent{
    .label-wrap {
      .label{
        @include utils.setPcContent{
          justify-content: flex-end;
          i{
            @include utils.bgCenterForSSV('diamond/diamond.png', 23px, 23px);
            margin-left: 6px;
          }
        }
      }
    }
    .diamond-list-wrapper{
      .diamond-item{
        @include utils.bgCenterForSSV('diamond/pc/diamond-bg.png', 134px, 108px);

        .top{
          height: 69px;
          position: relative;
          .image{
            width: 74px;
            height: 50px;
            top: 26px;
            @for $i from 0 to 9 {
              &.index-#{$i}{
                background-image: url(~@/assets/ss/diamond/pc/ss-diamond-new-0#{$i}.png);
              }
            }
          }
          .coin-num {
            height: 28px;
            font-size: 18px;
            line-height: 28px;
            margin-top: 2px;
            left: 0;
            padding-left: 30px;

            i{
              @include utils.bgCenterForSSV( 'diamond/diamond.png', 18px, 18px);
              margin-left: 1px;
            }
          }
          .diamond-input-wrapper{
            bottom: 2px;
            //color: white;
            //.basic-num{
            //  color: white;
            //}
            font-size: 14px;
            i {
              margin: 0 3PX 0 0;
              @include utils.bgCenterForSSV('diamond/diamond.png', 10px, 10px);
            }
            input{
              border-radius: 2PX;
              border: 1PX solid #CFCFCF;
              height: 22PX;
              margin-left: 2PX;
              text-indent: 4PX;
              font-size: 14PX;
              width: 46PX;
              background: #F2F1F1;
              position: relative;
              z-index: 10;
            }

            .tips{
              display: inline-block;
              height: 12PX;
              width: 12PX;
              margin-left: 1PX;
              cursor: pointer;
              position: relative;
              z-index: 10;
              @include utils.bgCenterForSSV('diamond/custom-diamond-tips-pop-btn.png', 12PX, 12PX);

              .custom-num-range-tips{
                width: 140PX;
                min-height: 60PX;
                background: white;
                position: absolute;
                left: 100%;
                top: -16PX;
                margin-left: 10PX;
                border-radius: 5PX;

                font-size: 14PX;
                font-family: PingFangSC-Regular, PingFang SC;
                font-weight: 400;
                color: #383838;
                line-height: 20PX;
                text-shadow: 0PX 1PX 3PX rgba(0,0,0,0.5);
                padding: 10px;
                display: flex;
                align-items: center;
                justify-content: center;
                box-shadow: 0px 1px 3px 0px rgba(0,0,0,0.5);

                &:after{
                  content: '';
                  height: 0;
                  width: 0;
                  border-right: 5PX solid white;
                  border-bottom: 5PX solid transparent;
                  border-top: 5PX solid transparent;
                  position: absolute;
                  left: -4PX;
                  top: 16PX;
                }
              }
            }
          }
        }

        .bottom{
          height: 38px;

          .origin {
            font-size: 12px;
            line-height: 1;
          }

          .now {
            font-size: 18px;
            line-height: 1.1;
          }
        }

        .common-bonus{
          @include utils.bgCenterForDC('diamond/bonus-bg.png', 53px, 53px);

          .discount{
            top: 14px;
            left: 14px;
          }
          .off{
            top: 21px;
            left: 21px;
          }

          .send{
            top: 14px;
            left: 14px;
            max-width: 70%;
          }
          .num{
            i{
              @include utils.bgCenterForSSV('diamond/diamond.png', 12px, 12px);
              margin-left: 2px;
            }
            top: 20px;
            left: 20px;
          }
        }

        &.diamond-item__active{
          &:after{
            @include utils.bgCenterForSSV('diamond/chosen-diamond-active_pc.png', 134px, 108px);
          }
        }
      }
    }
  }
}
.standard-diamond{
  .label-wrap{
    .label{
      @include utils.flexCenter;
      i{
        top: auto;
      }
    }
  }
}
/* ssv2 */
.diamond-part-wrapper.ssv2{
  @extend .standard-diamond;

  .label-wrap {
    .label{
      i{
        @include utils.bgCenterForSS('diamond/diamond.png', 32px, 32px);
        display: inline-block;
        margin-left: 6px;
      }
    }

    .charge-construction {
      color: #FF813C;
      line-height: 1;
      font-size: 18px;

      i {
        @include utils.bgCenterForSS('pc/charge-construction-flag.png', 22px, 22px);
      }
    }
  }
  .diamond-list-wrapper{
    .diamond-item{
      @include utils.bgCenterForSS('diamond/diamond-bg.png', 216px, 168px);
      background-color: transparent;

      .top{
        height: 116px;
        position: relative;
        .image{
          width: 104px;
          height: 70px;
          top: 45px;

          @for $i from 0 to 9 {
            &.image_#{$i} {
              background-image: url(~@/assets/ss/diamond/ss-diamond-new-0#{$i}.png);
              background-position: center center;
              background-size: 100%;
            }
          }
        }
        .coin-num {
          height: 28px;
          font-size: 20px;
          margin-top: 10px;
          color: #FFFFFF;

          i{
            @include utils.bgCenterForSSV('diamond/diamond.png', 20px, 20px);
            margin-left: 5px;
          }
        }
        .diamond-input-wrapper{
          bottom: 10px;
          color: white;
          .basic-num{
            color: white;
          }
          i {
            margin: 2px 8px 1px 2px;
            @include utils.bgCenterForSSV('diamond/diamond.png', 16px, 16px);
          }
          input{
            border-radius: 2px;
            border: 1px solid #CFCFCF;
            height: 28px;
            color: #3D3D3D;
            background: rgba(239, 239, 239, 0.3);
            margin-left: 7px;
          }
        }
      }

      .bottom{
        @include utils.flexCenter;
        .now{
          color: #000000;
        }
        .origin {
          color: #7D561E;
        }
      }

      .common-bonus{
        @include utils.bgCenterForSS('diamond/bonus_rebate.png', 80px, 80px);
        top: -8px;
        left: -14px;
        z-index: 2;

        .discount{
          transform: translate(-50%, -50%) rotate(-18deg);
          top: 40px;
          left: 46%;
          text-shadow: 0px 0px 1px rgba(50,27,0,0.73);;
          font-size: 23px;
          font-weight: bold;
          letter-spacing: -1px;
        }
        .off{
          transform: translate(-50%, -50%) rotate(-18deg);
          top: 57px;
          left: 54%;
          font-size: 14px;
          font-weight: bold;
        }

        .send{
          transform: translate(-50%, -50%) rotate(-20deg);
          top: 37px;
          left: 43%;
          max-width: 70%;
          font-size: 14px;
          text-shadow: none;
        }
        .num{
          i{
            @include utils.bgCenterForSS('diamond/diamond.png', 16px, 16px);
            margin-left: 2px;
          }

          transform: translate(-50%, -50%) rotate(-20deg);
          top: 51px;
          left: 52%;
          font-size: 18px;
        }
      }

      &.diamond-item__active{
        &:after {
          @include utils.bgCenterForSS('diamond/chosen-diamond-active.png', 216px, 168px);
        }
      }
    }
  }

  @include utils.setPcContent{
    .label-wrap {
      .label{
        @include utils.setPcContent{
          justify-content: flex-end;
          i{
            @include utils.bgCenterForSS('diamond/diamond.png', 23px, 23px);
            margin-left: 6px;
          }
        }
      }
    }
    .diamond-list-wrapper{
      .diamond-item{
        @include utils.bgCenterForSSV('diamond/pc/diamond-bg.png', 134px, 108px);

        .top{
          height: 69px;
          position: relative;
          .image{
            width: 74px;
            height: 50px;
            top: 26px;
            @for $i from 0 to 9 {
              &.index-#{$i}{
                background-image: url(~@/assets/ss/diamond/pc/ss-diamond-new-0#{$i}.png);
              }
            }
          }
          .coin-num {
            height: 28px;
            font-size: 18px;
            line-height: 28px;
            margin-top: 2px;
            left: 0;
            padding-left: 30px;

            i{
              @include utils.bgCenterForSSV( 'diamond/diamond.png', 18px, 18px);
              margin-left: 1px;
            }
          }
          .diamond-input-wrapper{
            bottom: 2px;
            //color: white;
            //.basic-num{
            //  color: white;
            //}
            font-size: 14px;
            i {
              margin: 0 3PX 0 0;
              @include utils.bgCenterForSSV('diamond/diamond.png', 10px, 10px);
            }
            input{
              border-radius: 2PX;
              border: 1PX solid #CFCFCF;
              height: 22PX;
              margin-left: 2PX;
              text-indent: 4PX;
              font-size: 14PX;
              width: 46PX;
              background: #F2F1F1;
              position: relative;
              z-index: 10;
            }

            .tips{
              display: inline-block;
              height: 12PX;
              width: 12PX;
              margin-left: 1PX;
              cursor: pointer;
              position: relative;
              z-index: 10;
              @include utils.bgCenterForSSV('diamond/custom-diamond-tips-pop-btn.png', 12PX, 12PX);

              .custom-num-range-tips{
                width: 140PX;
                min-height: 60PX;
                background: white;
                position: absolute;
                left: 100%;
                top: -16PX;
                margin-left: 10PX;
                border-radius: 5PX;

                font-size: 14PX;
                font-family: PingFangSC-Regular, PingFang SC;
                font-weight: 400;
                color: #383838;
                line-height: 20PX;
                text-shadow: 0PX 1PX 3PX rgba(0,0,0,0.5);
                padding: 10px;
                display: flex;
                align-items: center;
                justify-content: center;
                box-shadow: 0px 1px 3px 0px rgba(0,0,0,0.5);

                &:after{
                  content: '';
                  height: 0;
                  width: 0;
                  border-right: 5PX solid white;
                  border-bottom: 5PX solid transparent;
                  border-top: 5PX solid transparent;
                  position: absolute;
                  left: -4PX;
                  top: 16PX;
                }
              }
            }
          }
        }

        .bottom{
          height: 38px;

          .origin {
            font-size: 12px;
            line-height: 1;
          }

          .now {
            font-size: 18px;
            line-height: 1.1;
          }
        }

        .common-bonus{
          transform: scale(.7);
          top: -8px;
          left: -14px;
          transform-origin: left top;
        }

        &.diamond-item__active{
          &:after{
            @include utils.bgCenterForSSV('diamond/chosen-diamond-active_pc.png', 134px, 108px);
          }
        }
      }
    }
  }
}
</style>
